<?php

namespace App\Jobs;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateOrderMetaDataJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Order Data
     * array $orderId
     */
    protected $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = Order::find($this->orderId);

            $ticket = $order->ticket;

            $event = $order->ticket->event;

            $firstImage = $event->media->first();

            $orderMetaData = [
                'ticket' => [
                    'id' => $ticket->id,
                    'ticket_no' => $ticket->ticket_no,
                    'price' => $ticket->price,
                    'face_value_price' => $ticket->face_value_price,
                    'quantity' => $ticket->quantity,
                    'ticket_type' => [
                        'value' => $ticket->ticket_type,
                        'label' => $ticket->ticket_type?->getLabel(),
                        'color' => $ticket->ticket_type?->getBadgeColour(),
                    ],
                    'ticket_rows' => $ticket->ticket_rows,
                    'ticket_seats' => $ticket->ticket_seats,
                    'description' => $ticket->translations->pluck('description', 'locale'),
                    'sector' => $ticket->sector ? [
                        'id' => $ticket->sector->id,
                        'name' => $ticket->sector->translations->pluck('name', 'locale'),
                    ] : null,
                    'restrictions' => $ticket->restrictions ? $ticket->restrictions->map(function ($restriction) {
                        return [
                            'id' => $restriction->id,
                            'type' => $restriction->type,
                            'name' => $restriction->translations()->pluck('name', 'locale'),
                        ];
                    }) : [],
                ],
                'event' => [
                    'id' => $event->id,
                    'name' => $event->translations->pluck('name', 'locale'),
                    'description' => $event->translations->pluck('description', 'locale'),
                    'image' => $firstImage?->getUrl() ?? '',
                    'image_alt' => $firstImage?->custom_properties['alt'] ?? '',
                    'date' => $event->date,
                    'time' => $event->time,
                    'timezone' => $event->timezone,
                    'category' => [
                        'value' => $event->category->value,
                        'label' => $event->category->getLabel(),
                    ],
                    'country' => $event->country ? [
                        'id' => $event->country->id,
                        'name' => $event->country->translations->pluck('name', 'locale'),
                    ] : null,
                    'home_club' => $event->homeClub ? [
                        'id' => $event->homeClub->id,
                        'name' => $event->homeClub->translations->pluck('name', 'locale'),
                    ] : null,
                    'guest_club' => $event->guestClub ? [
                        'id' => $event->guestClub->id,
                        'name' => $event->guestClub->translations->pluck('name', 'locale'),
                    ] : null,
                    'stadium' => $event->stadium ? [
                        'id' => $event->stadium->id,
                        'name' => $event->stadium->translations->pluck('name', 'locale'),
                        'address_line_1' => $event->stadium->address_line_1,
                        'address_line_2' => $event->stadium->address_line_2,
                        'postcode' => $event->stadium->postcode,
                        'country' => $event->stadium->country?->translations->pluck('name', 'locale'),
                    ] : null,
                    'league' => $event->league ? [
                        'id' => $event->league->id,
                        'name' => $event->league->translations->pluck('name', 'locale'),
                    ] : null,
                    'restrictions' => $event->restrictions ? $event->restrictions->map(function ($restriction) {
                        return [
                            'id' => $restriction->id,
                            'type' => $restriction->type,
                            'name' => $restriction->translations()->pluck('name', 'locale'),
                        ];
                    }) : [],
                ],
            ];

            $order->update(['order_meta_data' => $orderMetaData]);

        } catch (\Exception $e) {
            Log::error('Update order meta data job failed: '.$e->getMessage());
        }
    }
}
