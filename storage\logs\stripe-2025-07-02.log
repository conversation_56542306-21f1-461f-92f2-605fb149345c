[2025-07-02 00:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:00:01] local.INFO: Cron job executed {"time":"2025-07-02 00:00:01"} 
[2025-07-02 00:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:00:02] local.INFO: Cron job executed {"time":"2025-07-02 00:00:02"} 
[2025-07-02 00:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:01:01] local.INFO: Cron job executed {"time":"2025-07-02 00:01:01"} 
[2025-07-02 00:01:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:01:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:01:03] local.INFO: Cron job executed {"time":"2025-07-02 00:01:03"} 
[2025-07-02 00:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:02:01] local.INFO: Cron job executed {"time":"2025-07-02 00:02:01"} 
[2025-07-02 00:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:02:03] local.INFO: Cron job executed {"time":"2025-07-02 00:02:03"} 
[2025-07-02 00:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:03:01] local.INFO: Cron job executed {"time":"2025-07-02 00:03:01"} 
[2025-07-02 00:03:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:03:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:03:03] local.INFO: Cron job executed {"time":"2025-07-02 00:03:03"} 
[2025-07-02 00:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:04:01] local.INFO: Cron job executed {"time":"2025-07-02 00:04:01"} 
[2025-07-02 00:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:04:02] local.INFO: Cron job executed {"time":"2025-07-02 00:04:02"} 
[2025-07-02 00:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:05:01] local.INFO: Cron job executed {"time":"2025-07-02 00:05:01"} 
[2025-07-02 00:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:05:02] local.INFO: Cron job executed {"time":"2025-07-02 00:05:02"} 
[2025-07-02 00:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:06:01] local.INFO: Cron job executed {"time":"2025-07-02 00:06:01"} 
[2025-07-02 00:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:06:02] local.INFO: Cron job executed {"time":"2025-07-02 00:06:02"} 
[2025-07-02 00:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:07:01] local.INFO: Cron job executed {"time":"2025-07-02 00:07:01"} 
[2025-07-02 00:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:07:02] local.INFO: Cron job executed {"time":"2025-07-02 00:07:02"} 
[2025-07-02 00:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:08:01] local.INFO: Cron job executed {"time":"2025-07-02 00:08:01"} 
[2025-07-02 00:08:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:08:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:08:03] local.INFO: Cron job executed {"time":"2025-07-02 00:08:03"} 
[2025-07-02 00:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:09:01] local.INFO: Cron job executed {"time":"2025-07-02 00:09:01"} 
[2025-07-02 00:09:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:09:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:09:03] local.INFO: Cron job executed {"time":"2025-07-02 00:09:03"} 
[2025-07-02 00:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:10:01] local.INFO: Cron job executed {"time":"2025-07-02 00:10:01"} 
[2025-07-02 00:10:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:10:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:10:03] local.INFO: Cron job executed {"time":"2025-07-02 00:10:03"} 
[2025-07-02 00:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:11:01] local.INFO: Cron job executed {"time":"2025-07-02 00:11:01"} 
[2025-07-02 00:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:11:02] local.INFO: Cron job executed {"time":"2025-07-02 00:11:02"} 
[2025-07-02 00:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:12:01] local.INFO: Cron job executed {"time":"2025-07-02 00:12:01"} 
[2025-07-02 00:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:12:02] local.INFO: Cron job executed {"time":"2025-07-02 00:12:02"} 
[2025-07-02 00:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:13:01] local.INFO: Cron job executed {"time":"2025-07-02 00:13:01"} 
[2025-07-02 00:13:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:13:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:13:03] local.INFO: Cron job executed {"time":"2025-07-02 00:13:03"} 
[2025-07-02 00:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:14:01] local.INFO: Cron job executed {"time":"2025-07-02 00:14:01"} 
[2025-07-02 00:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:14:03] local.INFO: Cron job executed {"time":"2025-07-02 00:14:03"} 
[2025-07-02 00:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:15:01] local.INFO: Cron job executed {"time":"2025-07-02 00:15:01"} 
[2025-07-02 00:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:15:03] local.INFO: Cron job executed {"time":"2025-07-02 00:15:03"} 
[2025-07-02 00:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:16:01] local.INFO: Cron job executed {"time":"2025-07-02 00:16:01"} 
[2025-07-02 00:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:16:03] local.INFO: Cron job executed {"time":"2025-07-02 00:16:03"} 
[2025-07-02 00:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:17:01] local.INFO: Cron job executed {"time":"2025-07-02 00:17:01"} 
[2025-07-02 00:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:17:02] local.INFO: Cron job executed {"time":"2025-07-02 00:17:02"} 
[2025-07-02 00:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:18:01] local.INFO: Cron job executed {"time":"2025-07-02 00:18:01"} 
[2025-07-02 00:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:18:02] local.INFO: Cron job executed {"time":"2025-07-02 00:18:02"} 
[2025-07-02 00:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:19:01] local.INFO: Cron job executed {"time":"2025-07-02 00:19:01"} 
[2025-07-02 00:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:19:02] local.INFO: Cron job executed {"time":"2025-07-02 00:19:02"} 
[2025-07-02 00:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:20:01] local.INFO: Cron job executed {"time":"2025-07-02 00:20:01"} 
[2025-07-02 00:20:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:20:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:20:03] local.INFO: Cron job executed {"time":"2025-07-02 00:20:03"} 
[2025-07-02 00:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:21:01] local.INFO: Cron job executed {"time":"2025-07-02 00:21:01"} 
[2025-07-02 00:21:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:21:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:21:03] local.INFO: Cron job executed {"time":"2025-07-02 00:21:03"} 
[2025-07-02 00:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:22:01] local.INFO: Cron job executed {"time":"2025-07-02 00:22:01"} 
[2025-07-02 00:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:22:03] local.INFO: Cron job executed {"time":"2025-07-02 00:22:03"} 
[2025-07-02 00:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:23:01] local.INFO: Cron job executed {"time":"2025-07-02 00:23:01"} 
[2025-07-02 00:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:23:02] local.INFO: Cron job executed {"time":"2025-07-02 00:23:02"} 
[2025-07-02 00:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:24:01] local.INFO: Cron job executed {"time":"2025-07-02 00:24:01"} 
[2025-07-02 00:24:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:24:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:24:03] local.INFO: Cron job executed {"time":"2025-07-02 00:24:03"} 
[2025-07-02 00:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:25:01] local.INFO: Cron job executed {"time":"2025-07-02 00:25:01"} 
[2025-07-02 00:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:25:02] local.INFO: Cron job executed {"time":"2025-07-02 00:25:02"} 
[2025-07-02 00:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:26:01] local.INFO: Cron job executed {"time":"2025-07-02 00:26:01"} 
[2025-07-02 00:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:26:03] local.INFO: Cron job executed {"time":"2025-07-02 00:26:03"} 
[2025-07-02 00:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:27:01] local.INFO: Cron job executed {"time":"2025-07-02 00:27:01"} 
[2025-07-02 00:27:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:27:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:27:03] local.INFO: Cron job executed {"time":"2025-07-02 00:27:03"} 
[2025-07-02 00:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:28:01] local.INFO: Cron job executed {"time":"2025-07-02 00:28:01"} 
[2025-07-02 00:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:28:03] local.INFO: Cron job executed {"time":"2025-07-02 00:28:03"} 
[2025-07-02 00:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:29:01] local.INFO: Cron job executed {"time":"2025-07-02 00:29:01"} 
[2025-07-02 00:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:29:02] local.INFO: Cron job executed {"time":"2025-07-02 00:29:02"} 
[2025-07-02 00:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:30:01] local.INFO: Cron job executed {"time":"2025-07-02 00:30:01"} 
[2025-07-02 00:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:30:02] local.INFO: Cron job executed {"time":"2025-07-02 00:30:02"} 
[2025-07-02 00:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:31:01] local.INFO: Cron job executed {"time":"2025-07-02 00:31:01"} 
[2025-07-02 00:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:31:03] local.INFO: Cron job executed {"time":"2025-07-02 00:31:03"} 
[2025-07-02 00:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:32:01] local.INFO: Cron job executed {"time":"2025-07-02 00:32:01"} 
[2025-07-02 00:32:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:32:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:32:03] local.INFO: Cron job executed {"time":"2025-07-02 00:32:03"} 
[2025-07-02 00:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:33:01] local.INFO: Cron job executed {"time":"2025-07-02 00:33:01"} 
[2025-07-02 00:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:33:02] local.INFO: Cron job executed {"time":"2025-07-02 00:33:02"} 
[2025-07-02 00:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:34:01] local.INFO: Cron job executed {"time":"2025-07-02 00:34:01"} 
[2025-07-02 00:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:34:02] local.INFO: Cron job executed {"time":"2025-07-02 00:34:02"} 
[2025-07-02 00:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:35:01] local.INFO: Cron job executed {"time":"2025-07-02 00:35:01"} 
[2025-07-02 00:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:35:02] local.INFO: Cron job executed {"time":"2025-07-02 00:35:02"} 
[2025-07-02 00:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:36:01] local.INFO: Cron job executed {"time":"2025-07-02 00:36:01"} 
[2025-07-02 00:36:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:36:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:36:03] local.INFO: Cron job executed {"time":"2025-07-02 00:36:03"} 
[2025-07-02 00:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:37:01] local.INFO: Cron job executed {"time":"2025-07-02 00:37:01"} 
[2025-07-02 00:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:37:02] local.INFO: Cron job executed {"time":"2025-07-02 00:37:02"} 
[2025-07-02 00:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:38:01] local.INFO: Cron job executed {"time":"2025-07-02 00:38:01"} 
[2025-07-02 00:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:38:03] local.INFO: Cron job executed {"time":"2025-07-02 00:38:03"} 
[2025-07-02 00:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:39:01] local.INFO: Cron job executed {"time":"2025-07-02 00:39:01"} 
[2025-07-02 00:39:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:39:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:39:03] local.INFO: Cron job executed {"time":"2025-07-02 00:39:03"} 
[2025-07-02 00:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:40:01] local.INFO: Cron job executed {"time":"2025-07-02 00:40:01"} 
[2025-07-02 00:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:40:02] local.INFO: Cron job executed {"time":"2025-07-02 00:40:02"} 
[2025-07-02 00:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:41:01] local.INFO: Cron job executed {"time":"2025-07-02 00:41:01"} 
[2025-07-02 00:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:41:02] local.INFO: Cron job executed {"time":"2025-07-02 00:41:02"} 
[2025-07-02 00:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:42:01] local.INFO: Cron job executed {"time":"2025-07-02 00:42:01"} 
[2025-07-02 00:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:42:02] local.INFO: Cron job executed {"time":"2025-07-02 00:42:02"} 
[2025-07-02 00:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:43:01] local.INFO: Cron job executed {"time":"2025-07-02 00:43:01"} 
[2025-07-02 00:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:43:02] local.INFO: Cron job executed {"time":"2025-07-02 00:43:02"} 
[2025-07-02 00:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:44:01] local.INFO: Cron job executed {"time":"2025-07-02 00:44:01"} 
[2025-07-02 00:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:44:02] local.INFO: Cron job executed {"time":"2025-07-02 00:44:02"} 
[2025-07-02 00:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:45:01] local.INFO: Cron job executed {"time":"2025-07-02 00:45:01"} 
[2025-07-02 00:45:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:45:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:45:03] local.INFO: Cron job executed {"time":"2025-07-02 00:45:03"} 
[2025-07-02 00:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:46:01] local.INFO: Cron job executed {"time":"2025-07-02 00:46:01"} 
[2025-07-02 00:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:46:02] local.INFO: Cron job executed {"time":"2025-07-02 00:46:02"} 
[2025-07-02 00:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:47:01] local.INFO: Cron job executed {"time":"2025-07-02 00:47:01"} 
[2025-07-02 00:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:47:03] local.INFO: Cron job executed {"time":"2025-07-02 00:47:03"} 
[2025-07-02 00:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:48:01] local.INFO: Cron job executed {"time":"2025-07-02 00:48:01"} 
[2025-07-02 00:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:48:02] local.INFO: Cron job executed {"time":"2025-07-02 00:48:02"} 
[2025-07-02 00:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:49:01] local.INFO: Cron job executed {"time":"2025-07-02 00:49:01"} 
[2025-07-02 00:49:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:49:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:49:03] local.INFO: Cron job executed {"time":"2025-07-02 00:49:03"} 
[2025-07-02 00:50:48] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:50:48] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:50:48] local.INFO: Cron job executed {"time":"2025-07-02 00:50:48"} 
[2025-07-02 00:50:51] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:50:51] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:50:51] local.INFO: Cron job executed {"time":"2025-07-02 00:50:51"} 
[2025-07-02 00:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:51:01] local.INFO: Cron job executed {"time":"2025-07-02 00:51:01"} 
[2025-07-02 00:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:51:02] local.INFO: Cron job executed {"time":"2025-07-02 00:51:02"} 
[2025-07-02 00:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:52:01] local.INFO: Cron job executed {"time":"2025-07-02 00:52:01"} 
[2025-07-02 00:52:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:52:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:52:03] local.INFO: Cron job executed {"time":"2025-07-02 00:52:03"} 
[2025-07-02 00:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:53:01] local.INFO: Cron job executed {"time":"2025-07-02 00:53:01"} 
[2025-07-02 00:53:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:53:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:53:03] local.INFO: Cron job executed {"time":"2025-07-02 00:53:03"} 
[2025-07-02 00:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:54:01] local.INFO: Cron job executed {"time":"2025-07-02 00:54:01"} 
[2025-07-02 00:54:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:54:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:54:03] local.INFO: Cron job executed {"time":"2025-07-02 00:54:03"} 
[2025-07-02 00:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:55:01] local.INFO: Cron job executed {"time":"2025-07-02 00:55:01"} 
[2025-07-02 00:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:55:02] local.INFO: Cron job executed {"time":"2025-07-02 00:55:02"} 
[2025-07-02 00:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:56:01] local.INFO: Cron job executed {"time":"2025-07-02 00:56:01"} 
[2025-07-02 00:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:56:03] local.INFO: Cron job executed {"time":"2025-07-02 00:56:03"} 
[2025-07-02 00:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:57:01] local.INFO: Cron job executed {"time":"2025-07-02 00:57:01"} 
[2025-07-02 00:57:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:57:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:57:03] local.INFO: Cron job executed {"time":"2025-07-02 00:57:03"} 
[2025-07-02 00:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:58:01] local.INFO: Cron job executed {"time":"2025-07-02 00:58:01"} 
[2025-07-02 00:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:58:02] local.INFO: Cron job executed {"time":"2025-07-02 00:58:02"} 
[2025-07-02 00:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:59:01] local.INFO: Cron job executed {"time":"2025-07-02 00:59:01"} 
[2025-07-02 00:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 00:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 00:59:02] local.INFO: Cron job executed {"time":"2025-07-02 00:59:02"} 
[2025-07-02 01:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:00:01] local.INFO: Cron job executed {"time":"2025-07-02 01:00:01"} 
[2025-07-02 01:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:00:02] local.INFO: Cron job executed {"time":"2025-07-02 01:00:02"} 
[2025-07-02 01:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:01:01] local.INFO: Cron job executed {"time":"2025-07-02 01:01:01"} 
[2025-07-02 01:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:01:02] local.INFO: Cron job executed {"time":"2025-07-02 01:01:02"} 
[2025-07-02 01:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:02:01] local.INFO: Cron job executed {"time":"2025-07-02 01:02:01"} 
[2025-07-02 01:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:02:02] local.INFO: Cron job executed {"time":"2025-07-02 01:02:02"} 
[2025-07-02 01:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:03:01] local.INFO: Cron job executed {"time":"2025-07-02 01:03:01"} 
[2025-07-02 01:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:03:02] local.INFO: Cron job executed {"time":"2025-07-02 01:03:02"} 
[2025-07-02 01:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:04:01] local.INFO: Cron job executed {"time":"2025-07-02 01:04:01"} 
[2025-07-02 01:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:04:02] local.INFO: Cron job executed {"time":"2025-07-02 01:04:02"} 
[2025-07-02 01:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:05:01] local.INFO: Cron job executed {"time":"2025-07-02 01:05:01"} 
[2025-07-02 01:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:05:02] local.INFO: Cron job executed {"time":"2025-07-02 01:05:02"} 
[2025-07-02 01:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:06:01] local.INFO: Cron job executed {"time":"2025-07-02 01:06:01"} 
[2025-07-02 01:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:06:02] local.INFO: Cron job executed {"time":"2025-07-02 01:06:02"} 
[2025-07-02 01:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:07:01] local.INFO: Cron job executed {"time":"2025-07-02 01:07:01"} 
[2025-07-02 01:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:07:02] local.INFO: Cron job executed {"time":"2025-07-02 01:07:02"} 
[2025-07-02 01:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:08:01] local.INFO: Cron job executed {"time":"2025-07-02 01:08:01"} 
[2025-07-02 01:08:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:08:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:08:03] local.INFO: Cron job executed {"time":"2025-07-02 01:08:03"} 
[2025-07-02 01:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:09:01] local.INFO: Cron job executed {"time":"2025-07-02 01:09:01"} 
[2025-07-02 01:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:09:02] local.INFO: Cron job executed {"time":"2025-07-02 01:09:02"} 
[2025-07-02 01:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:10:01] local.INFO: Cron job executed {"time":"2025-07-02 01:10:01"} 
[2025-07-02 01:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:10:02] local.INFO: Cron job executed {"time":"2025-07-02 01:10:02"} 
[2025-07-02 01:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:11:01] local.INFO: Cron job executed {"time":"2025-07-02 01:11:01"} 
[2025-07-02 01:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:11:02] local.INFO: Cron job executed {"time":"2025-07-02 01:11:02"} 
[2025-07-02 01:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:12:01] local.INFO: Cron job executed {"time":"2025-07-02 01:12:01"} 
[2025-07-02 01:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:12:02] local.INFO: Cron job executed {"time":"2025-07-02 01:12:02"} 
[2025-07-02 01:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:13:01] local.INFO: Cron job executed {"time":"2025-07-02 01:13:01"} 
[2025-07-02 01:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:13:03] local.INFO: Cron job executed {"time":"2025-07-02 01:13:03"} 
[2025-07-02 01:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:14:01] local.INFO: Cron job executed {"time":"2025-07-02 01:14:01"} 
[2025-07-02 01:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:14:02] local.INFO: Cron job executed {"time":"2025-07-02 01:14:02"} 
[2025-07-02 01:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:15:01] local.INFO: Cron job executed {"time":"2025-07-02 01:15:01"} 
[2025-07-02 01:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:15:02] local.INFO: Cron job executed {"time":"2025-07-02 01:15:02"} 
[2025-07-02 01:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:16:01] local.INFO: Cron job executed {"time":"2025-07-02 01:16:01"} 
[2025-07-02 01:16:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:16:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:16:03] local.INFO: Cron job executed {"time":"2025-07-02 01:16:03"} 
[2025-07-02 01:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:17:01] local.INFO: Cron job executed {"time":"2025-07-02 01:17:01"} 
[2025-07-02 01:17:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:17:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:17:03] local.INFO: Cron job executed {"time":"2025-07-02 01:17:03"} 
[2025-07-02 01:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:18:01] local.INFO: Cron job executed {"time":"2025-07-02 01:18:01"} 
[2025-07-02 01:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:18:03] local.INFO: Cron job executed {"time":"2025-07-02 01:18:03"} 
[2025-07-02 01:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:19:01] local.INFO: Cron job executed {"time":"2025-07-02 01:19:01"} 
[2025-07-02 01:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:19:02] local.INFO: Cron job executed {"time":"2025-07-02 01:19:02"} 
[2025-07-02 01:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:20:01] local.INFO: Cron job executed {"time":"2025-07-02 01:20:01"} 
[2025-07-02 01:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:20:02] local.INFO: Cron job executed {"time":"2025-07-02 01:20:02"} 
[2025-07-02 01:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:21:01] local.INFO: Cron job executed {"time":"2025-07-02 01:21:01"} 
[2025-07-02 01:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:21:02] local.INFO: Cron job executed {"time":"2025-07-02 01:21:02"} 
[2025-07-02 01:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:22:01] local.INFO: Cron job executed {"time":"2025-07-02 01:22:01"} 
[2025-07-02 01:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:22:02] local.INFO: Cron job executed {"time":"2025-07-02 01:22:02"} 
[2025-07-02 01:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:23:01] local.INFO: Cron job executed {"time":"2025-07-02 01:23:01"} 
[2025-07-02 01:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:23:02] local.INFO: Cron job executed {"time":"2025-07-02 01:23:02"} 
[2025-07-02 01:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:24:01] local.INFO: Cron job executed {"time":"2025-07-02 01:24:01"} 
[2025-07-02 01:24:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:24:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:24:03] local.INFO: Cron job executed {"time":"2025-07-02 01:24:03"} 
[2025-07-02 01:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:25:01] local.INFO: Cron job executed {"time":"2025-07-02 01:25:01"} 
[2025-07-02 01:25:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:25:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:25:03] local.INFO: Cron job executed {"time":"2025-07-02 01:25:03"} 
[2025-07-02 01:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:26:01] local.INFO: Cron job executed {"time":"2025-07-02 01:26:01"} 
[2025-07-02 01:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:26:03] local.INFO: Cron job executed {"time":"2025-07-02 01:26:03"} 
[2025-07-02 01:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:27:01] local.INFO: Cron job executed {"time":"2025-07-02 01:27:01"} 
[2025-07-02 01:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:27:02] local.INFO: Cron job executed {"time":"2025-07-02 01:27:02"} 
[2025-07-02 01:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:28:01] local.INFO: Cron job executed {"time":"2025-07-02 01:28:01"} 
[2025-07-02 01:28:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:28:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:28:03] local.INFO: Cron job executed {"time":"2025-07-02 01:28:03"} 
[2025-07-02 01:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:29:01] local.INFO: Cron job executed {"time":"2025-07-02 01:29:01"} 
[2025-07-02 01:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:29:02] local.INFO: Cron job executed {"time":"2025-07-02 01:29:02"} 
[2025-07-02 01:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:30:01] local.INFO: Cron job executed {"time":"2025-07-02 01:30:01"} 
[2025-07-02 01:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:30:02] local.INFO: Cron job executed {"time":"2025-07-02 01:30:02"} 
[2025-07-02 01:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:31:01] local.INFO: Cron job executed {"time":"2025-07-02 01:31:01"} 
[2025-07-02 01:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:31:03] local.INFO: Cron job executed {"time":"2025-07-02 01:31:03"} 
[2025-07-02 01:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:32:01] local.INFO: Cron job executed {"time":"2025-07-02 01:32:01"} 
[2025-07-02 01:32:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:32:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:32:03] local.INFO: Cron job executed {"time":"2025-07-02 01:32:03"} 
[2025-07-02 01:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:33:01] local.INFO: Cron job executed {"time":"2025-07-02 01:33:01"} 
[2025-07-02 01:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:33:02] local.INFO: Cron job executed {"time":"2025-07-02 01:33:02"} 
[2025-07-02 01:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:34:01] local.INFO: Cron job executed {"time":"2025-07-02 01:34:01"} 
[2025-07-02 01:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:34:02] local.INFO: Cron job executed {"time":"2025-07-02 01:34:02"} 
[2025-07-02 01:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:35:01] local.INFO: Cron job executed {"time":"2025-07-02 01:35:01"} 
[2025-07-02 01:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:35:02] local.INFO: Cron job executed {"time":"2025-07-02 01:35:02"} 
[2025-07-02 01:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:36:01] local.INFO: Cron job executed {"time":"2025-07-02 01:36:01"} 
[2025-07-02 01:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:36:02] local.INFO: Cron job executed {"time":"2025-07-02 01:36:02"} 
[2025-07-02 01:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:37:01] local.INFO: Cron job executed {"time":"2025-07-02 01:37:01"} 
[2025-07-02 01:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:37:02] local.INFO: Cron job executed {"time":"2025-07-02 01:37:02"} 
[2025-07-02 01:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:38:01] local.INFO: Cron job executed {"time":"2025-07-02 01:38:01"} 
[2025-07-02 01:38:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:38:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:38:03] local.INFO: Cron job executed {"time":"2025-07-02 01:38:03"} 
[2025-07-02 01:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:39:01] local.INFO: Cron job executed {"time":"2025-07-02 01:39:01"} 
[2025-07-02 01:39:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:39:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:39:03] local.INFO: Cron job executed {"time":"2025-07-02 01:39:03"} 
[2025-07-02 01:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:40:01] local.INFO: Cron job executed {"time":"2025-07-02 01:40:01"} 
[2025-07-02 01:40:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:40:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:40:03] local.INFO: Cron job executed {"time":"2025-07-02 01:40:03"} 
[2025-07-02 01:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:41:01] local.INFO: Cron job executed {"time":"2025-07-02 01:41:01"} 
[2025-07-02 01:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:41:02] local.INFO: Cron job executed {"time":"2025-07-02 01:41:02"} 
[2025-07-02 01:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:42:01] local.INFO: Cron job executed {"time":"2025-07-02 01:42:01"} 
[2025-07-02 01:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:42:02] local.INFO: Cron job executed {"time":"2025-07-02 01:42:02"} 
[2025-07-02 01:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:43:01] local.INFO: Cron job executed {"time":"2025-07-02 01:43:01"} 
[2025-07-02 01:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:43:02] local.INFO: Cron job executed {"time":"2025-07-02 01:43:02"} 
[2025-07-02 01:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:44:01] local.INFO: Cron job executed {"time":"2025-07-02 01:44:01"} 
[2025-07-02 01:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:44:02] local.INFO: Cron job executed {"time":"2025-07-02 01:44:02"} 
[2025-07-02 01:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:45:01] local.INFO: Cron job executed {"time":"2025-07-02 01:45:01"} 
[2025-07-02 01:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:45:02] local.INFO: Cron job executed {"time":"2025-07-02 01:45:02"} 
[2025-07-02 01:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:46:01] local.INFO: Cron job executed {"time":"2025-07-02 01:46:01"} 
[2025-07-02 01:46:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:46:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:46:03] local.INFO: Cron job executed {"time":"2025-07-02 01:46:03"} 
[2025-07-02 01:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:47:01] local.INFO: Cron job executed {"time":"2025-07-02 01:47:01"} 
[2025-07-02 01:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:47:03] local.INFO: Cron job executed {"time":"2025-07-02 01:47:03"} 
[2025-07-02 01:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:48:01] local.INFO: Cron job executed {"time":"2025-07-02 01:48:01"} 
[2025-07-02 01:48:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:48:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:48:03] local.INFO: Cron job executed {"time":"2025-07-02 01:48:03"} 
[2025-07-02 01:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:49:01] local.INFO: Cron job executed {"time":"2025-07-02 01:49:01"} 
[2025-07-02 01:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:49:02] local.INFO: Cron job executed {"time":"2025-07-02 01:49:02"} 
[2025-07-02 01:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:50:01] local.INFO: Cron job executed {"time":"2025-07-02 01:50:01"} 
[2025-07-02 01:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:50:02] local.INFO: Cron job executed {"time":"2025-07-02 01:50:02"} 
[2025-07-02 01:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:51:01] local.INFO: Cron job executed {"time":"2025-07-02 01:51:01"} 
[2025-07-02 01:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:51:02] local.INFO: Cron job executed {"time":"2025-07-02 01:51:02"} 
[2025-07-02 01:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:52:01] local.INFO: Cron job executed {"time":"2025-07-02 01:52:01"} 
[2025-07-02 01:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:52:02] local.INFO: Cron job executed {"time":"2025-07-02 01:52:02"} 
[2025-07-02 01:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:53:01] local.INFO: Cron job executed {"time":"2025-07-02 01:53:01"} 
[2025-07-02 01:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:53:02] local.INFO: Cron job executed {"time":"2025-07-02 01:53:02"} 
[2025-07-02 01:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:54:01] local.INFO: Cron job executed {"time":"2025-07-02 01:54:01"} 
[2025-07-02 01:54:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:54:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:54:03] local.INFO: Cron job executed {"time":"2025-07-02 01:54:03"} 
[2025-07-02 01:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:55:01] local.INFO: Cron job executed {"time":"2025-07-02 01:55:01"} 
[2025-07-02 01:55:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:55:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:55:03] local.INFO: Cron job executed {"time":"2025-07-02 01:55:03"} 
[2025-07-02 01:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:56:01] local.INFO: Cron job executed {"time":"2025-07-02 01:56:01"} 
[2025-07-02 01:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:56:03] local.INFO: Cron job executed {"time":"2025-07-02 01:56:03"} 
[2025-07-02 01:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:57:01] local.INFO: Cron job executed {"time":"2025-07-02 01:57:01"} 
[2025-07-02 01:57:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:57:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:57:03] local.INFO: Cron job executed {"time":"2025-07-02 01:57:03"} 
[2025-07-02 01:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:58:01] local.INFO: Cron job executed {"time":"2025-07-02 01:58:01"} 
[2025-07-02 01:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:58:03] local.INFO: Cron job executed {"time":"2025-07-02 01:58:03"} 
[2025-07-02 01:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:59:01] local.INFO: Cron job executed {"time":"2025-07-02 01:59:01"} 
[2025-07-02 01:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 01:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 01:59:02] local.INFO: Cron job executed {"time":"2025-07-02 01:59:02"} 
[2025-07-02 02:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:00:01] local.INFO: Cron job executed {"time":"2025-07-02 02:00:01"} 
[2025-07-02 02:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:00:02] local.INFO: Cron job executed {"time":"2025-07-02 02:00:02"} 
[2025-07-02 02:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:01:01] local.INFO: Cron job executed {"time":"2025-07-02 02:01:01"} 
[2025-07-02 02:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:01:02] local.INFO: Cron job executed {"time":"2025-07-02 02:01:02"} 
[2025-07-02 02:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:02:01] local.INFO: Cron job executed {"time":"2025-07-02 02:02:01"} 
[2025-07-02 02:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:02:03] local.INFO: Cron job executed {"time":"2025-07-02 02:02:03"} 
[2025-07-02 02:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:03:01] local.INFO: Cron job executed {"time":"2025-07-02 02:03:01"} 
[2025-07-02 02:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:03:02] local.INFO: Cron job executed {"time":"2025-07-02 02:03:02"} 
[2025-07-02 02:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:04:01] local.INFO: Cron job executed {"time":"2025-07-02 02:04:01"} 
[2025-07-02 02:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:04:02] local.INFO: Cron job executed {"time":"2025-07-02 02:04:02"} 
[2025-07-02 02:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:05:01] local.INFO: Cron job executed {"time":"2025-07-02 02:05:01"} 
[2025-07-02 02:05:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:05:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:05:03] local.INFO: Cron job executed {"time":"2025-07-02 02:05:03"} 
[2025-07-02 02:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:06:01] local.INFO: Cron job executed {"time":"2025-07-02 02:06:01"} 
[2025-07-02 02:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:06:02] local.INFO: Cron job executed {"time":"2025-07-02 02:06:02"} 
[2025-07-02 02:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:07:01] local.INFO: Cron job executed {"time":"2025-07-02 02:07:01"} 
[2025-07-02 02:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:07:02] local.INFO: Cron job executed {"time":"2025-07-02 02:07:02"} 
[2025-07-02 02:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:08:01] local.INFO: Cron job executed {"time":"2025-07-02 02:08:01"} 
[2025-07-02 02:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:08:02] local.INFO: Cron job executed {"time":"2025-07-02 02:08:02"} 
[2025-07-02 02:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:09:01] local.INFO: Cron job executed {"time":"2025-07-02 02:09:01"} 
[2025-07-02 02:09:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:09:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:09:03] local.INFO: Cron job executed {"time":"2025-07-02 02:09:03"} 
[2025-07-02 02:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:10:01] local.INFO: Cron job executed {"time":"2025-07-02 02:10:01"} 
[2025-07-02 02:10:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:10:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:10:03] local.INFO: Cron job executed {"time":"2025-07-02 02:10:03"} 
[2025-07-02 02:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:11:01] local.INFO: Cron job executed {"time":"2025-07-02 02:11:01"} 
[2025-07-02 02:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:11:03] local.INFO: Cron job executed {"time":"2025-07-02 02:11:03"} 
[2025-07-02 02:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:12:01] local.INFO: Cron job executed {"time":"2025-07-02 02:12:01"} 
[2025-07-02 02:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:12:02] local.INFO: Cron job executed {"time":"2025-07-02 02:12:02"} 
[2025-07-02 02:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:13:01] local.INFO: Cron job executed {"time":"2025-07-02 02:13:01"} 
[2025-07-02 02:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:13:02] local.INFO: Cron job executed {"time":"2025-07-02 02:13:02"} 
[2025-07-02 02:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:14:01] local.INFO: Cron job executed {"time":"2025-07-02 02:14:01"} 
[2025-07-02 02:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:14:02] local.INFO: Cron job executed {"time":"2025-07-02 02:14:02"} 
[2025-07-02 02:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:15:01] local.INFO: Cron job executed {"time":"2025-07-02 02:15:01"} 
[2025-07-02 02:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:15:02] local.INFO: Cron job executed {"time":"2025-07-02 02:15:02"} 
[2025-07-02 02:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:16:01] local.INFO: Cron job executed {"time":"2025-07-02 02:16:01"} 
[2025-07-02 02:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:16:02] local.INFO: Cron job executed {"time":"2025-07-02 02:16:02"} 
[2025-07-02 02:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:17:01] local.INFO: Cron job executed {"time":"2025-07-02 02:17:01"} 
[2025-07-02 02:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:17:02] local.INFO: Cron job executed {"time":"2025-07-02 02:17:02"} 
[2025-07-02 02:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:18:01] local.INFO: Cron job executed {"time":"2025-07-02 02:18:01"} 
[2025-07-02 02:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:18:03] local.INFO: Cron job executed {"time":"2025-07-02 02:18:03"} 
[2025-07-02 02:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:19:01] local.INFO: Cron job executed {"time":"2025-07-02 02:19:01"} 
[2025-07-02 02:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:19:03] local.INFO: Cron job executed {"time":"2025-07-02 02:19:03"} 
[2025-07-02 02:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:20:01] local.INFO: Cron job executed {"time":"2025-07-02 02:20:01"} 
[2025-07-02 02:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:20:02] local.INFO: Cron job executed {"time":"2025-07-02 02:20:02"} 
[2025-07-02 02:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:21:01] local.INFO: Cron job executed {"time":"2025-07-02 02:21:01"} 
[2025-07-02 02:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:21:02] local.INFO: Cron job executed {"time":"2025-07-02 02:21:02"} 
[2025-07-02 02:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:22:01] local.INFO: Cron job executed {"time":"2025-07-02 02:22:01"} 
[2025-07-02 02:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:22:03] local.INFO: Cron job executed {"time":"2025-07-02 02:22:03"} 
[2025-07-02 02:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:23:01] local.INFO: Cron job executed {"time":"2025-07-02 02:23:01"} 
[2025-07-02 02:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:23:03] local.INFO: Cron job executed {"time":"2025-07-02 02:23:03"} 
[2025-07-02 02:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:24:01] local.INFO: Cron job executed {"time":"2025-07-02 02:24:01"} 
[2025-07-02 02:24:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:24:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:24:03] local.INFO: Cron job executed {"time":"2025-07-02 02:24:03"} 
[2025-07-02 02:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:25:01] local.INFO: Cron job executed {"time":"2025-07-02 02:25:01"} 
[2025-07-02 02:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:25:02] local.INFO: Cron job executed {"time":"2025-07-02 02:25:02"} 
[2025-07-02 02:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:26:01] local.INFO: Cron job executed {"time":"2025-07-02 02:26:01"} 
[2025-07-02 02:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:26:02] local.INFO: Cron job executed {"time":"2025-07-02 02:26:02"} 
[2025-07-02 02:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:27:01] local.INFO: Cron job executed {"time":"2025-07-02 02:27:01"} 
[2025-07-02 02:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:27:03] local.INFO: Cron job executed {"time":"2025-07-02 02:27:03"} 
[2025-07-02 02:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:28:01] local.INFO: Cron job executed {"time":"2025-07-02 02:28:01"} 
[2025-07-02 02:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:28:02] local.INFO: Cron job executed {"time":"2025-07-02 02:28:02"} 
[2025-07-02 02:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:29:01] local.INFO: Cron job executed {"time":"2025-07-02 02:29:01"} 
[2025-07-02 02:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:29:02] local.INFO: Cron job executed {"time":"2025-07-02 02:29:02"} 
[2025-07-02 02:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:30:01] local.INFO: Cron job executed {"time":"2025-07-02 02:30:01"} 
[2025-07-02 02:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:30:02] local.INFO: Cron job executed {"time":"2025-07-02 02:30:02"} 
[2025-07-02 02:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:31:01] local.INFO: Cron job executed {"time":"2025-07-02 02:31:01"} 
[2025-07-02 02:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:31:02] local.INFO: Cron job executed {"time":"2025-07-02 02:31:02"} 
[2025-07-02 02:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:32:01] local.INFO: Cron job executed {"time":"2025-07-02 02:32:01"} 
[2025-07-02 02:32:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:32:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:32:03] local.INFO: Cron job executed {"time":"2025-07-02 02:32:03"} 
[2025-07-02 02:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:33:01] local.INFO: Cron job executed {"time":"2025-07-02 02:33:01"} 
[2025-07-02 02:33:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:33:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:33:03] local.INFO: Cron job executed {"time":"2025-07-02 02:33:03"} 
[2025-07-02 02:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:34:01] local.INFO: Cron job executed {"time":"2025-07-02 02:34:01"} 
[2025-07-02 02:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:34:02] local.INFO: Cron job executed {"time":"2025-07-02 02:34:02"} 
[2025-07-02 02:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:35:01] local.INFO: Cron job executed {"time":"2025-07-02 02:35:01"} 
[2025-07-02 02:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:35:02] local.INFO: Cron job executed {"time":"2025-07-02 02:35:02"} 
[2025-07-02 02:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:36:01] local.INFO: Cron job executed {"time":"2025-07-02 02:36:01"} 
[2025-07-02 02:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:36:02] local.INFO: Cron job executed {"time":"2025-07-02 02:36:02"} 
[2025-07-02 02:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:37:01] local.INFO: Cron job executed {"time":"2025-07-02 02:37:01"} 
[2025-07-02 02:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:37:02] local.INFO: Cron job executed {"time":"2025-07-02 02:37:02"} 
[2025-07-02 02:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:38:01] local.INFO: Cron job executed {"time":"2025-07-02 02:38:01"} 
[2025-07-02 02:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:38:02] local.INFO: Cron job executed {"time":"2025-07-02 02:38:02"} 
[2025-07-02 02:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:39:01] local.INFO: Cron job executed {"time":"2025-07-02 02:39:01"} 
[2025-07-02 02:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:39:02] local.INFO: Cron job executed {"time":"2025-07-02 02:39:02"} 
[2025-07-02 02:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:40:01] local.INFO: Cron job executed {"time":"2025-07-02 02:40:01"} 
[2025-07-02 02:40:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:40:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:40:03] local.INFO: Cron job executed {"time":"2025-07-02 02:40:03"} 
[2025-07-02 02:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:41:01] local.INFO: Cron job executed {"time":"2025-07-02 02:41:01"} 
[2025-07-02 02:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:41:02] local.INFO: Cron job executed {"time":"2025-07-02 02:41:02"} 
[2025-07-02 02:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:42:01] local.INFO: Cron job executed {"time":"2025-07-02 02:42:01"} 
[2025-07-02 02:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:42:02] local.INFO: Cron job executed {"time":"2025-07-02 02:42:02"} 
[2025-07-02 02:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:43:01] local.INFO: Cron job executed {"time":"2025-07-02 02:43:01"} 
[2025-07-02 02:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:43:03] local.INFO: Cron job executed {"time":"2025-07-02 02:43:03"} 
[2025-07-02 02:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:44:01] local.INFO: Cron job executed {"time":"2025-07-02 02:44:01"} 
[2025-07-02 02:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:44:02] local.INFO: Cron job executed {"time":"2025-07-02 02:44:02"} 
[2025-07-02 02:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:45:01] local.INFO: Cron job executed {"time":"2025-07-02 02:45:01"} 
[2025-07-02 02:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:45:02] local.INFO: Cron job executed {"time":"2025-07-02 02:45:02"} 
[2025-07-02 02:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:46:01] local.INFO: Cron job executed {"time":"2025-07-02 02:46:01"} 
[2025-07-02 02:46:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:46:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:46:03] local.INFO: Cron job executed {"time":"2025-07-02 02:46:03"} 
[2025-07-02 02:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:47:01] local.INFO: Cron job executed {"time":"2025-07-02 02:47:01"} 
[2025-07-02 02:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:47:03] local.INFO: Cron job executed {"time":"2025-07-02 02:47:03"} 
[2025-07-02 02:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:48:01] local.INFO: Cron job executed {"time":"2025-07-02 02:48:01"} 
[2025-07-02 02:48:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:48:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:48:03] local.INFO: Cron job executed {"time":"2025-07-02 02:48:03"} 
[2025-07-02 02:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:49:01] local.INFO: Cron job executed {"time":"2025-07-02 02:49:01"} 
[2025-07-02 02:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:49:02] local.INFO: Cron job executed {"time":"2025-07-02 02:49:02"} 
[2025-07-02 02:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:50:01] local.INFO: Cron job executed {"time":"2025-07-02 02:50:01"} 
[2025-07-02 02:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:50:02] local.INFO: Cron job executed {"time":"2025-07-02 02:50:02"} 
[2025-07-02 02:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:51:01] local.INFO: Cron job executed {"time":"2025-07-02 02:51:01"} 
[2025-07-02 02:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:51:02] local.INFO: Cron job executed {"time":"2025-07-02 02:51:02"} 
[2025-07-02 02:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:52:01] local.INFO: Cron job executed {"time":"2025-07-02 02:52:01"} 
[2025-07-02 02:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:52:03] local.INFO: Cron job executed {"time":"2025-07-02 02:52:03"} 
[2025-07-02 02:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:53:01] local.INFO: Cron job executed {"time":"2025-07-02 02:53:01"} 
[2025-07-02 02:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:53:02] local.INFO: Cron job executed {"time":"2025-07-02 02:53:02"} 
[2025-07-02 02:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:54:01] local.INFO: Cron job executed {"time":"2025-07-02 02:54:01"} 
[2025-07-02 02:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:54:02] local.INFO: Cron job executed {"time":"2025-07-02 02:54:02"} 
[2025-07-02 02:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:55:01] local.INFO: Cron job executed {"time":"2025-07-02 02:55:01"} 
[2025-07-02 02:55:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:55:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:55:03] local.INFO: Cron job executed {"time":"2025-07-02 02:55:03"} 
[2025-07-02 02:56:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:56:02] local.INFO: Cron job executed {"time":"2025-07-02 02:56:02"} 
[2025-07-02 02:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:56:03] local.INFO: Cron job executed {"time":"2025-07-02 02:56:03"} 
[2025-07-02 02:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:57:01] local.INFO: Cron job executed {"time":"2025-07-02 02:57:01"} 
[2025-07-02 02:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:57:02] local.INFO: Cron job executed {"time":"2025-07-02 02:57:02"} 
[2025-07-02 02:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:58:01] local.INFO: Cron job executed {"time":"2025-07-02 02:58:01"} 
[2025-07-02 02:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:58:03] local.INFO: Cron job executed {"time":"2025-07-02 02:58:03"} 
[2025-07-02 02:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:59:01] local.INFO: Cron job executed {"time":"2025-07-02 02:59:01"} 
[2025-07-02 02:59:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 02:59:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 02:59:03] local.INFO: Cron job executed {"time":"2025-07-02 02:59:03"} 
[2025-07-02 03:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:00:01] local.INFO: Cron job executed {"time":"2025-07-02 03:00:01"} 
[2025-07-02 03:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:00:02] local.INFO: Cron job executed {"time":"2025-07-02 03:00:02"} 
[2025-07-02 03:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:01:01] local.INFO: Cron job executed {"time":"2025-07-02 03:01:01"} 
[2025-07-02 03:01:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:01:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:01:03] local.INFO: Cron job executed {"time":"2025-07-02 03:01:03"} 
[2025-07-02 03:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:02:01] local.INFO: Cron job executed {"time":"2025-07-02 03:02:01"} 
[2025-07-02 03:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:02:03] local.INFO: Cron job executed {"time":"2025-07-02 03:02:03"} 
[2025-07-02 03:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:03:01] local.INFO: Cron job executed {"time":"2025-07-02 03:03:01"} 
[2025-07-02 03:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:03:02] local.INFO: Cron job executed {"time":"2025-07-02 03:03:02"} 
[2025-07-02 03:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:04:01] local.INFO: Cron job executed {"time":"2025-07-02 03:04:01"} 
[2025-07-02 03:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:04:02] local.INFO: Cron job executed {"time":"2025-07-02 03:04:02"} 
[2025-07-02 03:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:05:01] local.INFO: Cron job executed {"time":"2025-07-02 03:05:01"} 
[2025-07-02 03:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:05:02] local.INFO: Cron job executed {"time":"2025-07-02 03:05:02"} 
[2025-07-02 03:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:06:01] local.INFO: Cron job executed {"time":"2025-07-02 03:06:01"} 
[2025-07-02 03:06:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:06:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:06:03] local.INFO: Cron job executed {"time":"2025-07-02 03:06:03"} 
[2025-07-02 03:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:07:01] local.INFO: Cron job executed {"time":"2025-07-02 03:07:01"} 
[2025-07-02 03:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:07:02] local.INFO: Cron job executed {"time":"2025-07-02 03:07:02"} 
[2025-07-02 03:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:08:01] local.INFO: Cron job executed {"time":"2025-07-02 03:08:01"} 
[2025-07-02 03:08:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:08:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:08:03] local.INFO: Cron job executed {"time":"2025-07-02 03:08:03"} 
[2025-07-02 03:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:09:01] local.INFO: Cron job executed {"time":"2025-07-02 03:09:01"} 
[2025-07-02 03:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:09:02] local.INFO: Cron job executed {"time":"2025-07-02 03:09:02"} 
[2025-07-02 03:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:10:01] local.INFO: Cron job executed {"time":"2025-07-02 03:10:01"} 
[2025-07-02 03:10:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:10:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:10:03] local.INFO: Cron job executed {"time":"2025-07-02 03:10:03"} 
[2025-07-02 03:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:11:01] local.INFO: Cron job executed {"time":"2025-07-02 03:11:01"} 
[2025-07-02 03:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:11:03] local.INFO: Cron job executed {"time":"2025-07-02 03:11:03"} 
[2025-07-02 03:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:12:01] local.INFO: Cron job executed {"time":"2025-07-02 03:12:01"} 
[2025-07-02 03:12:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:12:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:12:03] local.INFO: Cron job executed {"time":"2025-07-02 03:12:03"} 
[2025-07-02 03:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:13:01] local.INFO: Cron job executed {"time":"2025-07-02 03:13:01"} 
[2025-07-02 03:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:13:02] local.INFO: Cron job executed {"time":"2025-07-02 03:13:02"} 
[2025-07-02 03:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:14:01] local.INFO: Cron job executed {"time":"2025-07-02 03:14:01"} 
[2025-07-02 03:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:14:02] local.INFO: Cron job executed {"time":"2025-07-02 03:14:02"} 
[2025-07-02 03:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:15:01] local.INFO: Cron job executed {"time":"2025-07-02 03:15:01"} 
[2025-07-02 03:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:15:03] local.INFO: Cron job executed {"time":"2025-07-02 03:15:03"} 
[2025-07-02 03:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:16:01] local.INFO: Cron job executed {"time":"2025-07-02 03:16:01"} 
[2025-07-02 03:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:16:02] local.INFO: Cron job executed {"time":"2025-07-02 03:16:02"} 
[2025-07-02 03:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:17:01] local.INFO: Cron job executed {"time":"2025-07-02 03:17:01"} 
[2025-07-02 03:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:17:02] local.INFO: Cron job executed {"time":"2025-07-02 03:17:02"} 
[2025-07-02 03:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:18:01] local.INFO: Cron job executed {"time":"2025-07-02 03:18:01"} 
[2025-07-02 03:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:18:02] local.INFO: Cron job executed {"time":"2025-07-02 03:18:02"} 
[2025-07-02 03:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:19:01] local.INFO: Cron job executed {"time":"2025-07-02 03:19:01"} 
[2025-07-02 03:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:19:02] local.INFO: Cron job executed {"time":"2025-07-02 03:19:02"} 
[2025-07-02 03:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:20:01] local.INFO: Cron job executed {"time":"2025-07-02 03:20:01"} 
[2025-07-02 03:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:20:02] local.INFO: Cron job executed {"time":"2025-07-02 03:20:02"} 
[2025-07-02 03:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:21:01] local.INFO: Cron job executed {"time":"2025-07-02 03:21:01"} 
[2025-07-02 03:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:21:02] local.INFO: Cron job executed {"time":"2025-07-02 03:21:02"} 
[2025-07-02 03:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:22:01] local.INFO: Cron job executed {"time":"2025-07-02 03:22:01"} 
[2025-07-02 03:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:22:02] local.INFO: Cron job executed {"time":"2025-07-02 03:22:02"} 
[2025-07-02 03:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:23:01] local.INFO: Cron job executed {"time":"2025-07-02 03:23:01"} 
[2025-07-02 03:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:23:02] local.INFO: Cron job executed {"time":"2025-07-02 03:23:02"} 
[2025-07-02 03:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:24:01] local.INFO: Cron job executed {"time":"2025-07-02 03:24:01"} 
[2025-07-02 03:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:24:02] local.INFO: Cron job executed {"time":"2025-07-02 03:24:02"} 
[2025-07-02 03:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:25:01] local.INFO: Cron job executed {"time":"2025-07-02 03:25:01"} 
[2025-07-02 03:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:25:02] local.INFO: Cron job executed {"time":"2025-07-02 03:25:02"} 
[2025-07-02 03:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:26:01] local.INFO: Cron job executed {"time":"2025-07-02 03:26:01"} 
[2025-07-02 03:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:26:02] local.INFO: Cron job executed {"time":"2025-07-02 03:26:02"} 
[2025-07-02 03:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:27:01] local.INFO: Cron job executed {"time":"2025-07-02 03:27:01"} 
[2025-07-02 03:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:27:02] local.INFO: Cron job executed {"time":"2025-07-02 03:27:02"} 
[2025-07-02 03:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:28:01] local.INFO: Cron job executed {"time":"2025-07-02 03:28:01"} 
[2025-07-02 03:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:28:02] local.INFO: Cron job executed {"time":"2025-07-02 03:28:02"} 
[2025-07-02 03:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:29:01] local.INFO: Cron job executed {"time":"2025-07-02 03:29:01"} 
[2025-07-02 03:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:29:02] local.INFO: Cron job executed {"time":"2025-07-02 03:29:02"} 
[2025-07-02 03:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:30:01] local.INFO: Cron job executed {"time":"2025-07-02 03:30:01"} 
[2025-07-02 03:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:30:02] local.INFO: Cron job executed {"time":"2025-07-02 03:30:02"} 
[2025-07-02 03:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:31:01] local.INFO: Cron job executed {"time":"2025-07-02 03:31:01"} 
[2025-07-02 03:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:31:02] local.INFO: Cron job executed {"time":"2025-07-02 03:31:02"} 
[2025-07-02 03:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:32:01] local.INFO: Cron job executed {"time":"2025-07-02 03:32:01"} 
[2025-07-02 03:32:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:32:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:32:03] local.INFO: Cron job executed {"time":"2025-07-02 03:32:03"} 
[2025-07-02 03:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:33:01] local.INFO: Cron job executed {"time":"2025-07-02 03:33:01"} 
[2025-07-02 03:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:33:02] local.INFO: Cron job executed {"time":"2025-07-02 03:33:02"} 
[2025-07-02 03:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:34:01] local.INFO: Cron job executed {"time":"2025-07-02 03:34:01"} 
[2025-07-02 03:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:34:02] local.INFO: Cron job executed {"time":"2025-07-02 03:34:02"} 
[2025-07-02 03:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:35:01] local.INFO: Cron job executed {"time":"2025-07-02 03:35:01"} 
[2025-07-02 03:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:35:02] local.INFO: Cron job executed {"time":"2025-07-02 03:35:02"} 
[2025-07-02 03:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:36:01] local.INFO: Cron job executed {"time":"2025-07-02 03:36:01"} 
[2025-07-02 03:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:36:02] local.INFO: Cron job executed {"time":"2025-07-02 03:36:02"} 
[2025-07-02 03:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:37:01] local.INFO: Cron job executed {"time":"2025-07-02 03:37:01"} 
[2025-07-02 03:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:37:02] local.INFO: Cron job executed {"time":"2025-07-02 03:37:02"} 
[2025-07-02 03:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:38:01] local.INFO: Cron job executed {"time":"2025-07-02 03:38:01"} 
[2025-07-02 03:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:38:02] local.INFO: Cron job executed {"time":"2025-07-02 03:38:02"} 
[2025-07-02 03:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:39:01] local.INFO: Cron job executed {"time":"2025-07-02 03:39:01"} 
[2025-07-02 03:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:39:02] local.INFO: Cron job executed {"time":"2025-07-02 03:39:02"} 
[2025-07-02 03:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:40:01] local.INFO: Cron job executed {"time":"2025-07-02 03:40:01"} 
[2025-07-02 03:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:40:02] local.INFO: Cron job executed {"time":"2025-07-02 03:40:02"} 
[2025-07-02 03:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:41:01] local.INFO: Cron job executed {"time":"2025-07-02 03:41:01"} 
[2025-07-02 03:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:41:02] local.INFO: Cron job executed {"time":"2025-07-02 03:41:02"} 
[2025-07-02 03:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:42:01] local.INFO: Cron job executed {"time":"2025-07-02 03:42:01"} 
[2025-07-02 03:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:42:03] local.INFO: Cron job executed {"time":"2025-07-02 03:42:03"} 
[2025-07-02 03:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:43:01] local.INFO: Cron job executed {"time":"2025-07-02 03:43:01"} 
[2025-07-02 03:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:43:03] local.INFO: Cron job executed {"time":"2025-07-02 03:43:03"} 
[2025-07-02 03:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:44:01] local.INFO: Cron job executed {"time":"2025-07-02 03:44:01"} 
[2025-07-02 03:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:44:03] local.INFO: Cron job executed {"time":"2025-07-02 03:44:03"} 
[2025-07-02 03:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:45:01] local.INFO: Cron job executed {"time":"2025-07-02 03:45:01"} 
[2025-07-02 03:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:45:03] local.INFO: Cron job executed {"time":"2025-07-02 03:45:03"} 
[2025-07-02 03:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:46:01] local.INFO: Cron job executed {"time":"2025-07-02 03:46:01"} 
[2025-07-02 03:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:46:02] local.INFO: Cron job executed {"time":"2025-07-02 03:46:02"} 
[2025-07-02 03:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:47:01] local.INFO: Cron job executed {"time":"2025-07-02 03:47:01"} 
[2025-07-02 03:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:47:02] local.INFO: Cron job executed {"time":"2025-07-02 03:47:02"} 
[2025-07-02 03:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:48:01] local.INFO: Cron job executed {"time":"2025-07-02 03:48:01"} 
[2025-07-02 03:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:48:02] local.INFO: Cron job executed {"time":"2025-07-02 03:48:02"} 
[2025-07-02 03:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:49:01] local.INFO: Cron job executed {"time":"2025-07-02 03:49:01"} 
[2025-07-02 03:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:49:02] local.INFO: Cron job executed {"time":"2025-07-02 03:49:02"} 
[2025-07-02 03:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:50:01] local.INFO: Cron job executed {"time":"2025-07-02 03:50:01"} 
[2025-07-02 03:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:50:02] local.INFO: Cron job executed {"time":"2025-07-02 03:50:02"} 
[2025-07-02 03:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:51:01] local.INFO: Cron job executed {"time":"2025-07-02 03:51:01"} 
[2025-07-02 03:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:51:02] local.INFO: Cron job executed {"time":"2025-07-02 03:51:02"} 
[2025-07-02 03:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:52:01] local.INFO: Cron job executed {"time":"2025-07-02 03:52:01"} 
[2025-07-02 03:52:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:52:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:52:03] local.INFO: Cron job executed {"time":"2025-07-02 03:52:03"} 
[2025-07-02 03:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:53:01] local.INFO: Cron job executed {"time":"2025-07-02 03:53:01"} 
[2025-07-02 03:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:53:02] local.INFO: Cron job executed {"time":"2025-07-02 03:53:02"} 
[2025-07-02 03:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:54:01] local.INFO: Cron job executed {"time":"2025-07-02 03:54:01"} 
[2025-07-02 03:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:54:02] local.INFO: Cron job executed {"time":"2025-07-02 03:54:02"} 
[2025-07-02 03:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:55:01] local.INFO: Cron job executed {"time":"2025-07-02 03:55:01"} 
[2025-07-02 03:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:55:02] local.INFO: Cron job executed {"time":"2025-07-02 03:55:02"} 
[2025-07-02 03:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:56:01] local.INFO: Cron job executed {"time":"2025-07-02 03:56:01"} 
[2025-07-02 03:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:56:03] local.INFO: Cron job executed {"time":"2025-07-02 03:56:03"} 
[2025-07-02 03:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:57:01] local.INFO: Cron job executed {"time":"2025-07-02 03:57:01"} 
[2025-07-02 03:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:57:02] local.INFO: Cron job executed {"time":"2025-07-02 03:57:02"} 
[2025-07-02 03:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:58:01] local.INFO: Cron job executed {"time":"2025-07-02 03:58:01"} 
[2025-07-02 03:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:58:02] local.INFO: Cron job executed {"time":"2025-07-02 03:58:02"} 
[2025-07-02 03:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:59:01] local.INFO: Cron job executed {"time":"2025-07-02 03:59:01"} 
[2025-07-02 03:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 03:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 03:59:03] local.INFO: Cron job executed {"time":"2025-07-02 03:59:03"} 
[2025-07-02 04:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:00:01] local.INFO: Cron job executed {"time":"2025-07-02 04:00:01"} 
[2025-07-02 04:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:00:02] local.INFO: Cron job executed {"time":"2025-07-02 04:00:02"} 
[2025-07-02 04:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:01:01] local.INFO: Cron job executed {"time":"2025-07-02 04:01:01"} 
[2025-07-02 04:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:01:02] local.INFO: Cron job executed {"time":"2025-07-02 04:01:02"} 
[2025-07-02 04:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:02:01] local.INFO: Cron job executed {"time":"2025-07-02 04:02:01"} 
[2025-07-02 04:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:02:02] local.INFO: Cron job executed {"time":"2025-07-02 04:02:02"} 
[2025-07-02 04:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:03:01] local.INFO: Cron job executed {"time":"2025-07-02 04:03:01"} 
[2025-07-02 04:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:03:02] local.INFO: Cron job executed {"time":"2025-07-02 04:03:02"} 
[2025-07-02 04:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:04:01] local.INFO: Cron job executed {"time":"2025-07-02 04:04:01"} 
[2025-07-02 04:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:04:02] local.INFO: Cron job executed {"time":"2025-07-02 04:04:02"} 
[2025-07-02 04:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:05:01] local.INFO: Cron job executed {"time":"2025-07-02 04:05:01"} 
[2025-07-02 04:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:05:02] local.INFO: Cron job executed {"time":"2025-07-02 04:05:02"} 
[2025-07-02 04:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:06:01] local.INFO: Cron job executed {"time":"2025-07-02 04:06:01"} 
[2025-07-02 04:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:06:02] local.INFO: Cron job executed {"time":"2025-07-02 04:06:02"} 
[2025-07-02 04:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:07:01] local.INFO: Cron job executed {"time":"2025-07-02 04:07:01"} 
[2025-07-02 04:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:07:02] local.INFO: Cron job executed {"time":"2025-07-02 04:07:02"} 
[2025-07-02 04:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:08:01] local.INFO: Cron job executed {"time":"2025-07-02 04:08:01"} 
[2025-07-02 04:08:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:08:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:08:03] local.INFO: Cron job executed {"time":"2025-07-02 04:08:03"} 
[2025-07-02 04:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:09:01] local.INFO: Cron job executed {"time":"2025-07-02 04:09:01"} 
[2025-07-02 04:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:09:02] local.INFO: Cron job executed {"time":"2025-07-02 04:09:02"} 
[2025-07-02 04:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:10:01] local.INFO: Cron job executed {"time":"2025-07-02 04:10:01"} 
[2025-07-02 04:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:10:02] local.INFO: Cron job executed {"time":"2025-07-02 04:10:02"} 
[2025-07-02 04:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:11:01] local.INFO: Cron job executed {"time":"2025-07-02 04:11:01"} 
[2025-07-02 04:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:11:02] local.INFO: Cron job executed {"time":"2025-07-02 04:11:02"} 
[2025-07-02 04:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:12:01] local.INFO: Cron job executed {"time":"2025-07-02 04:12:01"} 
[2025-07-02 04:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:12:02] local.INFO: Cron job executed {"time":"2025-07-02 04:12:02"} 
[2025-07-02 04:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:13:01] local.INFO: Cron job executed {"time":"2025-07-02 04:13:01"} 
[2025-07-02 04:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:13:02] local.INFO: Cron job executed {"time":"2025-07-02 04:13:02"} 
[2025-07-02 04:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:14:01] local.INFO: Cron job executed {"time":"2025-07-02 04:14:01"} 
[2025-07-02 04:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:14:02] local.INFO: Cron job executed {"time":"2025-07-02 04:14:02"} 
[2025-07-02 04:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:15:01] local.INFO: Cron job executed {"time":"2025-07-02 04:15:01"} 
[2025-07-02 04:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:15:02] local.INFO: Cron job executed {"time":"2025-07-02 04:15:02"} 
[2025-07-02 04:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:16:01] local.INFO: Cron job executed {"time":"2025-07-02 04:16:01"} 
[2025-07-02 04:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:16:02] local.INFO: Cron job executed {"time":"2025-07-02 04:16:02"} 
[2025-07-02 04:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:17:01] local.INFO: Cron job executed {"time":"2025-07-02 04:17:01"} 
[2025-07-02 04:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:17:02] local.INFO: Cron job executed {"time":"2025-07-02 04:17:02"} 
[2025-07-02 04:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:18:01] local.INFO: Cron job executed {"time":"2025-07-02 04:18:01"} 
[2025-07-02 04:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:18:02] local.INFO: Cron job executed {"time":"2025-07-02 04:18:02"} 
[2025-07-02 04:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:19:01] local.INFO: Cron job executed {"time":"2025-07-02 04:19:01"} 
[2025-07-02 04:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:19:02] local.INFO: Cron job executed {"time":"2025-07-02 04:19:02"} 
[2025-07-02 04:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:20:01] local.INFO: Cron job executed {"time":"2025-07-02 04:20:01"} 
[2025-07-02 04:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:20:02] local.INFO: Cron job executed {"time":"2025-07-02 04:20:02"} 
[2025-07-02 04:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:21:01] local.INFO: Cron job executed {"time":"2025-07-02 04:21:01"} 
[2025-07-02 04:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:21:02] local.INFO: Cron job executed {"time":"2025-07-02 04:21:02"} 
[2025-07-02 04:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:22:01] local.INFO: Cron job executed {"time":"2025-07-02 04:22:01"} 
[2025-07-02 04:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:22:02] local.INFO: Cron job executed {"time":"2025-07-02 04:22:02"} 
[2025-07-02 04:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:23:01] local.INFO: Cron job executed {"time":"2025-07-02 04:23:01"} 
[2025-07-02 04:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:23:02] local.INFO: Cron job executed {"time":"2025-07-02 04:23:02"} 
[2025-07-02 04:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:24:01] local.INFO: Cron job executed {"time":"2025-07-02 04:24:01"} 
[2025-07-02 04:24:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:24:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:24:03] local.INFO: Cron job executed {"time":"2025-07-02 04:24:03"} 
[2025-07-02 04:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:25:01] local.INFO: Cron job executed {"time":"2025-07-02 04:25:01"} 
[2025-07-02 04:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:25:02] local.INFO: Cron job executed {"time":"2025-07-02 04:25:02"} 
[2025-07-02 04:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:26:01] local.INFO: Cron job executed {"time":"2025-07-02 04:26:01"} 
[2025-07-02 04:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:26:02] local.INFO: Cron job executed {"time":"2025-07-02 04:26:02"} 
[2025-07-02 04:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:27:01] local.INFO: Cron job executed {"time":"2025-07-02 04:27:01"} 
[2025-07-02 04:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:27:02] local.INFO: Cron job executed {"time":"2025-07-02 04:27:02"} 
[2025-07-02 04:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:28:01] local.INFO: Cron job executed {"time":"2025-07-02 04:28:01"} 
[2025-07-02 04:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:28:02] local.INFO: Cron job executed {"time":"2025-07-02 04:28:02"} 
[2025-07-02 04:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:29:01] local.INFO: Cron job executed {"time":"2025-07-02 04:29:01"} 
[2025-07-02 04:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:29:02] local.INFO: Cron job executed {"time":"2025-07-02 04:29:02"} 
[2025-07-02 04:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:30:01] local.INFO: Cron job executed {"time":"2025-07-02 04:30:01"} 
[2025-07-02 04:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:30:02] local.INFO: Cron job executed {"time":"2025-07-02 04:30:02"} 
[2025-07-02 04:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:31:01] local.INFO: Cron job executed {"time":"2025-07-02 04:31:01"} 
[2025-07-02 04:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:31:02] local.INFO: Cron job executed {"time":"2025-07-02 04:31:02"} 
[2025-07-02 04:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:32:01] local.INFO: Cron job executed {"time":"2025-07-02 04:32:01"} 
[2025-07-02 04:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:32:02] local.INFO: Cron job executed {"time":"2025-07-02 04:32:02"} 
[2025-07-02 04:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:33:01] local.INFO: Cron job executed {"time":"2025-07-02 04:33:01"} 
[2025-07-02 04:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:33:02] local.INFO: Cron job executed {"time":"2025-07-02 04:33:02"} 
[2025-07-02 04:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:34:01] local.INFO: Cron job executed {"time":"2025-07-02 04:34:01"} 
[2025-07-02 04:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:34:02] local.INFO: Cron job executed {"time":"2025-07-02 04:34:02"} 
[2025-07-02 04:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:35:01] local.INFO: Cron job executed {"time":"2025-07-02 04:35:01"} 
[2025-07-02 04:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:35:02] local.INFO: Cron job executed {"time":"2025-07-02 04:35:02"} 
[2025-07-02 04:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:36:01] local.INFO: Cron job executed {"time":"2025-07-02 04:36:01"} 
[2025-07-02 04:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:36:02] local.INFO: Cron job executed {"time":"2025-07-02 04:36:02"} 
[2025-07-02 04:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:37:01] local.INFO: Cron job executed {"time":"2025-07-02 04:37:01"} 
[2025-07-02 04:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:37:02] local.INFO: Cron job executed {"time":"2025-07-02 04:37:02"} 
[2025-07-02 04:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:38:01] local.INFO: Cron job executed {"time":"2025-07-02 04:38:01"} 
[2025-07-02 04:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:38:02] local.INFO: Cron job executed {"time":"2025-07-02 04:38:02"} 
[2025-07-02 04:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:39:01] local.INFO: Cron job executed {"time":"2025-07-02 04:39:01"} 
[2025-07-02 04:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:39:02] local.INFO: Cron job executed {"time":"2025-07-02 04:39:02"} 
[2025-07-02 04:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:40:01] local.INFO: Cron job executed {"time":"2025-07-02 04:40:01"} 
[2025-07-02 04:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:40:02] local.INFO: Cron job executed {"time":"2025-07-02 04:40:02"} 
[2025-07-02 04:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:41:01] local.INFO: Cron job executed {"time":"2025-07-02 04:41:01"} 
[2025-07-02 04:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:41:02] local.INFO: Cron job executed {"time":"2025-07-02 04:41:02"} 
[2025-07-02 04:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:42:01] local.INFO: Cron job executed {"time":"2025-07-02 04:42:01"} 
[2025-07-02 04:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:42:02] local.INFO: Cron job executed {"time":"2025-07-02 04:42:02"} 
[2025-07-02 04:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:43:01] local.INFO: Cron job executed {"time":"2025-07-02 04:43:01"} 
[2025-07-02 04:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:43:02] local.INFO: Cron job executed {"time":"2025-07-02 04:43:02"} 
[2025-07-02 04:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:44:01] local.INFO: Cron job executed {"time":"2025-07-02 04:44:01"} 
[2025-07-02 04:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:44:02] local.INFO: Cron job executed {"time":"2025-07-02 04:44:02"} 
[2025-07-02 04:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:45:01] local.INFO: Cron job executed {"time":"2025-07-02 04:45:01"} 
[2025-07-02 04:45:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:45:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:45:03] local.INFO: Cron job executed {"time":"2025-07-02 04:45:03"} 
[2025-07-02 04:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:46:01] local.INFO: Cron job executed {"time":"2025-07-02 04:46:01"} 
[2025-07-02 04:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:46:02] local.INFO: Cron job executed {"time":"2025-07-02 04:46:02"} 
[2025-07-02 04:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:47:01] local.INFO: Cron job executed {"time":"2025-07-02 04:47:01"} 
[2025-07-02 04:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:47:02] local.INFO: Cron job executed {"time":"2025-07-02 04:47:02"} 
[2025-07-02 04:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:48:01] local.INFO: Cron job executed {"time":"2025-07-02 04:48:01"} 
[2025-07-02 04:48:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:48:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:48:03] local.INFO: Cron job executed {"time":"2025-07-02 04:48:03"} 
[2025-07-02 04:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:49:01] local.INFO: Cron job executed {"time":"2025-07-02 04:49:01"} 
[2025-07-02 04:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:49:02] local.INFO: Cron job executed {"time":"2025-07-02 04:49:02"} 
[2025-07-02 04:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:50:01] local.INFO: Cron job executed {"time":"2025-07-02 04:50:01"} 
[2025-07-02 04:50:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:50:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:50:03] local.INFO: Cron job executed {"time":"2025-07-02 04:50:03"} 
[2025-07-02 04:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:51:01] local.INFO: Cron job executed {"time":"2025-07-02 04:51:01"} 
[2025-07-02 04:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:51:02] local.INFO: Cron job executed {"time":"2025-07-02 04:51:02"} 
[2025-07-02 04:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:52:01] local.INFO: Cron job executed {"time":"2025-07-02 04:52:01"} 
[2025-07-02 04:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:52:02] local.INFO: Cron job executed {"time":"2025-07-02 04:52:02"} 
[2025-07-02 04:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:53:01] local.INFO: Cron job executed {"time":"2025-07-02 04:53:01"} 
[2025-07-02 04:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:53:02] local.INFO: Cron job executed {"time":"2025-07-02 04:53:02"} 
[2025-07-02 04:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:54:01] local.INFO: Cron job executed {"time":"2025-07-02 04:54:01"} 
[2025-07-02 04:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:54:02] local.INFO: Cron job executed {"time":"2025-07-02 04:54:02"} 
[2025-07-02 04:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:55:01] local.INFO: Cron job executed {"time":"2025-07-02 04:55:01"} 
[2025-07-02 04:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:55:02] local.INFO: Cron job executed {"time":"2025-07-02 04:55:02"} 
[2025-07-02 04:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:56:01] local.INFO: Cron job executed {"time":"2025-07-02 04:56:01"} 
[2025-07-02 04:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:56:02] local.INFO: Cron job executed {"time":"2025-07-02 04:56:02"} 
[2025-07-02 04:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:57:01] local.INFO: Cron job executed {"time":"2025-07-02 04:57:01"} 
[2025-07-02 04:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:57:02] local.INFO: Cron job executed {"time":"2025-07-02 04:57:02"} 
[2025-07-02 04:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:58:01] local.INFO: Cron job executed {"time":"2025-07-02 04:58:01"} 
[2025-07-02 04:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:58:02] local.INFO: Cron job executed {"time":"2025-07-02 04:58:02"} 
[2025-07-02 04:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:59:01] local.INFO: Cron job executed {"time":"2025-07-02 04:59:01"} 
[2025-07-02 04:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 04:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 04:59:02] local.INFO: Cron job executed {"time":"2025-07-02 04:59:02"} 
[2025-07-02 05:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:00:01] local.INFO: Cron job executed {"time":"2025-07-02 05:00:01"} 
[2025-07-02 05:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:00:02] local.INFO: Cron job executed {"time":"2025-07-02 05:00:02"} 
[2025-07-02 05:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:01:01] local.INFO: Cron job executed {"time":"2025-07-02 05:01:01"} 
[2025-07-02 05:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:01:02] local.INFO: Cron job executed {"time":"2025-07-02 05:01:02"} 
[2025-07-02 05:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:02:01] local.INFO: Cron job executed {"time":"2025-07-02 05:02:01"} 
[2025-07-02 05:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:02:02] local.INFO: Cron job executed {"time":"2025-07-02 05:02:02"} 
[2025-07-02 05:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:03:01] local.INFO: Cron job executed {"time":"2025-07-02 05:03:01"} 
[2025-07-02 05:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:03:02] local.INFO: Cron job executed {"time":"2025-07-02 05:03:02"} 
[2025-07-02 05:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:04:01] local.INFO: Cron job executed {"time":"2025-07-02 05:04:01"} 
[2025-07-02 05:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:04:02] local.INFO: Cron job executed {"time":"2025-07-02 05:04:02"} 
[2025-07-02 05:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:05:01] local.INFO: Cron job executed {"time":"2025-07-02 05:05:01"} 
[2025-07-02 05:05:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:05:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:05:03] local.INFO: Cron job executed {"time":"2025-07-02 05:05:03"} 
[2025-07-02 05:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:06:01] local.INFO: Cron job executed {"time":"2025-07-02 05:06:01"} 
[2025-07-02 05:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:06:02] local.INFO: Cron job executed {"time":"2025-07-02 05:06:02"} 
[2025-07-02 05:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:07:01] local.INFO: Cron job executed {"time":"2025-07-02 05:07:01"} 
[2025-07-02 05:07:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:07:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:07:03] local.INFO: Cron job executed {"time":"2025-07-02 05:07:03"} 
[2025-07-02 05:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:08:01] local.INFO: Cron job executed {"time":"2025-07-02 05:08:01"} 
[2025-07-02 05:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:08:02] local.INFO: Cron job executed {"time":"2025-07-02 05:08:02"} 
[2025-07-02 05:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:09:01] local.INFO: Cron job executed {"time":"2025-07-02 05:09:01"} 
[2025-07-02 05:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:09:02] local.INFO: Cron job executed {"time":"2025-07-02 05:09:02"} 
[2025-07-02 05:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:10:01] local.INFO: Cron job executed {"time":"2025-07-02 05:10:01"} 
[2025-07-02 05:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:10:02] local.INFO: Cron job executed {"time":"2025-07-02 05:10:02"} 
[2025-07-02 05:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:11:01] local.INFO: Cron job executed {"time":"2025-07-02 05:11:01"} 
[2025-07-02 05:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:11:03] local.INFO: Cron job executed {"time":"2025-07-02 05:11:03"} 
[2025-07-02 05:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:12:01] local.INFO: Cron job executed {"time":"2025-07-02 05:12:01"} 
[2025-07-02 05:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:12:02] local.INFO: Cron job executed {"time":"2025-07-02 05:12:02"} 
[2025-07-02 05:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:13:01] local.INFO: Cron job executed {"time":"2025-07-02 05:13:01"} 
[2025-07-02 05:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:13:02] local.INFO: Cron job executed {"time":"2025-07-02 05:13:02"} 
[2025-07-02 05:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:14:01] local.INFO: Cron job executed {"time":"2025-07-02 05:14:01"} 
[2025-07-02 05:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:14:02] local.INFO: Cron job executed {"time":"2025-07-02 05:14:02"} 
[2025-07-02 05:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:15:01] local.INFO: Cron job executed {"time":"2025-07-02 05:15:01"} 
[2025-07-02 05:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:15:02] local.INFO: Cron job executed {"time":"2025-07-02 05:15:02"} 
[2025-07-02 05:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:16:01] local.INFO: Cron job executed {"time":"2025-07-02 05:16:01"} 
[2025-07-02 05:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:16:02] local.INFO: Cron job executed {"time":"2025-07-02 05:16:02"} 
[2025-07-02 05:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:17:01] local.INFO: Cron job executed {"time":"2025-07-02 05:17:01"} 
[2025-07-02 05:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:17:02] local.INFO: Cron job executed {"time":"2025-07-02 05:17:02"} 
[2025-07-02 05:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:18:01] local.INFO: Cron job executed {"time":"2025-07-02 05:18:01"} 
[2025-07-02 05:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:18:02] local.INFO: Cron job executed {"time":"2025-07-02 05:18:02"} 
[2025-07-02 05:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:19:01] local.INFO: Cron job executed {"time":"2025-07-02 05:19:01"} 
[2025-07-02 05:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:19:02] local.INFO: Cron job executed {"time":"2025-07-02 05:19:02"} 
[2025-07-02 05:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:20:01] local.INFO: Cron job executed {"time":"2025-07-02 05:20:01"} 
[2025-07-02 05:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:20:02] local.INFO: Cron job executed {"time":"2025-07-02 05:20:02"} 
[2025-07-02 05:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:21:01] local.INFO: Cron job executed {"time":"2025-07-02 05:21:01"} 
[2025-07-02 05:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:21:02] local.INFO: Cron job executed {"time":"2025-07-02 05:21:02"} 
[2025-07-02 05:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:22:01] local.INFO: Cron job executed {"time":"2025-07-02 05:22:01"} 
[2025-07-02 05:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:22:03] local.INFO: Cron job executed {"time":"2025-07-02 05:22:03"} 
[2025-07-02 05:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:23:01] local.INFO: Cron job executed {"time":"2025-07-02 05:23:01"} 
[2025-07-02 05:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:23:02] local.INFO: Cron job executed {"time":"2025-07-02 05:23:02"} 
[2025-07-02 05:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:24:01] local.INFO: Cron job executed {"time":"2025-07-02 05:24:01"} 
[2025-07-02 05:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:24:02] local.INFO: Cron job executed {"time":"2025-07-02 05:24:02"} 
[2025-07-02 05:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:25:01] local.INFO: Cron job executed {"time":"2025-07-02 05:25:01"} 
[2025-07-02 05:25:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:25:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:25:03] local.INFO: Cron job executed {"time":"2025-07-02 05:25:03"} 
[2025-07-02 05:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:26:01] local.INFO: Cron job executed {"time":"2025-07-02 05:26:01"} 
[2025-07-02 05:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:26:03] local.INFO: Cron job executed {"time":"2025-07-02 05:26:03"} 
[2025-07-02 05:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:27:01] local.INFO: Cron job executed {"time":"2025-07-02 05:27:01"} 
[2025-07-02 05:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:27:02] local.INFO: Cron job executed {"time":"2025-07-02 05:27:02"} 
[2025-07-02 05:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:28:01] local.INFO: Cron job executed {"time":"2025-07-02 05:28:01"} 
[2025-07-02 05:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:28:02] local.INFO: Cron job executed {"time":"2025-07-02 05:28:02"} 
[2025-07-02 05:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:29:01] local.INFO: Cron job executed {"time":"2025-07-02 05:29:01"} 
[2025-07-02 05:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:29:02] local.INFO: Cron job executed {"time":"2025-07-02 05:29:02"} 
[2025-07-02 05:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:30:01] local.INFO: Cron job executed {"time":"2025-07-02 05:30:01"} 
[2025-07-02 05:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:30:02] local.INFO: Cron job executed {"time":"2025-07-02 05:30:02"} 
[2025-07-02 05:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:31:01] local.INFO: Cron job executed {"time":"2025-07-02 05:31:01"} 
[2025-07-02 05:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:31:02] local.INFO: Cron job executed {"time":"2025-07-02 05:31:02"} 
[2025-07-02 05:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:32:01] local.INFO: Cron job executed {"time":"2025-07-02 05:32:01"} 
[2025-07-02 05:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:32:02] local.INFO: Cron job executed {"time":"2025-07-02 05:32:02"} 
[2025-07-02 05:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:33:01] local.INFO: Cron job executed {"time":"2025-07-02 05:33:01"} 
[2025-07-02 05:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:33:02] local.INFO: Cron job executed {"time":"2025-07-02 05:33:02"} 
[2025-07-02 05:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:34:01] local.INFO: Cron job executed {"time":"2025-07-02 05:34:01"} 
[2025-07-02 05:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:34:02] local.INFO: Cron job executed {"time":"2025-07-02 05:34:02"} 
[2025-07-02 05:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:35:01] local.INFO: Cron job executed {"time":"2025-07-02 05:35:01"} 
[2025-07-02 05:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:35:02] local.INFO: Cron job executed {"time":"2025-07-02 05:35:02"} 
[2025-07-02 05:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:36:01] local.INFO: Cron job executed {"time":"2025-07-02 05:36:01"} 
[2025-07-02 05:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:36:02] local.INFO: Cron job executed {"time":"2025-07-02 05:36:02"} 
[2025-07-02 05:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:37:01] local.INFO: Cron job executed {"time":"2025-07-02 05:37:01"} 
[2025-07-02 05:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:37:02] local.INFO: Cron job executed {"time":"2025-07-02 05:37:02"} 
[2025-07-02 05:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:38:01] local.INFO: Cron job executed {"time":"2025-07-02 05:38:01"} 
[2025-07-02 05:38:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:38:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:38:03] local.INFO: Cron job executed {"time":"2025-07-02 05:38:03"} 
[2025-07-02 05:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:39:01] local.INFO: Cron job executed {"time":"2025-07-02 05:39:01"} 
[2025-07-02 05:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:39:02] local.INFO: Cron job executed {"time":"2025-07-02 05:39:02"} 
[2025-07-02 05:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:40:01] local.INFO: Cron job executed {"time":"2025-07-02 05:40:01"} 
[2025-07-02 05:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:40:02] local.INFO: Cron job executed {"time":"2025-07-02 05:40:02"} 
[2025-07-02 05:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:41:01] local.INFO: Cron job executed {"time":"2025-07-02 05:41:01"} 
[2025-07-02 05:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:41:02] local.INFO: Cron job executed {"time":"2025-07-02 05:41:02"} 
[2025-07-02 05:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:42:01] local.INFO: Cron job executed {"time":"2025-07-02 05:42:01"} 
[2025-07-02 05:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:42:02] local.INFO: Cron job executed {"time":"2025-07-02 05:42:02"} 
[2025-07-02 05:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:43:01] local.INFO: Cron job executed {"time":"2025-07-02 05:43:01"} 
[2025-07-02 05:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:43:02] local.INFO: Cron job executed {"time":"2025-07-02 05:43:02"} 
[2025-07-02 05:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:44:01] local.INFO: Cron job executed {"time":"2025-07-02 05:44:01"} 
[2025-07-02 05:44:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:44:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:44:03] local.INFO: Cron job executed {"time":"2025-07-02 05:44:03"} 
[2025-07-02 05:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:45:01] local.INFO: Cron job executed {"time":"2025-07-02 05:45:01"} 
[2025-07-02 05:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:45:02] local.INFO: Cron job executed {"time":"2025-07-02 05:45:02"} 
[2025-07-02 05:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:46:01] local.INFO: Cron job executed {"time":"2025-07-02 05:46:01"} 
[2025-07-02 05:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:46:02] local.INFO: Cron job executed {"time":"2025-07-02 05:46:02"} 
[2025-07-02 05:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:47:01] local.INFO: Cron job executed {"time":"2025-07-02 05:47:01"} 
[2025-07-02 05:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:47:02] local.INFO: Cron job executed {"time":"2025-07-02 05:47:02"} 
[2025-07-02 05:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:48:01] local.INFO: Cron job executed {"time":"2025-07-02 05:48:01"} 
[2025-07-02 05:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:48:02] local.INFO: Cron job executed {"time":"2025-07-02 05:48:02"} 
[2025-07-02 05:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:49:01] local.INFO: Cron job executed {"time":"2025-07-02 05:49:01"} 
[2025-07-02 05:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:49:02] local.INFO: Cron job executed {"time":"2025-07-02 05:49:02"} 
[2025-07-02 05:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:50:01] local.INFO: Cron job executed {"time":"2025-07-02 05:50:01"} 
[2025-07-02 05:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:50:02] local.INFO: Cron job executed {"time":"2025-07-02 05:50:02"} 
[2025-07-02 05:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:51:01] local.INFO: Cron job executed {"time":"2025-07-02 05:51:01"} 
[2025-07-02 05:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:51:02] local.INFO: Cron job executed {"time":"2025-07-02 05:51:02"} 
[2025-07-02 05:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:52:01] local.INFO: Cron job executed {"time":"2025-07-02 05:52:01"} 
[2025-07-02 05:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:52:02] local.INFO: Cron job executed {"time":"2025-07-02 05:52:02"} 
[2025-07-02 05:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:53:01] local.INFO: Cron job executed {"time":"2025-07-02 05:53:01"} 
[2025-07-02 05:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:53:02] local.INFO: Cron job executed {"time":"2025-07-02 05:53:02"} 
[2025-07-02 05:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:54:01] local.INFO: Cron job executed {"time":"2025-07-02 05:54:01"} 
[2025-07-02 05:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:54:02] local.INFO: Cron job executed {"time":"2025-07-02 05:54:02"} 
[2025-07-02 05:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:55:01] local.INFO: Cron job executed {"time":"2025-07-02 05:55:01"} 
[2025-07-02 05:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:55:02] local.INFO: Cron job executed {"time":"2025-07-02 05:55:02"} 
[2025-07-02 05:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:56:01] local.INFO: Cron job executed {"time":"2025-07-02 05:56:01"} 
[2025-07-02 05:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:56:02] local.INFO: Cron job executed {"time":"2025-07-02 05:56:02"} 
[2025-07-02 05:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:57:01] local.INFO: Cron job executed {"time":"2025-07-02 05:57:01"} 
[2025-07-02 05:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:57:02] local.INFO: Cron job executed {"time":"2025-07-02 05:57:02"} 
[2025-07-02 05:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:58:01] local.INFO: Cron job executed {"time":"2025-07-02 05:58:01"} 
[2025-07-02 05:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:58:02] local.INFO: Cron job executed {"time":"2025-07-02 05:58:02"} 
[2025-07-02 05:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:59:01] local.INFO: Cron job executed {"time":"2025-07-02 05:59:01"} 
[2025-07-02 05:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 05:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 05:59:02] local.INFO: Cron job executed {"time":"2025-07-02 05:59:02"} 
[2025-07-02 06:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:00:01] local.INFO: Cron job executed {"time":"2025-07-02 06:00:01"} 
[2025-07-02 06:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:00:02] local.INFO: Cron job executed {"time":"2025-07-02 06:00:02"} 
[2025-07-02 06:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:01:01] local.INFO: Cron job executed {"time":"2025-07-02 06:01:01"} 
[2025-07-02 06:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:01:02] local.INFO: Cron job executed {"time":"2025-07-02 06:01:02"} 
[2025-07-02 06:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:02:01] local.INFO: Cron job executed {"time":"2025-07-02 06:02:01"} 
[2025-07-02 06:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:02:02] local.INFO: Cron job executed {"time":"2025-07-02 06:02:02"} 
[2025-07-02 06:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:03:01] local.INFO: Cron job executed {"time":"2025-07-02 06:03:01"} 
[2025-07-02 06:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:03:02] local.INFO: Cron job executed {"time":"2025-07-02 06:03:02"} 
[2025-07-02 06:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:04:01] local.INFO: Cron job executed {"time":"2025-07-02 06:04:01"} 
[2025-07-02 06:04:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:04:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:04:03] local.INFO: Cron job executed {"time":"2025-07-02 06:04:03"} 
[2025-07-02 06:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:05:01] local.INFO: Cron job executed {"time":"2025-07-02 06:05:01"} 
[2025-07-02 06:05:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:05:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:05:03] local.INFO: Cron job executed {"time":"2025-07-02 06:05:03"} 
[2025-07-02 06:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:06:01] local.INFO: Cron job executed {"time":"2025-07-02 06:06:01"} 
[2025-07-02 06:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:06:02] local.INFO: Cron job executed {"time":"2025-07-02 06:06:02"} 
[2025-07-02 06:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:07:01] local.INFO: Cron job executed {"time":"2025-07-02 06:07:01"} 
[2025-07-02 06:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:07:02] local.INFO: Cron job executed {"time":"2025-07-02 06:07:02"} 
[2025-07-02 06:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:08:01] local.INFO: Cron job executed {"time":"2025-07-02 06:08:01"} 
[2025-07-02 06:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:08:02] local.INFO: Cron job executed {"time":"2025-07-02 06:08:02"} 
[2025-07-02 06:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:09:01] local.INFO: Cron job executed {"time":"2025-07-02 06:09:01"} 
[2025-07-02 06:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:09:02] local.INFO: Cron job executed {"time":"2025-07-02 06:09:02"} 
[2025-07-02 06:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:10:01] local.INFO: Cron job executed {"time":"2025-07-02 06:10:01"} 
[2025-07-02 06:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:10:02] local.INFO: Cron job executed {"time":"2025-07-02 06:10:02"} 
[2025-07-02 06:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:11:01] local.INFO: Cron job executed {"time":"2025-07-02 06:11:01"} 
[2025-07-02 06:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:11:03] local.INFO: Cron job executed {"time":"2025-07-02 06:11:03"} 
[2025-07-02 06:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:12:01] local.INFO: Cron job executed {"time":"2025-07-02 06:12:01"} 
[2025-07-02 06:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:12:02] local.INFO: Cron job executed {"time":"2025-07-02 06:12:02"} 
[2025-07-02 06:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:13:01] local.INFO: Cron job executed {"time":"2025-07-02 06:13:01"} 
[2025-07-02 06:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:13:02] local.INFO: Cron job executed {"time":"2025-07-02 06:13:02"} 
[2025-07-02 06:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:14:01] local.INFO: Cron job executed {"time":"2025-07-02 06:14:01"} 
[2025-07-02 06:14:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:14:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:14:03] local.INFO: Cron job executed {"time":"2025-07-02 06:14:03"} 
[2025-07-02 06:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:15:01] local.INFO: Cron job executed {"time":"2025-07-02 06:15:01"} 
[2025-07-02 06:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:15:02] local.INFO: Cron job executed {"time":"2025-07-02 06:15:02"} 
[2025-07-02 06:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:16:01] local.INFO: Cron job executed {"time":"2025-07-02 06:16:01"} 
[2025-07-02 06:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:16:02] local.INFO: Cron job executed {"time":"2025-07-02 06:16:02"} 
[2025-07-02 06:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:17:01] local.INFO: Cron job executed {"time":"2025-07-02 06:17:01"} 
[2025-07-02 06:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:17:02] local.INFO: Cron job executed {"time":"2025-07-02 06:17:02"} 
[2025-07-02 06:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:18:01] local.INFO: Cron job executed {"time":"2025-07-02 06:18:01"} 
[2025-07-02 06:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:18:02] local.INFO: Cron job executed {"time":"2025-07-02 06:18:02"} 
[2025-07-02 06:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:19:01] local.INFO: Cron job executed {"time":"2025-07-02 06:19:01"} 
[2025-07-02 06:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:19:03] local.INFO: Cron job executed {"time":"2025-07-02 06:19:03"} 
[2025-07-02 06:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:20:01] local.INFO: Cron job executed {"time":"2025-07-02 06:20:01"} 
[2025-07-02 06:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:20:02] local.INFO: Cron job executed {"time":"2025-07-02 06:20:02"} 
[2025-07-02 06:21:37] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:21:37] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:21:37] local.INFO: Cron job executed {"time":"2025-07-02 06:21:37"} 
[2025-07-02 06:21:40] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:21:40] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:21:40] local.INFO: Cron job executed {"time":"2025-07-02 06:21:40"} 
[2025-07-02 06:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:22:01] local.INFO: Cron job executed {"time":"2025-07-02 06:22:01"} 
[2025-07-02 06:22:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:22:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:22:03] local.INFO: Cron job executed {"time":"2025-07-02 06:22:03"} 
[2025-07-02 06:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:23:01] local.INFO: Cron job executed {"time":"2025-07-02 06:23:01"} 
[2025-07-02 06:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:23:03] local.INFO: Cron job executed {"time":"2025-07-02 06:23:03"} 
[2025-07-02 06:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:24:01] local.INFO: Cron job executed {"time":"2025-07-02 06:24:01"} 
[2025-07-02 06:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:24:02] local.INFO: Cron job executed {"time":"2025-07-02 06:24:02"} 
[2025-07-02 06:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:25:01] local.INFO: Cron job executed {"time":"2025-07-02 06:25:01"} 
[2025-07-02 06:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:25:02] local.INFO: Cron job executed {"time":"2025-07-02 06:25:02"} 
[2025-07-02 06:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:26:01] local.INFO: Cron job executed {"time":"2025-07-02 06:26:01"} 
[2025-07-02 06:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:26:03] local.INFO: Cron job executed {"time":"2025-07-02 06:26:03"} 
[2025-07-02 06:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:27:01] local.INFO: Cron job executed {"time":"2025-07-02 06:27:01"} 
[2025-07-02 06:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:27:02] local.INFO: Cron job executed {"time":"2025-07-02 06:27:02"} 
[2025-07-02 06:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:28:01] local.INFO: Cron job executed {"time":"2025-07-02 06:28:01"} 
[2025-07-02 06:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:28:02] local.INFO: Cron job executed {"time":"2025-07-02 06:28:02"} 
[2025-07-02 06:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:29:01] local.INFO: Cron job executed {"time":"2025-07-02 06:29:01"} 
[2025-07-02 06:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:29:02] local.INFO: Cron job executed {"time":"2025-07-02 06:29:02"} 
[2025-07-02 06:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:30:01] local.INFO: Cron job executed {"time":"2025-07-02 06:30:01"} 
[2025-07-02 06:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:30:02] local.INFO: Cron job executed {"time":"2025-07-02 06:30:02"} 
[2025-07-02 06:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:31:01] local.INFO: Cron job executed {"time":"2025-07-02 06:31:01"} 
[2025-07-02 06:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:31:03] local.INFO: Cron job executed {"time":"2025-07-02 06:31:03"} 
[2025-07-02 06:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:32:01] local.INFO: Cron job executed {"time":"2025-07-02 06:32:01"} 
[2025-07-02 06:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:32:02] local.INFO: Cron job executed {"time":"2025-07-02 06:32:02"} 
[2025-07-02 06:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:33:01] local.INFO: Cron job executed {"time":"2025-07-02 06:33:01"} 
[2025-07-02 06:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:33:02] local.INFO: Cron job executed {"time":"2025-07-02 06:33:02"} 
[2025-07-02 06:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:34:01] local.INFO: Cron job executed {"time":"2025-07-02 06:34:01"} 
[2025-07-02 06:34:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:34:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:34:03] local.INFO: Cron job executed {"time":"2025-07-02 06:34:03"} 
[2025-07-02 06:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:35:01] local.INFO: Cron job executed {"time":"2025-07-02 06:35:01"} 
[2025-07-02 06:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:35:02] local.INFO: Cron job executed {"time":"2025-07-02 06:35:02"} 
[2025-07-02 06:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:36:01] local.INFO: Cron job executed {"time":"2025-07-02 06:36:01"} 
[2025-07-02 06:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:36:02] local.INFO: Cron job executed {"time":"2025-07-02 06:36:02"} 
[2025-07-02 06:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:37:01] local.INFO: Cron job executed {"time":"2025-07-02 06:37:01"} 
[2025-07-02 06:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:37:02] local.INFO: Cron job executed {"time":"2025-07-02 06:37:02"} 
[2025-07-02 06:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:38:01] local.INFO: Cron job executed {"time":"2025-07-02 06:38:01"} 
[2025-07-02 06:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:38:02] local.INFO: Cron job executed {"time":"2025-07-02 06:38:02"} 
[2025-07-02 06:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:39:01] local.INFO: Cron job executed {"time":"2025-07-02 06:39:01"} 
[2025-07-02 06:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:39:02] local.INFO: Cron job executed {"time":"2025-07-02 06:39:02"} 
[2025-07-02 06:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:40:01] local.INFO: Cron job executed {"time":"2025-07-02 06:40:01"} 
[2025-07-02 06:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:40:02] local.INFO: Cron job executed {"time":"2025-07-02 06:40:02"} 
[2025-07-02 06:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:41:01] local.INFO: Cron job executed {"time":"2025-07-02 06:41:01"} 
[2025-07-02 06:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:41:02] local.INFO: Cron job executed {"time":"2025-07-02 06:41:02"} 
[2025-07-02 06:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:42:01] local.INFO: Cron job executed {"time":"2025-07-02 06:42:01"} 
[2025-07-02 06:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:42:02] local.INFO: Cron job executed {"time":"2025-07-02 06:42:02"} 
[2025-07-02 06:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:43:01] local.INFO: Cron job executed {"time":"2025-07-02 06:43:01"} 
[2025-07-02 06:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:43:02] local.INFO: Cron job executed {"time":"2025-07-02 06:43:02"} 
[2025-07-02 06:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:44:01] local.INFO: Cron job executed {"time":"2025-07-02 06:44:01"} 
[2025-07-02 06:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:44:02] local.INFO: Cron job executed {"time":"2025-07-02 06:44:02"} 
[2025-07-02 06:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:45:01] local.INFO: Cron job executed {"time":"2025-07-02 06:45:01"} 
[2025-07-02 06:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:45:02] local.INFO: Cron job executed {"time":"2025-07-02 06:45:02"} 
[2025-07-02 06:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:46:01] local.INFO: Cron job executed {"time":"2025-07-02 06:46:01"} 
[2025-07-02 06:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:46:02] local.INFO: Cron job executed {"time":"2025-07-02 06:46:02"} 
[2025-07-02 06:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:47:01] local.INFO: Cron job executed {"time":"2025-07-02 06:47:01"} 
[2025-07-02 06:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:47:02] local.INFO: Cron job executed {"time":"2025-07-02 06:47:02"} 
[2025-07-02 06:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:48:01] local.INFO: Cron job executed {"time":"2025-07-02 06:48:01"} 
[2025-07-02 06:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:48:02] local.INFO: Cron job executed {"time":"2025-07-02 06:48:02"} 
[2025-07-02 06:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:49:01] local.INFO: Cron job executed {"time":"2025-07-02 06:49:01"} 
[2025-07-02 06:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:49:02] local.INFO: Cron job executed {"time":"2025-07-02 06:49:02"} 
[2025-07-02 06:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:50:01] local.INFO: Cron job executed {"time":"2025-07-02 06:50:01"} 
[2025-07-02 06:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:50:02] local.INFO: Cron job executed {"time":"2025-07-02 06:50:02"} 
[2025-07-02 06:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:51:01] local.INFO: Cron job executed {"time":"2025-07-02 06:51:01"} 
[2025-07-02 06:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:51:02] local.INFO: Cron job executed {"time":"2025-07-02 06:51:02"} 
[2025-07-02 06:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:52:01] local.INFO: Cron job executed {"time":"2025-07-02 06:52:01"} 
[2025-07-02 06:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:52:02] local.INFO: Cron job executed {"time":"2025-07-02 06:52:02"} 
[2025-07-02 06:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:53:01] local.INFO: Cron job executed {"time":"2025-07-02 06:53:01"} 
[2025-07-02 06:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:53:02] local.INFO: Cron job executed {"time":"2025-07-02 06:53:02"} 
[2025-07-02 06:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:54:01] local.INFO: Cron job executed {"time":"2025-07-02 06:54:01"} 
[2025-07-02 06:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:54:02] local.INFO: Cron job executed {"time":"2025-07-02 06:54:02"} 
[2025-07-02 06:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:55:01] local.INFO: Cron job executed {"time":"2025-07-02 06:55:01"} 
[2025-07-02 06:55:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:55:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:55:03] local.INFO: Cron job executed {"time":"2025-07-02 06:55:03"} 
[2025-07-02 06:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:56:01] local.INFO: Cron job executed {"time":"2025-07-02 06:56:01"} 
[2025-07-02 06:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:56:02] local.INFO: Cron job executed {"time":"2025-07-02 06:56:02"} 
[2025-07-02 06:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:57:01] local.INFO: Cron job executed {"time":"2025-07-02 06:57:01"} 
[2025-07-02 06:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:57:02] local.INFO: Cron job executed {"time":"2025-07-02 06:57:02"} 
[2025-07-02 06:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:58:01] local.INFO: Cron job executed {"time":"2025-07-02 06:58:01"} 
[2025-07-02 06:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:58:02] local.INFO: Cron job executed {"time":"2025-07-02 06:58:02"} 
[2025-07-02 06:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:59:01] local.INFO: Cron job executed {"time":"2025-07-02 06:59:01"} 
[2025-07-02 06:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 06:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 06:59:02] local.INFO: Cron job executed {"time":"2025-07-02 06:59:02"} 
[2025-07-02 07:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:00:01] local.INFO: Cron job executed {"time":"2025-07-02 07:00:01"} 
[2025-07-02 07:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:00:02] local.INFO: Cron job executed {"time":"2025-07-02 07:00:02"} 
[2025-07-02 07:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:01:01] local.INFO: Cron job executed {"time":"2025-07-02 07:01:01"} 
[2025-07-02 07:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:01:02] local.INFO: Cron job executed {"time":"2025-07-02 07:01:02"} 
[2025-07-02 07:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:02:01] local.INFO: Cron job executed {"time":"2025-07-02 07:02:01"} 
[2025-07-02 07:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:02:02] local.INFO: Cron job executed {"time":"2025-07-02 07:02:02"} 
[2025-07-02 07:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:03:01] local.INFO: Cron job executed {"time":"2025-07-02 07:03:01"} 
[2025-07-02 07:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:03:02] local.INFO: Cron job executed {"time":"2025-07-02 07:03:02"} 
[2025-07-02 07:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:04:01] local.INFO: Cron job executed {"time":"2025-07-02 07:04:01"} 
[2025-07-02 07:04:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:04:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:04:03] local.INFO: Cron job executed {"time":"2025-07-02 07:04:03"} 
[2025-07-02 07:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:05:01] local.INFO: Cron job executed {"time":"2025-07-02 07:05:01"} 
[2025-07-02 07:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:05:02] local.INFO: Cron job executed {"time":"2025-07-02 07:05:02"} 
[2025-07-02 07:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:06:01] local.INFO: Cron job executed {"time":"2025-07-02 07:06:01"} 
[2025-07-02 07:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:06:02] local.INFO: Cron job executed {"time":"2025-07-02 07:06:02"} 
[2025-07-02 07:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:07:01] local.INFO: Cron job executed {"time":"2025-07-02 07:07:01"} 
[2025-07-02 07:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:07:02] local.INFO: Cron job executed {"time":"2025-07-02 07:07:02"} 
[2025-07-02 07:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:08:01] local.INFO: Cron job executed {"time":"2025-07-02 07:08:01"} 
[2025-07-02 07:08:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:08:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:08:03] local.INFO: Cron job executed {"time":"2025-07-02 07:08:03"} 
[2025-07-02 07:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:09:01] local.INFO: Cron job executed {"time":"2025-07-02 07:09:01"} 
[2025-07-02 07:09:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:09:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:09:03] local.INFO: Cron job executed {"time":"2025-07-02 07:09:03"} 
[2025-07-02 07:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:10:01] local.INFO: Cron job executed {"time":"2025-07-02 07:10:01"} 
[2025-07-02 07:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:10:02] local.INFO: Cron job executed {"time":"2025-07-02 07:10:02"} 
[2025-07-02 07:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:11:01] local.INFO: Cron job executed {"time":"2025-07-02 07:11:01"} 
[2025-07-02 07:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:11:02] local.INFO: Cron job executed {"time":"2025-07-02 07:11:02"} 
[2025-07-02 07:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:12:01] local.INFO: Cron job executed {"time":"2025-07-02 07:12:01"} 
[2025-07-02 07:12:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:12:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:12:03] local.INFO: Cron job executed {"time":"2025-07-02 07:12:03"} 
[2025-07-02 07:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:13:01] local.INFO: Cron job executed {"time":"2025-07-02 07:13:01"} 
[2025-07-02 07:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:13:02] local.INFO: Cron job executed {"time":"2025-07-02 07:13:02"} 
[2025-07-02 07:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:14:01] local.INFO: Cron job executed {"time":"2025-07-02 07:14:01"} 
[2025-07-02 07:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:14:02] local.INFO: Cron job executed {"time":"2025-07-02 07:14:02"} 
[2025-07-02 07:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:15:01] local.INFO: Cron job executed {"time":"2025-07-02 07:15:01"} 
[2025-07-02 07:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:15:02] local.INFO: Cron job executed {"time":"2025-07-02 07:15:02"} 
[2025-07-02 07:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:16:01] local.INFO: Cron job executed {"time":"2025-07-02 07:16:01"} 
[2025-07-02 07:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:16:02] local.INFO: Cron job executed {"time":"2025-07-02 07:16:02"} 
[2025-07-02 07:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:17:01] local.INFO: Cron job executed {"time":"2025-07-02 07:17:01"} 
[2025-07-02 07:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:17:02] local.INFO: Cron job executed {"time":"2025-07-02 07:17:02"} 
[2025-07-02 07:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:18:01] local.INFO: Cron job executed {"time":"2025-07-02 07:18:01"} 
[2025-07-02 07:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:18:02] local.INFO: Cron job executed {"time":"2025-07-02 07:18:02"} 
[2025-07-02 07:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:19:01] local.INFO: Cron job executed {"time":"2025-07-02 07:19:01"} 
[2025-07-02 07:19:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:19:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:19:02] local.INFO: Cron job executed {"time":"2025-07-02 07:19:02"} 
[2025-07-02 07:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:20:01] local.INFO: Cron job executed {"time":"2025-07-02 07:20:01"} 
[2025-07-02 07:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:20:02] local.INFO: Cron job executed {"time":"2025-07-02 07:20:02"} 
[2025-07-02 07:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:21:01] local.INFO: Cron job executed {"time":"2025-07-02 07:21:01"} 
[2025-07-02 07:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:21:02] local.INFO: Cron job executed {"time":"2025-07-02 07:21:02"} 
[2025-07-02 07:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:22:01] local.INFO: Cron job executed {"time":"2025-07-02 07:22:01"} 
[2025-07-02 07:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:22:02] local.INFO: Cron job executed {"time":"2025-07-02 07:22:02"} 
[2025-07-02 07:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:23:01] local.INFO: Cron job executed {"time":"2025-07-02 07:23:01"} 
[2025-07-02 07:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:23:03] local.INFO: Cron job executed {"time":"2025-07-02 07:23:03"} 
[2025-07-02 07:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:24:01] local.INFO: Cron job executed {"time":"2025-07-02 07:24:01"} 
[2025-07-02 07:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:24:02] local.INFO: Cron job executed {"time":"2025-07-02 07:24:02"} 
[2025-07-02 07:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:25:01] local.INFO: Cron job executed {"time":"2025-07-02 07:25:01"} 
[2025-07-02 07:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:25:02] local.INFO: Cron job executed {"time":"2025-07-02 07:25:02"} 
[2025-07-02 07:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:26:01] local.INFO: Cron job executed {"time":"2025-07-02 07:26:01"} 
[2025-07-02 07:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:26:02] local.INFO: Cron job executed {"time":"2025-07-02 07:26:02"} 
[2025-07-02 07:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:27:01] local.INFO: Cron job executed {"time":"2025-07-02 07:27:01"} 
[2025-07-02 07:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:27:02] local.INFO: Cron job executed {"time":"2025-07-02 07:27:02"} 
[2025-07-02 07:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:28:01] local.INFO: Cron job executed {"time":"2025-07-02 07:28:01"} 
[2025-07-02 07:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:28:02] local.INFO: Cron job executed {"time":"2025-07-02 07:28:02"} 
[2025-07-02 07:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:29:01] local.INFO: Cron job executed {"time":"2025-07-02 07:29:01"} 
[2025-07-02 07:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:29:02] local.INFO: Cron job executed {"time":"2025-07-02 07:29:02"} 
[2025-07-02 07:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:30:01] local.INFO: Cron job executed {"time":"2025-07-02 07:30:01"} 
[2025-07-02 07:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:30:02] local.INFO: Cron job executed {"time":"2025-07-02 07:30:02"} 
[2025-07-02 07:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:31:01] local.INFO: Cron job executed {"time":"2025-07-02 07:31:01"} 
[2025-07-02 07:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:31:02] local.INFO: Cron job executed {"time":"2025-07-02 07:31:02"} 
[2025-07-02 07:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:32:01] local.INFO: Cron job executed {"time":"2025-07-02 07:32:01"} 
[2025-07-02 07:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:32:02] local.INFO: Cron job executed {"time":"2025-07-02 07:32:02"} 
[2025-07-02 07:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:33:01] local.INFO: Cron job executed {"time":"2025-07-02 07:33:01"} 
[2025-07-02 07:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:33:02] local.INFO: Cron job executed {"time":"2025-07-02 07:33:02"} 
[2025-07-02 07:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:34:01] local.INFO: Cron job executed {"time":"2025-07-02 07:34:01"} 
[2025-07-02 07:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:34:02] local.INFO: Cron job executed {"time":"2025-07-02 07:34:02"} 
[2025-07-02 07:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:35:01] local.INFO: Cron job executed {"time":"2025-07-02 07:35:01"} 
[2025-07-02 07:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:35:02] local.INFO: Cron job executed {"time":"2025-07-02 07:35:02"} 
[2025-07-02 07:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:36:01] local.INFO: Cron job executed {"time":"2025-07-02 07:36:01"} 
[2025-07-02 07:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:36:02] local.INFO: Cron job executed {"time":"2025-07-02 07:36:02"} 
[2025-07-02 07:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:37:01] local.INFO: Cron job executed {"time":"2025-07-02 07:37:01"} 
[2025-07-02 07:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:37:02] local.INFO: Cron job executed {"time":"2025-07-02 07:37:02"} 
[2025-07-02 07:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:38:01] local.INFO: Cron job executed {"time":"2025-07-02 07:38:01"} 
[2025-07-02 07:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:38:03] local.INFO: Cron job executed {"time":"2025-07-02 07:38:03"} 
[2025-07-02 07:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:39:01] local.INFO: Cron job executed {"time":"2025-07-02 07:39:01"} 
[2025-07-02 07:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:39:02] local.INFO: Cron job executed {"time":"2025-07-02 07:39:02"} 
[2025-07-02 07:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:40:01] local.INFO: Cron job executed {"time":"2025-07-02 07:40:01"} 
[2025-07-02 07:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:40:02] local.INFO: Cron job executed {"time":"2025-07-02 07:40:02"} 
[2025-07-02 07:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:41:01] local.INFO: Cron job executed {"time":"2025-07-02 07:41:01"} 
[2025-07-02 07:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:41:02] local.INFO: Cron job executed {"time":"2025-07-02 07:41:02"} 
[2025-07-02 07:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:42:01] local.INFO: Cron job executed {"time":"2025-07-02 07:42:01"} 
[2025-07-02 07:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:42:03] local.INFO: Cron job executed {"time":"2025-07-02 07:42:03"} 
[2025-07-02 07:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:43:01] local.INFO: Cron job executed {"time":"2025-07-02 07:43:01"} 
[2025-07-02 07:43:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:43:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:43:03] local.INFO: Cron job executed {"time":"2025-07-02 07:43:03"} 
[2025-07-02 07:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:44:01] local.INFO: Cron job executed {"time":"2025-07-02 07:44:01"} 
[2025-07-02 07:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:44:02] local.INFO: Cron job executed {"time":"2025-07-02 07:44:02"} 
[2025-07-02 07:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:45:01] local.INFO: Cron job executed {"time":"2025-07-02 07:45:01"} 
[2025-07-02 07:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:45:02] local.INFO: Cron job executed {"time":"2025-07-02 07:45:02"} 
[2025-07-02 07:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:46:01] local.INFO: Cron job executed {"time":"2025-07-02 07:46:01"} 
[2025-07-02 07:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:46:02] local.INFO: Cron job executed {"time":"2025-07-02 07:46:02"} 
[2025-07-02 07:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:47:01] local.INFO: Cron job executed {"time":"2025-07-02 07:47:01"} 
[2025-07-02 07:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:47:03] local.INFO: Cron job executed {"time":"2025-07-02 07:47:03"} 
[2025-07-02 07:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:48:01] local.INFO: Cron job executed {"time":"2025-07-02 07:48:01"} 
[2025-07-02 07:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:48:02] local.INFO: Cron job executed {"time":"2025-07-02 07:48:02"} 
[2025-07-02 07:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:49:01] local.INFO: Cron job executed {"time":"2025-07-02 07:49:01"} 
[2025-07-02 07:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:49:02] local.INFO: Cron job executed {"time":"2025-07-02 07:49:02"} 
[2025-07-02 07:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:50:01] local.INFO: Cron job executed {"time":"2025-07-02 07:50:01"} 
[2025-07-02 07:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:50:02] local.INFO: Cron job executed {"time":"2025-07-02 07:50:02"} 
[2025-07-02 07:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:51:01] local.INFO: Cron job executed {"time":"2025-07-02 07:51:01"} 
[2025-07-02 07:51:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:51:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:51:03] local.INFO: Cron job executed {"time":"2025-07-02 07:51:03"} 
[2025-07-02 07:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:52:01] local.INFO: Cron job executed {"time":"2025-07-02 07:52:01"} 
[2025-07-02 07:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:52:02] local.INFO: Cron job executed {"time":"2025-07-02 07:52:02"} 
[2025-07-02 07:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:53:01] local.INFO: Cron job executed {"time":"2025-07-02 07:53:01"} 
[2025-07-02 07:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:53:03] local.INFO: Cron job executed {"time":"2025-07-02 07:53:03"} 
[2025-07-02 07:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:54:01] local.INFO: Cron job executed {"time":"2025-07-02 07:54:01"} 
[2025-07-02 07:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:54:02] local.INFO: Cron job executed {"time":"2025-07-02 07:54:02"} 
[2025-07-02 07:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:55:01] local.INFO: Cron job executed {"time":"2025-07-02 07:55:01"} 
[2025-07-02 07:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:55:02] local.INFO: Cron job executed {"time":"2025-07-02 07:55:02"} 
[2025-07-02 07:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:56:01] local.INFO: Cron job executed {"time":"2025-07-02 07:56:01"} 
[2025-07-02 07:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:56:02] local.INFO: Cron job executed {"time":"2025-07-02 07:56:02"} 
[2025-07-02 07:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:57:01] local.INFO: Cron job executed {"time":"2025-07-02 07:57:01"} 
[2025-07-02 07:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:57:02] local.INFO: Cron job executed {"time":"2025-07-02 07:57:02"} 
[2025-07-02 07:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:58:01] local.INFO: Cron job executed {"time":"2025-07-02 07:58:01"} 
[2025-07-02 07:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:58:02] local.INFO: Cron job executed {"time":"2025-07-02 07:58:02"} 
[2025-07-02 07:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:59:01] local.INFO: Cron job executed {"time":"2025-07-02 07:59:01"} 
[2025-07-02 07:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 07:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 07:59:02] local.INFO: Cron job executed {"time":"2025-07-02 07:59:02"} 
[2025-07-02 08:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:00:01] local.INFO: Cron job executed {"time":"2025-07-02 08:00:01"} 
[2025-07-02 08:00:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:00:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:00:03] local.INFO: Cron job executed {"time":"2025-07-02 08:00:03"} 
[2025-07-02 08:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:01:01] local.INFO: Cron job executed {"time":"2025-07-02 08:01:01"} 
[2025-07-02 08:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:01:02] local.INFO: Cron job executed {"time":"2025-07-02 08:01:02"} 
[2025-07-02 08:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:02:01] local.INFO: Cron job executed {"time":"2025-07-02 08:02:01"} 
[2025-07-02 08:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:02:02] local.INFO: Cron job executed {"time":"2025-07-02 08:02:02"} 
[2025-07-02 08:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:03:01] local.INFO: Cron job executed {"time":"2025-07-02 08:03:01"} 
[2025-07-02 08:03:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:03:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:03:03] local.INFO: Cron job executed {"time":"2025-07-02 08:03:03"} 
[2025-07-02 08:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:04:01] local.INFO: Cron job executed {"time":"2025-07-02 08:04:01"} 
[2025-07-02 08:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:04:02] local.INFO: Cron job executed {"time":"2025-07-02 08:04:02"} 
[2025-07-02 08:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:05:01] local.INFO: Cron job executed {"time":"2025-07-02 08:05:01"} 
[2025-07-02 08:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:05:02] local.INFO: Cron job executed {"time":"2025-07-02 08:05:02"} 
[2025-07-02 08:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:06:01] local.INFO: Cron job executed {"time":"2025-07-02 08:06:01"} 
[2025-07-02 08:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:06:02] local.INFO: Cron job executed {"time":"2025-07-02 08:06:02"} 
[2025-07-02 08:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:07:01] local.INFO: Cron job executed {"time":"2025-07-02 08:07:01"} 
[2025-07-02 08:07:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:07:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:07:03] local.INFO: Cron job executed {"time":"2025-07-02 08:07:03"} 
[2025-07-02 08:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:08:01] local.INFO: Cron job executed {"time":"2025-07-02 08:08:01"} 
[2025-07-02 08:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:08:02] local.INFO: Cron job executed {"time":"2025-07-02 08:08:02"} 
[2025-07-02 08:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:09:01] local.INFO: Cron job executed {"time":"2025-07-02 08:09:01"} 
[2025-07-02 08:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:09:02] local.INFO: Cron job executed {"time":"2025-07-02 08:09:02"} 
[2025-07-02 08:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:10:01] local.INFO: Cron job executed {"time":"2025-07-02 08:10:01"} 
[2025-07-02 08:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:10:02] local.INFO: Cron job executed {"time":"2025-07-02 08:10:02"} 
[2025-07-02 08:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:11:01] local.INFO: Cron job executed {"time":"2025-07-02 08:11:01"} 
[2025-07-02 08:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:11:02] local.INFO: Cron job executed {"time":"2025-07-02 08:11:02"} 
[2025-07-02 08:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:12:01] local.INFO: Cron job executed {"time":"2025-07-02 08:12:01"} 
[2025-07-02 08:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:12:02] local.INFO: Cron job executed {"time":"2025-07-02 08:12:02"} 
[2025-07-02 08:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:13:01] local.INFO: Cron job executed {"time":"2025-07-02 08:13:01"} 
[2025-07-02 08:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:13:02] local.INFO: Cron job executed {"time":"2025-07-02 08:13:02"} 
[2025-07-02 08:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:14:01] local.INFO: Cron job executed {"time":"2025-07-02 08:14:01"} 
[2025-07-02 08:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:14:02] local.INFO: Cron job executed {"time":"2025-07-02 08:14:02"} 
[2025-07-02 08:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:15:01] local.INFO: Cron job executed {"time":"2025-07-02 08:15:01"} 
[2025-07-02 08:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:15:02] local.INFO: Cron job executed {"time":"2025-07-02 08:15:02"} 
[2025-07-02 08:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:16:01] local.INFO: Cron job executed {"time":"2025-07-02 08:16:01"} 
[2025-07-02 08:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:16:02] local.INFO: Cron job executed {"time":"2025-07-02 08:16:02"} 
[2025-07-02 08:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:17:01] local.INFO: Cron job executed {"time":"2025-07-02 08:17:01"} 
[2025-07-02 08:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:17:02] local.INFO: Cron job executed {"time":"2025-07-02 08:17:02"} 
[2025-07-02 08:18:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:18:02] local.INFO: Cron job executed {"time":"2025-07-02 08:18:02"} 
[2025-07-02 08:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:18:03] local.INFO: Cron job executed {"time":"2025-07-02 08:18:03"} 
[2025-07-02 08:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:19:01] local.INFO: Cron job executed {"time":"2025-07-02 08:19:01"} 
[2025-07-02 08:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:19:03] local.INFO: Cron job executed {"time":"2025-07-02 08:19:03"} 
[2025-07-02 08:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:20:01] local.INFO: Cron job executed {"time":"2025-07-02 08:20:01"} 
[2025-07-02 08:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:20:02] local.INFO: Cron job executed {"time":"2025-07-02 08:20:02"} 
[2025-07-02 08:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:21:01] local.INFO: Cron job executed {"time":"2025-07-02 08:21:01"} 
[2025-07-02 08:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:21:02] local.INFO: Cron job executed {"time":"2025-07-02 08:21:02"} 
[2025-07-02 08:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:22:01] local.INFO: Cron job executed {"time":"2025-07-02 08:22:01"} 
[2025-07-02 08:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:22:02] local.INFO: Cron job executed {"time":"2025-07-02 08:22:02"} 
[2025-07-02 08:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:23:01] local.INFO: Cron job executed {"time":"2025-07-02 08:23:01"} 
[2025-07-02 08:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:23:02] local.INFO: Cron job executed {"time":"2025-07-02 08:23:02"} 
[2025-07-02 08:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:24:01] local.INFO: Cron job executed {"time":"2025-07-02 08:24:01"} 
[2025-07-02 08:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:24:02] local.INFO: Cron job executed {"time":"2025-07-02 08:24:02"} 
[2025-07-02 08:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:25:01] local.INFO: Cron job executed {"time":"2025-07-02 08:25:01"} 
[2025-07-02 08:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:25:02] local.INFO: Cron job executed {"time":"2025-07-02 08:25:02"} 
[2025-07-02 08:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:26:01] local.INFO: Cron job executed {"time":"2025-07-02 08:26:01"} 
[2025-07-02 08:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:26:02] local.INFO: Cron job executed {"time":"2025-07-02 08:26:02"} 
[2025-07-02 08:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:27:01] local.INFO: Cron job executed {"time":"2025-07-02 08:27:01"} 
[2025-07-02 08:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:27:02] local.INFO: Cron job executed {"time":"2025-07-02 08:27:02"} 
[2025-07-02 08:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:28:01] local.INFO: Cron job executed {"time":"2025-07-02 08:28:01"} 
[2025-07-02 08:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:28:02] local.INFO: Cron job executed {"time":"2025-07-02 08:28:02"} 
[2025-07-02 08:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:29:01] local.INFO: Cron job executed {"time":"2025-07-02 08:29:01"} 
[2025-07-02 08:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:29:02] local.INFO: Cron job executed {"time":"2025-07-02 08:29:02"} 
[2025-07-02 08:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:30:01] local.INFO: Cron job executed {"time":"2025-07-02 08:30:01"} 
[2025-07-02 08:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:30:03] local.INFO: Cron job executed {"time":"2025-07-02 08:30:03"} 
[2025-07-02 08:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:31:01] local.INFO: Cron job executed {"time":"2025-07-02 08:31:01"} 
[2025-07-02 08:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:31:02] local.INFO: Cron job executed {"time":"2025-07-02 08:31:02"} 
[2025-07-02 08:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:32:01] local.INFO: Cron job executed {"time":"2025-07-02 08:32:01"} 
[2025-07-02 08:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:32:02] local.INFO: Cron job executed {"time":"2025-07-02 08:32:02"} 
[2025-07-02 08:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:33:01] local.INFO: Cron job executed {"time":"2025-07-02 08:33:01"} 
[2025-07-02 08:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:33:02] local.INFO: Cron job executed {"time":"2025-07-02 08:33:02"} 
[2025-07-02 08:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:34:01] local.INFO: Cron job executed {"time":"2025-07-02 08:34:01"} 
[2025-07-02 08:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:34:02] local.INFO: Cron job executed {"time":"2025-07-02 08:34:02"} 
[2025-07-02 08:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:35:01] local.INFO: Cron job executed {"time":"2025-07-02 08:35:01"} 
[2025-07-02 08:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:35:02] local.INFO: Cron job executed {"time":"2025-07-02 08:35:02"} 
[2025-07-02 08:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:36:01] local.INFO: Cron job executed {"time":"2025-07-02 08:36:01"} 
[2025-07-02 08:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:36:02] local.INFO: Cron job executed {"time":"2025-07-02 08:36:02"} 
[2025-07-02 08:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:37:01] local.INFO: Cron job executed {"time":"2025-07-02 08:37:01"} 
[2025-07-02 08:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:37:02] local.INFO: Cron job executed {"time":"2025-07-02 08:37:02"} 
[2025-07-02 08:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:38:01] local.INFO: Cron job executed {"time":"2025-07-02 08:38:01"} 
[2025-07-02 08:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:38:02] local.INFO: Cron job executed {"time":"2025-07-02 08:38:02"} 
[2025-07-02 08:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:39:01] local.INFO: Cron job executed {"time":"2025-07-02 08:39:01"} 
[2025-07-02 08:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:39:02] local.INFO: Cron job executed {"time":"2025-07-02 08:39:02"} 
[2025-07-02 08:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:40:01] local.INFO: Cron job executed {"time":"2025-07-02 08:40:01"} 
[2025-07-02 08:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:40:02] local.INFO: Cron job executed {"time":"2025-07-02 08:40:02"} 
[2025-07-02 08:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:41:01] local.INFO: Cron job executed {"time":"2025-07-02 08:41:01"} 
[2025-07-02 08:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:41:02] local.INFO: Cron job executed {"time":"2025-07-02 08:41:02"} 
[2025-07-02 08:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:42:01] local.INFO: Cron job executed {"time":"2025-07-02 08:42:01"} 
[2025-07-02 08:42:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:42:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:42:03] local.INFO: Cron job executed {"time":"2025-07-02 08:42:03"} 
[2025-07-02 08:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:43:01] local.INFO: Cron job executed {"time":"2025-07-02 08:43:01"} 
[2025-07-02 08:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:43:02] local.INFO: Cron job executed {"time":"2025-07-02 08:43:02"} 
[2025-07-02 08:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:44:01] local.INFO: Cron job executed {"time":"2025-07-02 08:44:01"} 
[2025-07-02 08:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:44:02] local.INFO: Cron job executed {"time":"2025-07-02 08:44:02"} 
[2025-07-02 08:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:45:01] local.INFO: Cron job executed {"time":"2025-07-02 08:45:01"} 
[2025-07-02 08:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:45:02] local.INFO: Cron job executed {"time":"2025-07-02 08:45:02"} 
[2025-07-02 08:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:46:01] local.INFO: Cron job executed {"time":"2025-07-02 08:46:01"} 
[2025-07-02 08:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:46:02] local.INFO: Cron job executed {"time":"2025-07-02 08:46:02"} 
[2025-07-02 08:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:47:01] local.INFO: Cron job executed {"time":"2025-07-02 08:47:01"} 
[2025-07-02 08:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:47:02] local.INFO: Cron job executed {"time":"2025-07-02 08:47:02"} 
[2025-07-02 08:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:48:01] local.INFO: Cron job executed {"time":"2025-07-02 08:48:01"} 
[2025-07-02 08:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:48:02] local.INFO: Cron job executed {"time":"2025-07-02 08:48:02"} 
[2025-07-02 08:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:49:01] local.INFO: Cron job executed {"time":"2025-07-02 08:49:01"} 
[2025-07-02 08:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:49:02] local.INFO: Cron job executed {"time":"2025-07-02 08:49:02"} 
[2025-07-02 08:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:50:01] local.INFO: Cron job executed {"time":"2025-07-02 08:50:01"} 
[2025-07-02 08:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:50:02] local.INFO: Cron job executed {"time":"2025-07-02 08:50:02"} 
[2025-07-02 08:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:51:01] local.INFO: Cron job executed {"time":"2025-07-02 08:51:01"} 
[2025-07-02 08:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:51:02] local.INFO: Cron job executed {"time":"2025-07-02 08:51:02"} 
[2025-07-02 08:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:52:01] local.INFO: Cron job executed {"time":"2025-07-02 08:52:01"} 
[2025-07-02 08:52:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:52:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:52:03] local.INFO: Cron job executed {"time":"2025-07-02 08:52:03"} 
[2025-07-02 08:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:53:01] local.INFO: Cron job executed {"time":"2025-07-02 08:53:01"} 
[2025-07-02 08:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:53:03] local.INFO: Cron job executed {"time":"2025-07-02 08:53:03"} 
[2025-07-02 08:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:54:01] local.INFO: Cron job executed {"time":"2025-07-02 08:54:01"} 
[2025-07-02 08:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:54:02] local.INFO: Cron job executed {"time":"2025-07-02 08:54:02"} 
[2025-07-02 08:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:55:01] local.INFO: Cron job executed {"time":"2025-07-02 08:55:01"} 
[2025-07-02 08:55:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:55:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:55:03] local.INFO: Cron job executed {"time":"2025-07-02 08:55:03"} 
[2025-07-02 08:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:56:01] local.INFO: Cron job executed {"time":"2025-07-02 08:56:01"} 
[2025-07-02 08:56:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:56:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:56:03] local.INFO: Cron job executed {"time":"2025-07-02 08:56:03"} 
[2025-07-02 08:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:57:01] local.INFO: Cron job executed {"time":"2025-07-02 08:57:01"} 
[2025-07-02 08:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:57:02] local.INFO: Cron job executed {"time":"2025-07-02 08:57:02"} 
[2025-07-02 08:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:58:01] local.INFO: Cron job executed {"time":"2025-07-02 08:58:01"} 
[2025-07-02 08:58:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:58:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:58:03] local.INFO: Cron job executed {"time":"2025-07-02 08:58:03"} 
[2025-07-02 08:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:59:01] local.INFO: Cron job executed {"time":"2025-07-02 08:59:01"} 
[2025-07-02 08:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 08:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 08:59:02] local.INFO: Cron job executed {"time":"2025-07-02 08:59:02"} 
[2025-07-02 09:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:00:01] local.INFO: Cron job executed {"time":"2025-07-02 09:00:01"} 
[2025-07-02 09:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:00:02] local.INFO: Cron job executed {"time":"2025-07-02 09:00:02"} 
[2025-07-02 09:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:01:01] local.INFO: Cron job executed {"time":"2025-07-02 09:01:01"} 
[2025-07-02 09:01:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:01:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:01:03] local.INFO: Cron job executed {"time":"2025-07-02 09:01:03"} 
[2025-07-02 09:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:02:01] local.INFO: Cron job executed {"time":"2025-07-02 09:02:01"} 
[2025-07-02 09:02:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:02:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:02:03] local.INFO: Cron job executed {"time":"2025-07-02 09:02:03"} 
[2025-07-02 09:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:03:01] local.INFO: Cron job executed {"time":"2025-07-02 09:03:01"} 
[2025-07-02 09:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:03:02] local.INFO: Cron job executed {"time":"2025-07-02 09:03:02"} 
[2025-07-02 09:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:04:01] local.INFO: Cron job executed {"time":"2025-07-02 09:04:01"} 
[2025-07-02 09:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:04:02] local.INFO: Cron job executed {"time":"2025-07-02 09:04:02"} 
[2025-07-02 09:05:11] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:05:11] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:05:11] local.INFO: Cron job executed {"time":"2025-07-02 09:05:11"} 
[2025-07-02 09:05:15] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:05:15] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:05:15] local.INFO: Cron job executed {"time":"2025-07-02 09:05:15"} 
[2025-07-02 09:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:06:01] local.INFO: Cron job executed {"time":"2025-07-02 09:06:01"} 
[2025-07-02 09:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:06:02] local.INFO: Cron job executed {"time":"2025-07-02 09:06:02"} 
[2025-07-02 09:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:07:01] local.INFO: Cron job executed {"time":"2025-07-02 09:07:01"} 
[2025-07-02 09:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:07:02] local.INFO: Cron job executed {"time":"2025-07-02 09:07:02"} 
[2025-07-02 09:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:08:01] local.INFO: Cron job executed {"time":"2025-07-02 09:08:01"} 
[2025-07-02 09:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:08:02] local.INFO: Cron job executed {"time":"2025-07-02 09:08:02"} 
[2025-07-02 09:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:09:01] local.INFO: Cron job executed {"time":"2025-07-02 09:09:01"} 
[2025-07-02 09:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:09:02] local.INFO: Cron job executed {"time":"2025-07-02 09:09:02"} 
[2025-07-02 09:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:10:01] local.INFO: Cron job executed {"time":"2025-07-02 09:10:01"} 
[2025-07-02 09:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:10:02] local.INFO: Cron job executed {"time":"2025-07-02 09:10:02"} 
[2025-07-02 09:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:11:01] local.INFO: Cron job executed {"time":"2025-07-02 09:11:01"} 
[2025-07-02 09:11:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:11:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:11:02] local.INFO: Cron job executed {"time":"2025-07-02 09:11:02"} 
[2025-07-02 09:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:12:01] local.INFO: Cron job executed {"time":"2025-07-02 09:12:01"} 
[2025-07-02 09:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:12:02] local.INFO: Cron job executed {"time":"2025-07-02 09:12:02"} 
[2025-07-02 09:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:13:01] local.INFO: Cron job executed {"time":"2025-07-02 09:13:01"} 
[2025-07-02 09:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:13:02] local.INFO: Cron job executed {"time":"2025-07-02 09:13:02"} 
[2025-07-02 09:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:14:01] local.INFO: Cron job executed {"time":"2025-07-02 09:14:01"} 
[2025-07-02 09:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:14:02] local.INFO: Cron job executed {"time":"2025-07-02 09:14:02"} 
[2025-07-02 09:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:15:01] local.INFO: Cron job executed {"time":"2025-07-02 09:15:01"} 
[2025-07-02 09:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:15:02] local.INFO: Cron job executed {"time":"2025-07-02 09:15:02"} 
[2025-07-02 09:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:16:01] local.INFO: Cron job executed {"time":"2025-07-02 09:16:01"} 
[2025-07-02 09:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:16:02] local.INFO: Cron job executed {"time":"2025-07-02 09:16:02"} 
[2025-07-02 09:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:17:01] local.INFO: Cron job executed {"time":"2025-07-02 09:17:01"} 
[2025-07-02 09:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:17:02] local.INFO: Cron job executed {"time":"2025-07-02 09:17:02"} 
[2025-07-02 09:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:18:01] local.INFO: Cron job executed {"time":"2025-07-02 09:18:01"} 
[2025-07-02 09:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:18:02] local.INFO: Cron job executed {"time":"2025-07-02 09:18:02"} 
[2025-07-02 09:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:19:01] local.INFO: Cron job executed {"time":"2025-07-02 09:19:01"} 
[2025-07-02 09:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:19:03] local.INFO: Cron job executed {"time":"2025-07-02 09:19:03"} 
[2025-07-02 09:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:20:01] local.INFO: Cron job executed {"time":"2025-07-02 09:20:01"} 
[2025-07-02 09:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:20:02] local.INFO: Cron job executed {"time":"2025-07-02 09:20:02"} 
[2025-07-02 09:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:21:01] local.INFO: Cron job executed {"time":"2025-07-02 09:21:01"} 
[2025-07-02 09:21:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:21:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:21:03] local.INFO: Cron job executed {"time":"2025-07-02 09:21:03"} 
[2025-07-02 09:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:22:01] local.INFO: Cron job executed {"time":"2025-07-02 09:22:01"} 
[2025-07-02 09:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:22:02] local.INFO: Cron job executed {"time":"2025-07-02 09:22:02"} 
[2025-07-02 09:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:23:01] local.INFO: Cron job executed {"time":"2025-07-02 09:23:01"} 
[2025-07-02 09:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:23:03] local.INFO: Cron job executed {"time":"2025-07-02 09:23:03"} 
[2025-07-02 09:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:24:01] local.INFO: Cron job executed {"time":"2025-07-02 09:24:01"} 
[2025-07-02 09:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:24:02] local.INFO: Cron job executed {"time":"2025-07-02 09:24:02"} 
[2025-07-02 09:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:25:01] local.INFO: Cron job executed {"time":"2025-07-02 09:25:01"} 
[2025-07-02 09:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:25:02] local.INFO: Cron job executed {"time":"2025-07-02 09:25:02"} 
[2025-07-02 09:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:26:01] local.INFO: Cron job executed {"time":"2025-07-02 09:26:01"} 
[2025-07-02 09:26:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:26:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:26:03] local.INFO: Cron job executed {"time":"2025-07-02 09:26:03"} 
[2025-07-02 09:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:27:01] local.INFO: Cron job executed {"time":"2025-07-02 09:27:01"} 
[2025-07-02 09:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:27:02] local.INFO: Cron job executed {"time":"2025-07-02 09:27:02"} 
[2025-07-02 09:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:28:01] local.INFO: Cron job executed {"time":"2025-07-02 09:28:01"} 
[2025-07-02 09:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:28:02] local.INFO: Cron job executed {"time":"2025-07-02 09:28:02"} 
[2025-07-02 09:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:29:01] local.INFO: Cron job executed {"time":"2025-07-02 09:29:01"} 
[2025-07-02 09:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:29:02] local.INFO: Cron job executed {"time":"2025-07-02 09:29:02"} 
[2025-07-02 09:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:30:01] local.INFO: Cron job executed {"time":"2025-07-02 09:30:01"} 
[2025-07-02 09:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:30:02] local.INFO: Cron job executed {"time":"2025-07-02 09:30:02"} 
[2025-07-02 09:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:31:01] local.INFO: Cron job executed {"time":"2025-07-02 09:31:01"} 
[2025-07-02 09:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:31:02] local.INFO: Cron job executed {"time":"2025-07-02 09:31:02"} 
[2025-07-02 09:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:32:01] local.INFO: Cron job executed {"time":"2025-07-02 09:32:01"} 
[2025-07-02 09:32:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:32:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:32:02] local.INFO: Cron job executed {"time":"2025-07-02 09:32:02"} 
[2025-07-02 09:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:33:01] local.INFO: Cron job executed {"time":"2025-07-02 09:33:01"} 
[2025-07-02 09:33:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:33:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:33:02] local.INFO: Cron job executed {"time":"2025-07-02 09:33:02"} 
[2025-07-02 09:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:34:01] local.INFO: Cron job executed {"time":"2025-07-02 09:34:01"} 
[2025-07-02 09:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:34:02] local.INFO: Cron job executed {"time":"2025-07-02 09:34:02"} 
[2025-07-02 09:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:35:01] local.INFO: Cron job executed {"time":"2025-07-02 09:35:01"} 
[2025-07-02 09:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:35:02] local.INFO: Cron job executed {"time":"2025-07-02 09:35:02"} 
[2025-07-02 09:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:36:01] local.INFO: Cron job executed {"time":"2025-07-02 09:36:01"} 
[2025-07-02 09:36:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:36:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:36:02] local.INFO: Cron job executed {"time":"2025-07-02 09:36:02"} 
[2025-07-02 09:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:37:01] local.INFO: Cron job executed {"time":"2025-07-02 09:37:01"} 
[2025-07-02 09:37:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:37:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:37:02] local.INFO: Cron job executed {"time":"2025-07-02 09:37:02"} 
[2025-07-02 09:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:38:01] local.INFO: Cron job executed {"time":"2025-07-02 09:38:01"} 
[2025-07-02 09:38:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:38:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:38:02] local.INFO: Cron job executed {"time":"2025-07-02 09:38:02"} 
[2025-07-02 09:39:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:39:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:39:01] local.INFO: Cron job executed {"time":"2025-07-02 09:39:01"} 
[2025-07-02 09:39:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:39:02] local.INFO: Cron job executed {"time":"2025-07-02 09:39:02"} 
[2025-07-02 09:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:40:01] local.INFO: Cron job executed {"time":"2025-07-02 09:40:01"} 
[2025-07-02 09:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:40:02] local.INFO: Cron job executed {"time":"2025-07-02 09:40:02"} 
[2025-07-02 09:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:41:01] local.INFO: Cron job executed {"time":"2025-07-02 09:41:01"} 
[2025-07-02 09:41:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:41:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:41:02] local.INFO: Cron job executed {"time":"2025-07-02 09:41:02"} 
[2025-07-02 09:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:42:01] local.INFO: Cron job executed {"time":"2025-07-02 09:42:01"} 
[2025-07-02 09:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:42:02] local.INFO: Cron job executed {"time":"2025-07-02 09:42:02"} 
[2025-07-02 09:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:43:01] local.INFO: Cron job executed {"time":"2025-07-02 09:43:01"} 
[2025-07-02 09:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:43:02] local.INFO: Cron job executed {"time":"2025-07-02 09:43:02"} 
[2025-07-02 09:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:44:01] local.INFO: Cron job executed {"time":"2025-07-02 09:44:01"} 
[2025-07-02 09:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:44:02] local.INFO: Cron job executed {"time":"2025-07-02 09:44:02"} 
[2025-07-02 09:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:45:01] local.INFO: Cron job executed {"time":"2025-07-02 09:45:01"} 
[2025-07-02 09:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:45:02] local.INFO: Cron job executed {"time":"2025-07-02 09:45:02"} 
[2025-07-02 09:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:46:01] local.INFO: Cron job executed {"time":"2025-07-02 09:46:01"} 
[2025-07-02 09:46:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:46:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:46:02] local.INFO: Cron job executed {"time":"2025-07-02 09:46:02"} 
[2025-07-02 09:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:47:01] local.INFO: Cron job executed {"time":"2025-07-02 09:47:01"} 
[2025-07-02 09:47:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:47:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:47:03] local.INFO: Cron job executed {"time":"2025-07-02 09:47:03"} 
[2025-07-02 09:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:48:01] local.INFO: Cron job executed {"time":"2025-07-02 09:48:01"} 
[2025-07-02 09:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:48:02] local.INFO: Cron job executed {"time":"2025-07-02 09:48:02"} 
[2025-07-02 09:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:49:01] local.INFO: Cron job executed {"time":"2025-07-02 09:49:01"} 
[2025-07-02 09:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:49:02] local.INFO: Cron job executed {"time":"2025-07-02 09:49:02"} 
[2025-07-02 09:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:50:01] local.INFO: Cron job executed {"time":"2025-07-02 09:50:01"} 
[2025-07-02 09:50:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:50:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:50:02] local.INFO: Cron job executed {"time":"2025-07-02 09:50:02"} 
[2025-07-02 09:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:51:01] local.INFO: Cron job executed {"time":"2025-07-02 09:51:01"} 
[2025-07-02 09:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:51:02] local.INFO: Cron job executed {"time":"2025-07-02 09:51:02"} 
[2025-07-02 09:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:52:01] local.INFO: Cron job executed {"time":"2025-07-02 09:52:01"} 
[2025-07-02 09:52:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:52:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:52:02] local.INFO: Cron job executed {"time":"2025-07-02 09:52:02"} 
[2025-07-02 09:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:53:01] local.INFO: Cron job executed {"time":"2025-07-02 09:53:01"} 
[2025-07-02 09:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:53:02] local.INFO: Cron job executed {"time":"2025-07-02 09:53:02"} 
[2025-07-02 09:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:54:01] local.INFO: Cron job executed {"time":"2025-07-02 09:54:01"} 
[2025-07-02 09:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:54:02] local.INFO: Cron job executed {"time":"2025-07-02 09:54:02"} 
[2025-07-02 09:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:55:01] local.INFO: Cron job executed {"time":"2025-07-02 09:55:01"} 
[2025-07-02 09:55:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:55:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:55:02] local.INFO: Cron job executed {"time":"2025-07-02 09:55:02"} 
[2025-07-02 09:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:56:01] local.INFO: Cron job executed {"time":"2025-07-02 09:56:01"} 
[2025-07-02 09:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:56:02] local.INFO: Cron job executed {"time":"2025-07-02 09:56:02"} 
[2025-07-02 09:57:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:57:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:57:01] local.INFO: Cron job executed {"time":"2025-07-02 09:57:01"} 
[2025-07-02 09:57:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:57:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:57:02] local.INFO: Cron job executed {"time":"2025-07-02 09:57:02"} 
[2025-07-02 09:58:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:58:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:58:01] local.INFO: Cron job executed {"time":"2025-07-02 09:58:01"} 
[2025-07-02 09:58:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:58:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:58:02] local.INFO: Cron job executed {"time":"2025-07-02 09:58:02"} 
[2025-07-02 09:59:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:59:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:59:01] local.INFO: Cron job executed {"time":"2025-07-02 09:59:01"} 
[2025-07-02 09:59:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 09:59:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 09:59:02] local.INFO: Cron job executed {"time":"2025-07-02 09:59:02"} 
[2025-07-02 10:00:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:00:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:00:01] local.INFO: Cron job executed {"time":"2025-07-02 10:00:01"} 
[2025-07-02 10:00:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:00:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:00:02] local.INFO: Cron job executed {"time":"2025-07-02 10:00:02"} 
[2025-07-02 10:01:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:01:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:01:01] local.INFO: Cron job executed {"time":"2025-07-02 10:01:01"} 
[2025-07-02 10:01:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:01:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:01:02] local.INFO: Cron job executed {"time":"2025-07-02 10:01:02"} 
[2025-07-02 10:02:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:02:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:02:01] local.INFO: Cron job executed {"time":"2025-07-02 10:02:01"} 
[2025-07-02 10:02:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:02:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:02:02] local.INFO: Cron job executed {"time":"2025-07-02 10:02:02"} 
[2025-07-02 10:03:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:03:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:03:01] local.INFO: Cron job executed {"time":"2025-07-02 10:03:01"} 
[2025-07-02 10:03:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:03:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:03:02] local.INFO: Cron job executed {"time":"2025-07-02 10:03:02"} 
[2025-07-02 10:04:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:04:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:04:01] local.INFO: Cron job executed {"time":"2025-07-02 10:04:01"} 
[2025-07-02 10:04:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:04:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:04:02] local.INFO: Cron job executed {"time":"2025-07-02 10:04:02"} 
[2025-07-02 10:05:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:05:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:05:01] local.INFO: Cron job executed {"time":"2025-07-02 10:05:01"} 
[2025-07-02 10:05:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:05:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:05:02] local.INFO: Cron job executed {"time":"2025-07-02 10:05:02"} 
[2025-07-02 10:06:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:06:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:06:01] local.INFO: Cron job executed {"time":"2025-07-02 10:06:01"} 
[2025-07-02 10:06:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:06:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:06:02] local.INFO: Cron job executed {"time":"2025-07-02 10:06:02"} 
[2025-07-02 10:07:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:07:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:07:01] local.INFO: Cron job executed {"time":"2025-07-02 10:07:01"} 
[2025-07-02 10:07:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:07:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:07:02] local.INFO: Cron job executed {"time":"2025-07-02 10:07:02"} 
[2025-07-02 10:08:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:08:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:08:01] local.INFO: Cron job executed {"time":"2025-07-02 10:08:01"} 
[2025-07-02 10:08:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:08:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:08:02] local.INFO: Cron job executed {"time":"2025-07-02 10:08:02"} 
[2025-07-02 10:09:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:09:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:09:01] local.INFO: Cron job executed {"time":"2025-07-02 10:09:01"} 
[2025-07-02 10:09:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:09:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:09:02] local.INFO: Cron job executed {"time":"2025-07-02 10:09:02"} 
[2025-07-02 10:10:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:10:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:10:01] local.INFO: Cron job executed {"time":"2025-07-02 10:10:01"} 
[2025-07-02 10:10:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:10:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:10:02] local.INFO: Cron job executed {"time":"2025-07-02 10:10:02"} 
[2025-07-02 10:11:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:11:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:11:01] local.INFO: Cron job executed {"time":"2025-07-02 10:11:01"} 
[2025-07-02 10:11:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:11:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:11:03] local.INFO: Cron job executed {"time":"2025-07-02 10:11:03"} 
[2025-07-02 10:12:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:12:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:12:01] local.INFO: Cron job executed {"time":"2025-07-02 10:12:01"} 
[2025-07-02 10:12:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:12:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:12:02] local.INFO: Cron job executed {"time":"2025-07-02 10:12:02"} 
[2025-07-02 10:13:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:13:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:13:01] local.INFO: Cron job executed {"time":"2025-07-02 10:13:01"} 
[2025-07-02 10:13:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:13:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:13:02] local.INFO: Cron job executed {"time":"2025-07-02 10:13:02"} 
[2025-07-02 10:14:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:14:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:14:01] local.INFO: Cron job executed {"time":"2025-07-02 10:14:01"} 
[2025-07-02 10:14:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:14:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:14:02] local.INFO: Cron job executed {"time":"2025-07-02 10:14:02"} 
[2025-07-02 10:15:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:15:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:15:01] local.INFO: Cron job executed {"time":"2025-07-02 10:15:01"} 
[2025-07-02 10:15:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:15:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:15:02] local.INFO: Cron job executed {"time":"2025-07-02 10:15:02"} 
[2025-07-02 10:16:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:16:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:16:01] local.INFO: Cron job executed {"time":"2025-07-02 10:16:01"} 
[2025-07-02 10:16:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:16:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:16:02] local.INFO: Cron job executed {"time":"2025-07-02 10:16:02"} 
[2025-07-02 10:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:17:01] local.INFO: Cron job executed {"time":"2025-07-02 10:17:01"} 
[2025-07-02 10:17:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:17:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:17:02] local.INFO: Cron job executed {"time":"2025-07-02 10:17:02"} 
[2025-07-02 10:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:18:01] local.INFO: Cron job executed {"time":"2025-07-02 10:18:01"} 
[2025-07-02 10:18:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:18:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:18:02] local.INFO: Cron job executed {"time":"2025-07-02 10:18:02"} 
[2025-07-02 10:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:19:01] local.INFO: Cron job executed {"time":"2025-07-02 10:19:01"} 
[2025-07-02 10:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:19:03] local.INFO: Cron job executed {"time":"2025-07-02 10:19:03"} 
[2025-07-02 10:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:20:01] local.INFO: Cron job executed {"time":"2025-07-02 10:20:01"} 
[2025-07-02 10:20:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:20:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:20:02] local.INFO: Cron job executed {"time":"2025-07-02 10:20:02"} 
[2025-07-02 10:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:21:01] local.INFO: Cron job executed {"time":"2025-07-02 10:21:01"} 
[2025-07-02 10:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:21:02] local.INFO: Cron job executed {"time":"2025-07-02 10:21:02"} 
[2025-07-02 10:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:22:01] local.INFO: Cron job executed {"time":"2025-07-02 10:22:01"} 
[2025-07-02 10:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:22:02] local.INFO: Cron job executed {"time":"2025-07-02 10:22:02"} 
[2025-07-02 10:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:23:01] local.INFO: Cron job executed {"time":"2025-07-02 10:23:01"} 
[2025-07-02 10:23:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:23:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:23:03] local.INFO: Cron job executed {"time":"2025-07-02 10:23:03"} 
[2025-07-02 10:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:24:01] local.INFO: Cron job executed {"time":"2025-07-02 10:24:01"} 
[2025-07-02 10:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:24:02] local.INFO: Cron job executed {"time":"2025-07-02 10:24:02"} 
[2025-07-02 10:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:25:01] local.INFO: Cron job executed {"time":"2025-07-02 10:25:01"} 
[2025-07-02 10:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:25:03] local.INFO: Cron job executed {"time":"2025-07-02 10:25:03"} 
[2025-07-02 10:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:26:01] local.INFO: Cron job executed {"time":"2025-07-02 10:26:01"} 
[2025-07-02 10:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:26:02] local.INFO: Cron job executed {"time":"2025-07-02 10:26:02"} 
[2025-07-02 10:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:27:01] local.INFO: Cron job executed {"time":"2025-07-02 10:27:01"} 
[2025-07-02 10:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:27:02] local.INFO: Cron job executed {"time":"2025-07-02 10:27:02"} 
[2025-07-02 10:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:28:01] local.INFO: Cron job executed {"time":"2025-07-02 10:28:01"} 
[2025-07-02 10:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:28:02] local.INFO: Cron job executed {"time":"2025-07-02 10:28:02"} 
[2025-07-02 10:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:29:01] local.INFO: Cron job executed {"time":"2025-07-02 10:29:01"} 
[2025-07-02 10:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:29:03] local.INFO: Cron job executed {"time":"2025-07-02 10:29:03"} 
[2025-07-02 10:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:30:01] local.INFO: Cron job executed {"time":"2025-07-02 10:30:01"} 
[2025-07-02 10:30:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:30:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:30:02] local.INFO: Cron job executed {"time":"2025-07-02 10:30:02"} 
[2025-07-02 10:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:31:01] local.INFO: Cron job executed {"time":"2025-07-02 10:31:01"} 
[2025-07-02 10:31:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-02 10:31:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-02 10:31:03] local.INFO: Cron job executed {"time":"2025-07-02 10:31:03"} 
