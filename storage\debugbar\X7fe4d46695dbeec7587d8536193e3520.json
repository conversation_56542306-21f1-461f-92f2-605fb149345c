{"__meta": {"id": "X7fe4d46695dbeec7587d8536193e3520", "datetime": "2025-07-01 09:19:30", "utime": **********.59784, "method": "POST", "uri": "/api/checkout/webhook", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.507652, "end": **********.597855, "duration": 0.09020304679870605, "duration_str": "90.2ms", "measures": [{"label": "Booting", "start": **********.507652, "relative_start": 0, "end": **********.574876, "relative_end": **********.574876, "duration": 0.06722402572631836, "duration_str": "67.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.574906, "relative_start": 0.06725406646728516, "end": **********.597857, "relative_end": 1.9073486328125e-06, "duration": 0.02295088768005371, "duration_str": "22.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6947240, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/checkout/webhook", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\StripeWebHookController@handleCheckoutWebhook", "namespace": null, "prefix": "api", "where": [], "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "as": "checkout.webhook", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/StripeWebHookController.php:20-57</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0116, "accumulated_duration_str": "11.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values ('25', 'evt_3Rg0LYRhkfMMoe7t1XLJc4yQ', 'charge.updated', 'ch_3Rg0LYRhkfMMoe7t1nEma9A4', 'succeeded', '{\\\"id\\\":\\\"evt_3Rg0LYRhkfMMoe7t1XLJc4yQ\\\",\\\"object\\\":\\\"event\\\",\\\"api_version\\\":\\\"2025-03-31.basil\\\",\\\"created\\\":1751361569,\\\"data\\\":{\\\"object\\\":{\\\"id\\\":\\\"ch_3Rg0LYRhkfMMoe7t1nEma9A4\\\",\\\"object\\\":\\\"charge\\\",\\\"amount\\\":60510,\\\"amount_captured\\\":60510,\\\"amount_refunded\\\":0,\\\"application\\\":null,\\\"application_fee\\\":null,\\\"application_fee_amount\\\":null,\\\"balance_transaction\\\":\\\"txn_3Rg0LYRhkfMMoe7t12kTMJiR\\\",\\\"billing_details\\\":{\\\"address\\\":{\\\"city\\\":null,\\\"country\\\":null,\\\"line1\\\":null,\\\"line2\\\":null,\\\"postal_code\\\":null,\\\"state\\\":null},\\\"email\\\":null,\\\"name\\\":null,\\\"phone\\\":null,\\\"tax_id\\\":null},\\\"calculated_statement_descriptor\\\":\\\"Stripe\\\",\\\"captured\\\":true,\\\"created\\\":1751361566,\\\"currency\\\":\\\"eur\\\",\\\"customer\\\":null,\\\"description\\\":null,\\\"destination\\\":null,\\\"dispute\\\":null,\\\"disputed\\\":false,\\\"failure_balance_transaction\\\":null,\\\"failure_code\\\":null,\\\"failure_message\\\":null,\\\"fraud_details\\\":[],\\\"livemode\\\":false,\\\"metadata\\\":{\\\"user_id\\\":\\\"7\\\",\\\"order_id\\\":\\\"25\\\",\\\"customer_email\\\":\\\"<EMAIL>\\\",\\\"ticket_reservation_id\\\":\\\"28\\\"},\\\"on_behalf_of\\\":null,\\\"order\\\":null,\\\"outcome\\\":{\\\"advice_code\\\":null,\\\"network_advice_code\\\":null,\\\"network_decline_code\\\":null,\\\"network_status\\\":\\\"approved_by_network\\\",\\\"reason\\\":null,\\\"risk_level\\\":\\\"normal\\\",\\\"risk_score\\\":62,\\\"seller_message\\\":\\\"Payment complete.\\\",\\\"type\\\":\\\"authorized\\\"},\\\"paid\\\":true,\\\"payment_intent\\\":\\\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\\\",\\\"payment_method\\\":\\\"pm_1Rg0LaRhkfMMoe7tDrKnoulg\\\",\\\"payment_method_details\\\":{\\\"card\\\":{\\\"amount_authorized\\\":60510,\\\"authorization_code\\\":\\\"526200\\\",\\\"brand\\\":\\\"visa\\\",\\\"checks\\\":{\\\"address_line1_check\\\":null,\\\"address_postal_code_check\\\":null,\\\"cvc_check\\\":\\\"pass\\\"},\\\"country\\\":\\\"US\\\",\\\"exp_month\\\":4,\\\"exp_year\\\":2029,\\\"extended_authorization\\\":{\\\"status\\\":\\\"disabled\\\"},\\\"fingerprint\\\":\\\"4qHsohpy0cF1sMdU\\\",\\\"funding\\\":\\\"credit\\\",\\\"incremental_authorization\\\":{\\\"status\\\":\\\"unavailable\\\"},\\\"installments\\\":null,\\\"last4\\\":\\\"4242\\\",\\\"mandate\\\":null,\\\"multicapture\\\":{\\\"status\\\":\\\"unavailable\\\"},\\\"network\\\":\\\"visa\\\",\\\"network_token\\\":{\\\"used\\\":false},\\\"network_transaction_id\\\":\\\"521137211511110\\\",\\\"overcapture\\\":{\\\"maximum_amount_capturable\\\":60510,\\\"status\\\":\\\"unavailable\\\"},\\\"regulated_status\\\":\\\"unregulated\\\",\\\"three_d_secure\\\":null,\\\"wallet\\\":null},\\\"type\\\":\\\"card\\\"},\\\"radar_options\\\":[],\\\"receipt_email\\\":null,\\\"receipt_number\\\":null,\\\"receipt_url\\\":\\\"https:\\\\/\\\\/pay.stripe.com\\\\/receipts\\\\/payment\\\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KKHQjsMGMgbqGZTXDOg6LBZmgXmpCy53UUAKfdroEuxwJLfJkZ5UKe1IUHSfwHnUjbXp3slYkFovhP5a\\\",\\\"refunded\\\":false,\\\"review\\\":null,\\\"shipping\\\":null,\\\"source\\\":null,\\\"source_transfer\\\":null,\\\"statement_descriptor\\\":null,\\\"statement_descriptor_suffix\\\":null,\\\"status\\\":\\\"succeeded\\\",\\\"transfer_data\\\":null,\\\"transfer_group\\\":null},\\\"previous_attributes\\\":{\\\"balance_transaction\\\":null,\\\"receipt_url\\\":\\\"https:\\\\/\\\\/pay.stripe.com\\\\/receipts\\\\/payment\\\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KKHQjsMGMgZuMOldU_w6LBZraNjGDeKBLHgU-pv7lQhZaxOyOpOf0HzBwCwOYJzP4fSTLP-J5iYg2FLp\\\"}},\\\"livemode\\\":false,\\\"pending_webhooks\\\":2,\\\"request\\\":{\\\"id\\\":null,\\\"idempotency_key\\\":null},\\\"type\\\":\\\"charge.updated\\\"}', '2025-07-01 09:19:30', '2025-07-01 09:19:30')", "type": "query", "params": [], "bindings": ["25", "evt_3Rg0LYRhkfMMoe7t1XLJc4yQ", "charge.updated", "ch_3Rg0LYRhkfMMoe7t1nEma9A4", "succeeded", "{\"id\":\"evt_3Rg0LYRhkfMMoe7t1XLJc4yQ\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751361569,\"data\":{\"object\":{\"id\":\"ch_3Rg0LYRhkfMMoe7t1nEma9A4\",\"object\":\"charge\",\"amount\":60510,\"amount_captured\":60510,\"amount_refunded\":0,\"application\":null,\"application_fee\":null,\"application_fee_amount\":null,\"balance_transaction\":\"txn_3Rg0LYRhkfMMoe7t12kTMJiR\",\"billing_details\":{\"address\":{\"city\":null,\"country\":null,\"line1\":null,\"line2\":null,\"postal_code\":null,\"state\":null},\"email\":null,\"name\":null,\"phone\":null,\"tax_id\":null},\"calculated_statement_descriptor\":\"Stripe\",\"captured\":true,\"created\":1751361566,\"currency\":\"eur\",\"customer\":null,\"description\":null,\"destination\":null,\"dispute\":null,\"disputed\":false,\"failure_balance_transaction\":null,\"failure_code\":null,\"failure_message\":null,\"fraud_details\":[],\"livemode\":false,\"metadata\":{\"user_id\":\"7\",\"order_id\":\"25\",\"customer_email\":\"<EMAIL>\",\"ticket_reservation_id\":\"28\"},\"on_behalf_of\":null,\"order\":null,\"outcome\":{\"advice_code\":null,\"network_advice_code\":null,\"network_decline_code\":null,\"network_status\":\"approved_by_network\",\"reason\":null,\"risk_level\":\"normal\",\"risk_score\":62,\"seller_message\":\"Payment complete.\",\"type\":\"authorized\"},\"paid\":true,\"payment_intent\":\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\"payment_method\":\"pm_1Rg0LaRhkfMMoe7tDrKnoulg\",\"payment_method_details\":{\"card\":{\"amount_authorized\":60510,\"authorization_code\":\"526200\",\"brand\":\"visa\",\"checks\":{\"address_line1_check\":null,\"address_postal_code_check\":null,\"cvc_check\":\"pass\"},\"country\":\"US\",\"exp_month\":4,\"exp_year\":2029,\"extended_authorization\":{\"status\":\"disabled\"},\"fingerprint\":\"4qHsohpy0cF1sMdU\",\"funding\":\"credit\",\"incremental_authorization\":{\"status\":\"unavailable\"},\"installments\":null,\"last4\":\"4242\",\"mandate\":null,\"multicapture\":{\"status\":\"unavailable\"},\"network\":\"visa\",\"network_token\":{\"used\":false},\"network_transaction_id\":\"521137211511110\",\"overcapture\":{\"maximum_amount_capturable\":60510,\"status\":\"unavailable\"},\"regulated_status\":\"unregulated\",\"three_d_secure\":null,\"wallet\":null},\"type\":\"card\"},\"radar_options\":[],\"receipt_email\":null,\"receipt_number\":null,\"receipt_url\":\"https:\\/\\/pay.stripe.com\\/receipts\\/payment\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KKHQjsMGMgbqGZTXDOg6LBZmgXmpCy53UUAKfdroEuxwJLfJkZ5UKe1IUHSfwHnUjbXp3slYkFovhP5a\",\"refunded\":false,\"review\":null,\"shipping\":null,\"source\":null,\"source_transfer\":null,\"statement_descriptor\":null,\"statement_descriptor_suffix\":null,\"status\":\"succeeded\",\"transfer_data\":null,\"transfer_group\":null},\"previous_attributes\":{\"balance_transaction\":null,\"receipt_url\":\"https:\\/\\/pay.stripe.com\\/receipts\\/payment\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KKHQjsMGMgZuMOldU_w6LBZraNjGDeKBLHgU-pv7lQhZaxOyOpOf0HzBwCwOYJzP4fSTLP-J5iYg2FLp\"}},\"livemode\":false,\"pending_webhooks\":2,\"request\":{\"id\":null,\"idempotency_key\":null},\"type\":\"charge.updated\"}", "2025-07-01 09:19:30", "2025-07-01 09:19:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.581138, "duration": 0.0116, "duration_str": "11.6ms", "memory": 0, "memory_str": null, "filename": "PaymentLogRepository.php:18", "source": {"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FPaymentLogRepository.php&line=18", "ajax": false, "filename": "PaymentLogRepository.php", "line": "18"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 100}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.595547, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:35", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=35", "ajax": false, "filename": "StripeWebHookController.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.596633, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:49", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=49", "ajax": false, "filename": "StripeWebHookController.php", "line": "49"}, "connection": "ticketgol", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f49106d-cb68-4b9a-96c2-cae25d387ee2\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/checkout/webhook", "status_code": "<pre class=sf-dump id=sf-dump-55475389 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-55475389\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2087298657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2087298657\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-518733470 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">evt_3Rg0LYRhkfMMoe7t1XLJc4yQ</span>\"\n  \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"5 characters\">event</span>\"\n  \"<span class=sf-dump-key>api_version</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-03-31.basil</span>\"\n  \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361569</span>\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>object</span>\" => <span class=sf-dump-note>array:46</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"27 characters\">ch_3Rg0LYRhkfMMoe7t1nEma9A4</span>\"\n      \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"6 characters\">charge</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>amount_captured</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>amount_refunded</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>application</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>application_fee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>balance_transaction</span>\" => \"<span class=sf-dump-str title=\"28 characters\">txn_3Rg0LYRhkfMMoe7t12kTMJiR</span>\"\n      \"<span class=sf-dump-key>billing_details</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>line1</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>line2</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>postal_code</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      \"<span class=sf-dump-key>calculated_statement_descriptor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Stripe</span>\"\n      \"<span class=sf-dump-key>captured</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361566</span>\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n      \"<span class=sf-dump-key>customer</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>destination</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>dispute</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>disputed</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>failure_balance_transaction</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>failure_code</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>failure_message</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>fraud_details</span>\" => []\n      \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>metadata</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n        \"<span class=sf-dump-key>order_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n        \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>ticket_reservation_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>on_behalf_of</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>outcome</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>advice_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>network_advice_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>network_decline_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>network_status</span>\" => \"<span class=sf-dump-str title=\"19 characters\">approved_by_network</span>\"\n        \"<span class=sf-dump-key>reason</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>risk_level</span>\" => \"<span class=sf-dump-str title=\"6 characters\">normal</span>\"\n        \"<span class=sf-dump-key>risk_score</span>\" => <span class=sf-dump-num>62</span>\n        \"<span class=sf-dump-key>seller_message</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Payment complete.</span>\"\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">authorized</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>paid</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>payment_intent</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pi_3Rg0LYRhkfMMoe7t1wm4olLN</span>\"\n      \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pm_1Rg0LaRhkfMMoe7tDrKnoulg</span>\"\n      \"<span class=sf-dump-key>payment_method_details</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-note>array:22</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount_authorized</span>\" => <span class=sf-dump-num>60510</span>\n          \"<span class=sf-dump-key>authorization_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">526200</span>\"\n          \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n          \"<span class=sf-dump-key>checks</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>address_line1_check</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>address_postal_code_check</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>cvc_check</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pass</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">US</span>\"\n          \"<span class=sf-dump-key>exp_month</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>exp_year</span>\" => <span class=sf-dump-num>2029</span>\n          \"<span class=sf-dump-key>extended_authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">disabled</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>fingerprint</span>\" => \"<span class=sf-dump-str title=\"16 characters\">4qHsohpy0cF1sMdU</span>\"\n          \"<span class=sf-dump-key>funding</span>\" => \"<span class=sf-dump-str title=\"6 characters\">credit</span>\"\n          \"<span class=sf-dump-key>incremental_authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unavailable</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>installments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>last4</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4242</span>\"\n          \"<span class=sf-dump-key>mandate</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>multicapture</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unavailable</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>network</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n          \"<span class=sf-dump-key>network_token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>used</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          \"<span class=sf-dump-key>network_transaction_id</span>\" => \"<span class=sf-dump-str title=\"15 characters\">521137211511110</span>\"\n          \"<span class=sf-dump-key>overcapture</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>maximum_amount_capturable</span>\" => <span class=sf-dump-num>60510</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unavailable</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>regulated_status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unregulated</span>\"\n          \"<span class=sf-dump-key>three_d_secure</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>wallet</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">card</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>radar_options</span>\" => []\n      \"<span class=sf-dump-key>receipt_email</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_number</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_url</span>\" => \"<span class=sf-dump-str title=\"156 characters\">https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KKHQjsMGMgbqGZTXDOg6LBZmgXmpCy53UUAKfdroEuxwJLfJkZ5UKe1IUHSfwHnUjbXp3slYkFovhP5a</span>\"\n      \"<span class=sf-dump-key>refunded</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>review</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>source</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>source_transfer</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor_suffix</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">succeeded</span>\"\n      \"<span class=sf-dump-key>transfer_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>transfer_group</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>previous_attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>balance_transaction</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_url</span>\" => \"<span class=sf-dump-str title=\"156 characters\">https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KKHQjsMGMgZuMOldU_w6LBZraNjGDeKBLHgU-pv7lQhZaxOyOpOf0HzBwCwOYJzP4fSTLP-J5iYg2FLp</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>pending_webhooks</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>request</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>idempotency_key</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">charge.updated</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518733470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1088444447 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>stripe-signature</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">t=1751361569,v1=1291212e6d4322e64b0573552fd89cad64ca5326ad586c9de1a05a029a781654,v0=ad129f9427732e20edf7a787f469525264e667930ed4ffe110d4a4460165b9d6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">application/json; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">*/*; q=0.5, application/xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4059</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">Stripe/1.0 (+https://stripe.com/docs/webhooks)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088444447\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-523412809 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-523412809\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1402815641 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:19:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402815641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-674839227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-674839227\", {\"maxDepth\":0})</script>\n"}}