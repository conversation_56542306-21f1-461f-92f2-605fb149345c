{"__meta": {"id": "X89e1df6d6a1db5b514993e95f0712504", "datetime": "2025-07-01 09:39:03", "utime": **********.717218, "method": "GET", "uri": "/api/v1/orders/25", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.562348, "end": **********.717232, "duration": 0.15488409996032715, "duration_str": "155ms", "measures": [{"label": "Booting", "start": **********.562348, "relative_start": 0, "end": **********.658525, "relative_end": **********.658525, "duration": 0.0961771011352539, "duration_str": "96.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.658541, "relative_start": 0.09619307518005371, "end": **********.717235, "relative_end": 3.0994415283203125e-06, "duration": 0.05869412422180176, "duration_str": "58.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6576144, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/orders/{id}", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@show", "namespace": null, "prefix": "api/v1/orders", "where": [], "as": "api.orders.show", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=80\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:80-91</a>"}, "queries": {"nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.017929999999999998, "accumulated_duration_str": "17.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs' limit 1", "type": "query", "params": [], "bindings": ["huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.666398, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.625}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.669536, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 3.625, "width_percent": 4.518}, {"sql": "select `id`, `order_no`, `buyer_id`, `ticket_id`, `quantity`, `total_price`, `status`, `purchase_date`, `description`, `order_meta_data` from `orders` where `buyer_id` = 7 and `id` = 25 and `orders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7, 25], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 17, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.673979, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 8.143, "width_percent": 5.745}, {"sql": "select `id`, `name`, `email`, `user_name` from `users` where `users`.`id` in (7) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.677795, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 13.887, "width_percent": 2.342}, {"sql": "select `id`, `user_id`, `address`, `phone` from `user_details` where `user_details`.`user_id` in (7) and `user_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.681689, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 16.23, "width_percent": 11.88}, {"sql": "select `id`, `order_id`, `currency_code`, `payment_method_type`, `total_amount`, `paid_at`, `card_brand`, `card_last_four` from `order_transactions` where `order_transactions`.`order_id` in (25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.686019, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 28.109, "width_percent": 8.199}, {"sql": "select `id`, `order_id`, `name`, `email`, `gender`, `dob` from `attendees` where `attendees`.`order_id` in (25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.690646, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 36.308, "width_percent": 6.023}, {"sql": "select * from `tickets` where `tickets`.`id` in (20) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.693861, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 42.331, "width_percent": 8.031}, {"sql": "select `id`, `name`, `user_name`, `email` from `users` where `users`.`id` in (6) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.698016, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 50.363, "width_percent": 2.956}, {"sql": "select `id`, `user_id`, `address`, `phone`, `company`, `description`, `city`, `country_id` from `user_details` where `user_details`.`user_id` in (6) and `user_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.6999452, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 53.318, "width_percent": 2.454}, {"sql": "select * from `countries` where `countries`.`id` in (231) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.701788, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 55.772, "width_percent": 6.247}, {"sql": "select `id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (231)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 41, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, {"index": 42, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 146}, {"index": 43, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 82}, {"index": 44, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 45, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.704885, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:139", "source": {"index": 41, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=139", "ajax": false, "filename": "OrderRepository.php", "line": "139"}, "connection": "ticketgol", "explain": null, "start_percent": 62.019, "width_percent": 6.079}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI1Ijt9fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI1Ijt9fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.71053, "duration": 0.005719999999999999, "duration_str": "5.72ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 68.098, "width_percent": 31.902}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserDetail": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUserDetail.php&line=1", "ajax": false, "filename": "UserDetail.php", "line": "?"}}, "App\\Models\\Order": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\OrderTransaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}, "App\\Models\\Attendee": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FAttendee.php&line=1", "ajax": false, "filename": "Attendee.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders/25\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f49176b-d56a-4b04-a716-7cad93d4171e\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders/25", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"910 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; __stripe_sid=fbeba680-a0c5-44e0-b5f7-7f6c49bc688dd7312d; XSRF-TOKEN=eyJpdiI6IlBwcHFVdEFWaDU1UUh6SkJ1V1ZlSXc9PSIsInZhbHVlIjoiNEpsRVJCT0RSUCtxNzBLVm41TEsxeFZTczl5eDZlUUM2TnFxNzF1R2dYY2g5YmZ1cUErdkdZZ0RKM0dQTWQybWdSV0I2OHE5YURpSzV3QXU5WWxnelRwS1ViWURDSTNXMkk4YlJSRlp4L0hpd1IwMUo5WmJaOEdqampNQTVsNnMiLCJtYWMiOiIxMWNhMTkxNWEzOTBlNGY2MGJiYjJjNDBjNWUzNDAwNGUwNDJhYWUxODhiZDM5Y2IwMDA3MDFjODAxMWU1MGJiIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InVrZ2dxNHRmMHorWDQ0WHlUR1Vkb1E9PSIsInZhbHVlIjoiNXB5dHZUVGMxUERLSkdoeUhqWlkySjhENVpjR1BZNy9yMkExaHVPSTUvdnVQMlphSFg0N1grLzM5Q2p2MnRCUGNVMlJ0aGJjQmVWbnlVNlNPUERRN3hCVDdVRVY4OXNhaHA2Q1FWVXdPeUpITExRUitTZnlhVlR4VXlDeGxGRTEiLCJtYWMiOiJmYTI4YzM0ZDA5MTJlYmFhNTNhODU1ZGM2ZWI5YjBmZDIzOTc3NTdlNjFlMmU1MmQyNWVkMDM3NWU4YzBlMzIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlBwcHFVdEFWaDU1UUh6SkJ1V1ZlSXc9PSIsInZhbHVlIjoiNEpsRVJCT0RSUCtxNzBLVm41TEsxeFZTczl5eDZlUUM2TnFxNzF1R2dYY2g5YmZ1cUErdkdZZ0RKM0dQTWQybWdSV0I2OHE5YURpSzV3QXU5WWxnelRwS1ViWURDSTNXMkk4YlJSRlp4L0hpd1IwMUo5WmJaOEdqampNQTVsNnMiLCJtYWMiOiIxMWNhMTkxNWEzOTBlNGY2MGJiYjJjNDBjNWUzNDAwNGUwNDJhYWUxODhiZDM5Y2IwMDA3MDFjODAxMWU1MGJiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-570442641 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570442641\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-456354452 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:39:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjUwdjZBdlprOCtEYUljNExlL3NrU2c9PSIsInZhbHVlIjoiYzZtSzRsYzlWT2ZnUFBBNzRjdFRsbG4yR09HSmc4L25wT2x2NlNuOWJSU0NEUmowS3lyR3RFcWhzNEdleGxhTjJLOUdxcDIwMXhQbnArZHcxbTQyWnRzSFRHU0l3aHg0MmlpME42Nm1CK2VJdUxKVy9obUxrcnJmOW5zK3Y3TnkiLCJtYWMiOiI1NmYwMTBiZWI5ODBjMzQ5MWVlZWMzZDc2YjM5OGQzMzUyM2NlYmU5NWE5NWM1ODJmNjczMTY0NTBjODk1ZjI1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:39:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkFqa0l2YlczYUJEY0xDVi83SGlod1E9PSIsInZhbHVlIjoiMFB5c2xkRXNkSWY0NURqcW5GL1p3ajd0ZTJTQlZmRU45N2pkUkRXTEs0QzVUa0VaQjVBNHo1WGFZZVhZdFJtcnJkTzdzTnZ0S2pPN0NSdDYxVFRKQ1Y5ekxyaEh1RTNCY2xDVkxLb3lOU2dZVjNYb2ZldEdyM1BVSk91TVVzSFMiLCJtYWMiOiJjZDBmNTI5YjU3OWE3NmIwZTc0M2MyODgwMDk5OWYyNzJjMTg5OTlhNTdmMWRmNjgwNGMyMTkxNzRkOTdhMjNhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:39:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjUwdjZBdlprOCtEYUljNExlL3NrU2c9PSIsInZhbHVlIjoiYzZtSzRsYzlWT2ZnUFBBNzRjdFRsbG4yR09HSmc4L25wT2x2NlNuOWJSU0NEUmowS3lyR3RFcWhzNEdleGxhTjJLOUdxcDIwMXhQbnArZHcxbTQyWnRzSFRHU0l3aHg0MmlpME42Nm1CK2VJdUxKVy9obUxrcnJmOW5zK3Y3TnkiLCJtYWMiOiI1NmYwMTBiZWI5ODBjMzQ5MWVlZWMzZDc2YjM5OGQzMzUyM2NlYmU5NWE5NWM1ODJmNjczMTY0NTBjODk1ZjI1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:39:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkFqa0l2YlczYUJEY0xDVi83SGlod1E9PSIsInZhbHVlIjoiMFB5c2xkRXNkSWY0NURqcW5GL1p3ajd0ZTJTQlZmRU45N2pkUkRXTEs0QzVUa0VaQjVBNHo1WGFZZVhZdFJtcnJkTzdzTnZ0S2pPN0NSdDYxVFRKQ1Y5ekxyaEh1RTNCY2xDVkxLb3lOU2dZVjNYb2ZldEdyM1BVSk91TVVzSFMiLCJtYWMiOiJjZDBmNTI5YjU3OWE3NmIwZTc0M2MyODgwMDk5OWYyNzJjMTg5OTlhNTdmMWRmNjgwNGMyMTkxNzRkOTdhMjNhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:39:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456354452\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/25</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}