{"__meta": {"id": "X96b35632170aabb4e23cbd52822caa70", "datetime": "2025-07-01 06:58:12", "utime": 1751353092.562803, "method": "GET", "uri": "/events", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.735632, "end": 1751353092.562819, "duration": 0.8271870613098145, "duration_str": "827ms", "measures": [{"label": "Booting", "start": **********.735632, "relative_start": 0, "end": **********.797254, "relative_end": **********.797254, "duration": 0.06162214279174805, "duration_str": "61.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797264, "relative_start": 0.06163215637207031, "end": 1751353092.562821, "relative_end": 1.9073486328125e-06, "duration": 0.765556812286377, "duration_str": "766ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 17456336, "peak_usage_str": "17MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 34, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.873468, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.878947, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.887539, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.895761, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.900733, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.905257, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.908577, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.96785, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.9722, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.012702, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.018904, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.049034, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.051725, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.084385, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.087781, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.123548, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.128333, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.162753, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.166221, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.207401, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.210882, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.247739, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.253255, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.289006, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.291619, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.330522, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353092.338221, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": 1751353092.344214, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1751353092.357374, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1751353092.37278, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::f07f1a332c895be3dafc362336ba959c", "param_count": null, "params": [], "start": 1751353092.443071, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/f07f1a332c895be3dafc362336ba959c.blade.php__components::f07f1a332c895be3dafc362336ba959c", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Ff07f1a332c895be3dafc362336ba959c.blade.php&line=1", "ajax": false, "filename": "f07f1a332c895be3dafc362336ba959c.blade.php", "line": "?"}}, {"name": "filament-language-switch::language-switch", "param_count": null, "params": [], "start": 1751353092.4441, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/language-switch.blade.phpfilament-language-switch::language-switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Flanguage-switch.blade.php&line=1", "ajax": false, "filename": "language-switch.blade.php", "line": "?"}}, {"name": "filament-language-switch::switch", "param_count": null, "params": [], "start": 1751353092.444591, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/switch.blade.phpfilament-language-switch::switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": 1751353092.558611, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}]}, "route": {"uri": "GET events", "domain": "admin.ticketgol.test", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents@__invoke", "as": "filament.admin.resources.events.index", "namespace": null, "prefix": "/events", "where": [], "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 41, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "47.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7' limit 1", "type": "query", "params": [], "bindings": ["zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.800076, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.342}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.802529, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.342, "width_percent": 2.453}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.807349, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "ticketgol", "explain": null, "start_percent": 3.795, "width_percent": 1.069}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.811121, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "ticketgol", "explain": null, "start_percent": 4.864, "width_percent": 1.929}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.813595, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "ticketgol", "explain": null, "start_percent": 6.792, "width_percent": 7.945}, {"sql": "select count(*) as aggregate from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8392081, "duration": 0.01366, "duration_str": "13.66ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "ticketgol", "explain": null, "start_percent": 14.738, "width_percent": 28.637}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.854393, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 43.375, "width_percent": 5.577}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.859719, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 48.952, "width_percent": 1.824}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.863675, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 50.776, "width_percent": 2.432}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.866256, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 53.208, "width_percent": 1.614}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:12' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.376664, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 54.822, "width_percent": 1.845}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 06:58:12' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.381455, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 56.667, "width_percent": 4.382}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 06:58:12' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.387405, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 61.048, "width_percent": 1.95}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 06:58:12' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.391923, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 62.998, "width_percent": 3.836}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:12' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.396729, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 66.834, "width_percent": 0.839}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 06:58:12' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.399941, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 67.673, "width_percent": 1.237}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 06:58:12' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.403545, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 68.91, "width_percent": 0.797}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 06:58:12' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.406868, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 69.706, "width_percent": 2.075}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 06:58:12' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.4115, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 71.782, "width_percent": 2.642}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 06:58:12' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.416033, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 74.423, "width_percent": 1.363}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.419944, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 75.786, "width_percent": 0.985}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 06:58:12' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.423464, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 76.771, "width_percent": 1.3}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 06:58:12' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.428183, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 78.071, "width_percent": 1.111}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 06:58:12' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.432065, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 79.182, "width_percent": 1.384}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 06:58:12' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.436185, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 80.566, "width_percent": 0.901}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:12' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.4598908, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 81.468, "width_percent": 1.635}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 06:58:12' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.4635339, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 83.103, "width_percent": 0.86}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 06:58:12' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.466849, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 83.962, "width_percent": 1.132}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 06:58:12' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.470428, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 85.094, "width_percent": 1.09}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:12' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.474875, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 86.184, "width_percent": 0.964}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 06:58:12' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.478197, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 87.149, "width_percent": 1.006}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 06:58:12' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.4813972, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 88.155, "width_percent": 1.027}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 06:58:12' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.484952, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 89.182, "width_percent": 1.006}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 06:58:12' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.488934, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 90.189, "width_percent": 0.985}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 06:58:12' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.492262, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 91.174, "width_percent": 1.237}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.495587, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 92.411, "width_percent": 0.964}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 06:58:12' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.498814, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 93.375, "width_percent": 1.006}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 06:58:12' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.502719, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 94.382, "width_percent": 1.845}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 06:58:12' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.506758, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 96.226, "width_percent": 1.216}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 06:58:12' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353092.5103111, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 97.442, "width_percent": 1.048}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9', `last_activity` = 1751353092, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9", 1751353092, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": 1751353092.561105, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 98.491, "width_percent": 1.509}]}, "models": {"data": {"App\\Models\\Event": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Slug": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"app.filament.resources.event-resource.pages.manage-events #DrtPSfbT8oPX6a9PCUbp": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:2 [\n      \"created_at\" => false\n      \"updated_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.event-resource.pages.manage-events\"\n  \"component\" => \"App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents\"\n  \"id\" => \"DrtPSfbT8oPX6a9PCUbp\"\n]", "filament-language-switch #KsCdcJ8vbYarVhX1wiS8": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"KsCdcJ8vbYarVhX1wiS8\"\n]", "filament.livewire.notifications #Q0f7nyEZem0hhvzjHus4": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#9156\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"Q0f7nyEZem0hhvzjHus4\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 172, "messages": [{"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1975504792 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975504792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.818892, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1172500607 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172500607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.819283, "xdebug_link": null}, {"message": "[\n  ability => create_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-589855720 data-indent-pad=\"  \"><span class=sf-dump-note>create_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">create_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589855720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.820625, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1002332887 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002332887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.820973, "xdebug_link": null}, {"message": "[\n  ability => reorder_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-528737256 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reorder_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528737256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.822881, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1962019790 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962019790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.823227, "xdebug_link": null}, {"message": "[\n  ability => delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-662408718 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662408718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.826514, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1914594972 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914594972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.826998, "xdebug_link": null}, {"message": "[\n  ability => force_delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1346635088 data-indent-pad=\"  \"><span class=sf-dump-note>force_delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">force_delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346635088\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.828254, "xdebug_link": null}, {"message": "[\n  ability => forceDeleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1116047672 data-indent-pad=\"  \"><span class=sf-dump-note>forceDeleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">forceDeleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116047672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.828654, "xdebug_link": null}, {"message": "[\n  ability => restore_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-101435614 data-indent-pad=\"  \"><span class=sf-dump-note>restore_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">restore_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101435614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.829787, "xdebug_link": null}, {"message": "[\n  ability => restoreAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394427506 data-indent-pad=\"  \"><span class=sf-dump-note>restoreAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restoreAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394427506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.830129, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1344535127 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344535127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.928427, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1505061810 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505061810\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.928866, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-217587409 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217587409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930771, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-891661108 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891661108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.931215, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-897115419 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897115419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.960371, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1489958056 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489958056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.960969, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-486721088 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486721088\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965958, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1761820410 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761820410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.96651, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1891589811 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891589811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.977536, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1858018002 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858018002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.977959, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2053143582 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053143582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.980761, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-53334354 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53334354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.981264, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1336525574 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336525574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.004281, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1034563494 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034563494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.004655, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1466455857 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466455857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.006592, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-538515986 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538515986\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.006925, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-229956300 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229956300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.011263, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1203105396 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203105396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.011801, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-807195721 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807195721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.017784, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1822836655 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822836655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.018218, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-3205783 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3205783\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.022977, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2022548109 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022548109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.023386, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793980352 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793980352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.025069, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-752062685 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752062685\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.025801, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-32711621 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32711621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.044296, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-594927266 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594927266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.04483, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-946114196 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946114196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.048012, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1890052063 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890052063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.048359, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-373647137 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373647137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.05569, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-131169099 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131169099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.056065, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-372987212 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372987212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.058286, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1078445348 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078445348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.058678, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1037949822 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037949822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.079032, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-190742383 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190742383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.079509, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-583043020 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583043020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.083229, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1926365352 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926365352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.083668, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1612279791 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612279791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.094002, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-609716642 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609716642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.094505, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2012650337 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012650337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.096525, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-893217960 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893217960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.096894, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1120792714 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120792714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.1162, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-20102131 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20102131\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.11655, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-773086793 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773086793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.11887, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-628974045 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-628974045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.119309, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-250516831 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250516831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.122494, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1268283632 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268283632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.122871, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1877167868 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1877167868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.127243, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-154698653 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154698653\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.127649, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2122230292 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122230292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.132486, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861916211 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861916211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.132938, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1531279183 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531279183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.136908, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-166110795 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166110795\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.137485, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1705013065 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705013065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.15792, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1415825150 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415825150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.158269, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-364868631 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364868631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.161595, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1781609620 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781609620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.162013, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-558049890 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558049890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.171615, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1118906119 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118906119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.172128, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1316641728 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316641728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.174841, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1819651556 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819651556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.175307, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-800509417 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800509417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.200942, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1141308287 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141308287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.20149, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-506785815 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506785815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.206088, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1185535295 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185535295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.206445, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-616912426 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616912426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.217511, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-782736623 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782736623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.217988, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2110380357 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110380357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.220037, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-326033690 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326033690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.220409, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1420878538 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420878538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.240086, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-310555100 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310555100\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.240665, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1822222807 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822222807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.242819, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1011946579 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011946579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.243182, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-564984140 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564984140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.246157, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-591191349 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591191349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.246786, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1857403909 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857403909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.252077, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1023153157 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023153157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.252492, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1924688110 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924688110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.259149, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1859190415 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859190415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.259692, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-269791170 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269791170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.261735, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1756086906 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756086906\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.262083, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1099294929 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099294929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.282977, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1595641725 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595641725\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.283445, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-962480908 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962480908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.287745, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-813276471 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813276471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.288216, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1018482728 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018482728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.295777, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-641881215 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641881215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.296155, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1921155147 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921155147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.298038, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-614265093 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614265093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.298393, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1412319075 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412319075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.321859, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1009242494 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009242494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.322388, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-691045802 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691045802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.32548, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1080581075 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080581075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.325979, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1500520835 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500520835\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.329489, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1495231794 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495231794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.329866, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1124400563 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124400563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.336292, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2035492367 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035492367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.337017, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1212669845 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212669845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.374409, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-757792224 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757792224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.375452, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1393417117 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1393417117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.379676, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-539523753 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539523753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.380092, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-292051358 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292051358\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.385591, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-297512075 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297512075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.386103, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-975117371 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975117371\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.390028, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2066209285 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066209285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.390522, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-782786880 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782786880\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.395416, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-60067622 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60067622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.395805, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1642902813 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642902813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.39865, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2115706775 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115706775\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.399011, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2119392183 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119392183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.402153, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-399217309 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399217309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.402591, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-968171713 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968171713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.405473, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1626997450 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626997450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.405838, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1319738844 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319738844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.40969, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1788550781 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788550781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.410263, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-434700441 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434700441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.414507, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-967737974 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967737974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.4149, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-726879630 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726879630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.418554, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1221512804 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221512804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.418981, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2030619061 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030619061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.421996, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-738923289 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738923289\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.422342, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1580515729 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580515729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.426735, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2078925659 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078925659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.427183, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-603897899 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603897899\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.43054, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-555478709 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555478709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.430944, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1805973246 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805973246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.434763, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-286377270 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286377270\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.435174, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-570004181 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570004181\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.43807, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1102764355 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102764355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.438432, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-149649385 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149649385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.457561, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701750031 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701750031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.458642, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-596859336 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-596859336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.462285, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-524193678 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524193678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.46265, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1361086842 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361086842\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.46532, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2117115777 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117115777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.465695, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-813671349 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813671349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.469134, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1881244376 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881244376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.469533, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1153695136 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1153695136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.473021, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1407665488 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407665488\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.473655, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-57803395 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57803395\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.476895, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-821156992 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821156992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.477278, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2100281595 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100281595\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.480148, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-621550454 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621550454\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.480513, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1666391125 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666391125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.483651, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309698059 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309698059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.48405, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1356890129 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356890129\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.487297, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1067586709 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067586709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.487792, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-488435570 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488435570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.490995, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2048892661 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048892661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.491358, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1208037094 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208037094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.494462, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-493415895 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493415895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.494804, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1444179881 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444179881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.497609, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-224055956 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224055956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.497945, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1205838850 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205838850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.501031, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1005772331 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005772331\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.501468, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1711380668 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711380668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.505316, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1222300183 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222300183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.505705, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1348285495 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348285495\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.509016, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1398767803 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398767803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.509377, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1481114572 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481114572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.512218, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1490304772 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490304772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353092.512549, "xdebug_link": null}]}, "session": {"_token": "u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j", "url": "[]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://admin.ticketgol.test/events\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2"}, "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f48dde5-57b5-448f-9855-e9aecd759248\" target=\"_blank\">View in Telescope</a>", "path_info": "/events", "status_code": "<pre class=sf-dump id=sf-dump-34069355 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-34069355\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-999125856 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-999125856\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1779726389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1779726389\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-397645180 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1147 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Ii8xRW95eWtha0Eza1lZMUpDSmxrS1E9PSIsInZhbHVlIjoiVWRzTFJiNm83dlVkSXdDdlBXSktiNWFBUlV5cU55SHZ3SlI3ckJKNzhSTnFWdFd6SXJXd21xekc0aWNma2YrdGxWdStyMHg4OXJSZzloenEzakxuazd5VitkamJ6MGtGeDBhSks0akZsVmpsVGplb2FXdlNCTnNib0xPNDRIT1ciLCJtYWMiOiIzMDE4NjA4NzlmMjdiZjJlM2M1NTQ4NTk4NGJhZTRjNDFiNTFkMjE4MGVkYTU2Y2M2NjFjNWExZjY3ZTkzMDVjIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkZkV09SOFFLSE1SWDM0VUdwMmF3b3c9PSIsInZhbHVlIjoiOEZnQ2V4ZE1EaFgrdkNnOEFnSTRkRitNakpqeWpieFRlTW5JeGtjdDZDalU3aFBlUXk3cDRIbkpERUY3U2lLRnc4ZHQ2VFdEUm5BcjRqc3BGblpXT3p4amswbDE4d3o1R1ZsbDRycVZZUTUwNUZPRnBqZStBM0FmcTF3L0tid1kiLCJtYWMiOiJhMTQ5MjBhMTRmMDQ5OWI2OGY5ZDMyNDBmMWIzMGM0YTU0NTAzNTBjMzg3ODU4MmYyYjc0ODM2ZTcwNmU0YzkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397645180\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-727295215 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727295215\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1289290245 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 06:58:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InRZaDAxaDgxNlNjNWtMR3VVMmF4VEE9PSIsInZhbHVlIjoiVys2ZDMwRDZSVUtCMFh2Ulp2ZkNxdUp3YzVnM0FuR29uY2hvTkJPbnVpRDRXNS8xdStybkRFNnQzYjlVbi9OdVQ4YXZWbnkxbEl0WXVRZ2NpM0dMWWk5M1hKZGpxUmFESzVzbG94MEYxeEVTQUloYm1XMUtsTDN5SUl3b1dzT0UiLCJtYWMiOiIwZDkyNmEyYmE2ODNiYzVkMDU2NjM2ODk1YTM1N2VjZDcyMThlZWZiMWZkMDVhZDFhNDUzNTQyMDA2MDVlNmJkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 08:58:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkVRZTNmRWpSajVPL0lQdmVoMU5yT0E9PSIsInZhbHVlIjoiUE1wUkdsOFNRZ3l2NVEvQUh4RzlXbXc1QXl1TGFLZjZlSG5ROGxtSkJKandDQ0liNVpVeS9yOW43UEZTajBicWhTTGtHMlBxbE1EdW94czNvOVlZZGFjNkcyb0x2TDlpV0EvUUhqREJCd2xmU1ZQaTA2OUpTS0drblNTaDVoZ28iLCJtYWMiOiJjNjQ0MWQ2NDA1MzgwNDcyOGQ1NTgzNTdmMTNmNDIwMDBiNDkxOGQxNThkYmNkNGM1ODc0OTZmYWExNmRlZDNkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 08:58:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InRZaDAxaDgxNlNjNWtMR3VVMmF4VEE9PSIsInZhbHVlIjoiVys2ZDMwRDZSVUtCMFh2Ulp2ZkNxdUp3YzVnM0FuR29uY2hvTkJPbnVpRDRXNS8xdStybkRFNnQzYjlVbi9OdVQ4YXZWbnkxbEl0WXVRZ2NpM0dMWWk5M1hKZGpxUmFESzVzbG94MEYxeEVTQUloYm1XMUtsTDN5SUl3b1dzT0UiLCJtYWMiOiIwZDkyNmEyYmE2ODNiYzVkMDU2NjM2ODk1YTM1N2VjZDcyMThlZWZiMWZkMDVhZDFhNDUzNTQyMDA2MDVlNmJkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 08:58:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkVRZTNmRWpSajVPL0lQdmVoMU5yT0E9PSIsInZhbHVlIjoiUE1wUkdsOFNRZ3l2NVEvQUh4RzlXbXc1QXl1TGFLZjZlSG5ROGxtSkJKandDQ0liNVpVeS9yOW43UEZTajBicWhTTGtHMlBxbE1EdW94czNvOVlZZGFjNkcyb0x2TDlpV0EvUUhqREJCd2xmU1ZQaTA2OUpTS0drblNTaDVoZ28iLCJtYWMiOiJjNjQ0MWQ2NDA1MzgwNDcyOGQ1NTgzNTdmMTNmNDIwMDBiNDkxOGQxNThkYmNkNGM1ODc0OTZmYWExNmRlZDNkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 08:58:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289290245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-768190441 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768190441\", {\"maxDepth\":0})</script>\n"}}