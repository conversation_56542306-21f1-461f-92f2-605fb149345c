import React from "react";
import useTranslations from "@/hooks/useTranslations";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import EventDateTime from "@/components/eventdetails/EventDateTime";
import {
    Target,
    CalendarDays,
    Flag,
    Trophy,
    Tag,
    FileText,
    Dribbble,
    House,
    Plane,
} from "lucide-react";

function EventInfo({ event }) {
    const { translate } = useTranslations();

    return (
        <InfoCard title={translate("events.info", "Event Information")}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Event Image */}
                <div className="lg:col-span-1">
                    <figure className="overflow-hidden relative w-full h-48 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-xl shadow-lg">
                        {event.image ? (
                            <img
                                src={event.image}
                                alt={event.image_alt || event.name}
                                className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                            />
                        ) : (
                            <div className="flex justify-center items-center h-full">
                                <img
                                    className="w-16 h-auto opacity-60"
                                    src="/img/ticketgol-logo.png"
                                    alt={event.name || translate("events.event_image", "Event Image")}
                                />
                            </div>
                        )}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div className="absolute bottom-4 left-4 text-white">
                            <p className="text-sm font-medium">
                                {event.category?.label}
                            </p>
                        </div>
                    </figure>
                </div>

                {/* Event Details */}
                <div className="lg:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <InfoItem
                            label={translate("events.name", "Event Name")}
                            value={event.name}
                            className="md:col-span-2"
                            highlight={true}
                            icon={<Target size={18} />}
                        />
                        {event.description && (
                            <InfoItem
                                label={translate(
                                    "events.description",
                                    "Description",
                                )}
                                value={event.description}
                                className="md:col-span-2"
                                icon={<FileText size={18} />}
                            />
                        )}
                        <InfoItem
                            label={translate("events.category", "Category")}
                            value={event.category?.label}
                            icon={<Tag size={18} />}
                        />
                        <InfoItem
                            label={translate("events.date_time", "Date & Time")}
                            value={<EventDateTime event={event} />}
                            icon={<CalendarDays size={18} />}
                            highlight={true}
                            className="md:col-span-2"
                        />
                        {event.country && (
                            <InfoItem
                                label={translate("events.country", "Country")}
                                value={event.country.name}
                                icon={<Flag size={18} />}
                            />
                        )}
                        {event.league && (
                            <InfoItem
                                label={translate("events.league", "League")}
                                value={event.league.name}
                                icon={<Trophy size={18} />}
                            />
                        )}
                    </div>

                    {/* Clubs Information */}
                    {(event.home_club || event.guest_club) && (
                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                <span>
                                    <Dribbble size={18} />
                                </span>
                                {translate("events.order.clubs", "Teams")}
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {event.home_club && (
                                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                        <InfoItem
                                            label={translate(
                                                "events.home_club",
                                                "Home Club",
                                            )}
                                            value={event.home_club.name}
                                            icon={<House size={18} />}
                                        />
                                    </div>
                                )}
                                {event.guest_club && (
                                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                        <InfoItem
                                            label={translate(
                                                "events.guest_club",
                                                "Guest Club",
                                            )}
                                            value={event.guest_club.name}
                                            icon={<Plane size={18} />}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </InfoCard>
    );
}

export default EventInfo;
