<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            'buyer' => $this->whenLoaded('buyer', function () {
                return [
                    'id' => $this->buyer->id,
                    'name' => $this->buyer->name,
                    'email' => $this->buyer->email,
                    'phone' => $this->buyer->userDetail->phone ?? null,
                    'address' => $this->buyer->userDetail->address ?? null,
                ];
            }),

            'ticket' => $this->order_meta_data && isset($this->order_meta_data->ticket) ? (function () {
                $ticket = $this->order_meta_data->ticket;
                $currentLocale = app()->getLocale();

                return [
                    'id' => $ticket->id,
                    'ticket_no' => $ticket->ticket_no,
                    'price' => $ticket->price,
                    'face_value_price' => $ticket->face_value_price,
                    'quantity' => $ticket->quantity,
                    'ticket_type' => $ticket->ticket_type->label ?? null,
                    'ticket_rows' => $ticket->ticket_rows,
                    'ticket_seats' => $ticket->ticket_seats,
                    'description' => $ticket->description->{$currentLocale} ?? null,

                    'restrictions' => isset($ticket->restrictions) ? collect($ticket->restrictions)->map(function ($restriction) use ($currentLocale) {
                        return [
                            'id' => $restriction->id,
                            'type' => $restriction->type,
                            'name' => $restriction->name->{$currentLocale} ?? '',
                        ];
                    }) : [],

                    'seller' => $this->whenLoaded('ticket.seller', function () {
                        return [
                            'id' => $this->ticket->seller->id,
                            'name' => $this->ticket->seller->name,
                            'email' => $this->ticket->seller->email,
                            'address' => $this->ticket->seller->userDetail->address ?? null,
                            'phone' => $this->ticket->seller->userDetail->phone ?? null,
                            'company' => $this->ticket->seller->userDetail->company ?? null,
                            'description' => $this->ticket->seller->userDetail->description ?? null,
                            'city' => $this->ticket->seller->userDetail->city ?? null,
                            'country' => $this->ticket->seller->userDetail->country?->translation?->name ?? null,
                        ];
                    }),

                    'sector' => isset($ticket->sector) ? [
                        'id' => $ticket->sector->id,
                        'name' => $ticket->sector->name->{$currentLocale} ?? null,
                    ] : null,

                    'event' => isset($this->order_meta_data->event) ? (function () use ($currentLocale) {
                        $event = $this->order_meta_data->event;

                        return [
                            'id' => $event->id,
                            'name' => $event->name->{$currentLocale} ?? null,
                            'description' => $event->description->{$currentLocale} ?? null,
                            'image' => $event->image ?? '',
                            'image_alt' => $event->image_alt ?? '',
                            'date' => $event->date,
                            'time' => $event->time,
                            'timezone' => $event->timezone,
                            'category' => [
                                'value' => $event->category->value,
                                'label' => $event->category->label,
                            ],

                            'country' => isset($event->country) ? [
                                'id' => $event->country->id,
                                'name' => $event->country->name->{$currentLocale} ?? null,
                            ] : null,

                            'home_club' => isset($event->home_club) ? [
                                'id' => $event->home_club->id,
                                'name' => $event->home_club->name->{$currentLocale} ?? null,
                            ] : null,

                            'guest_club' => isset($event->guest_club) ? [
                                'id' => $event->guest_club->id,
                                'name' => $event->guest_club->name->{$currentLocale} ?? null,
                            ] : null,

                            'stadium' => isset($event->stadium) ? [
                                'id' => $event->stadium->id,
                                'name' => $event->stadium->name->{$currentLocale} ?? null,
                                'address_line_1' => $event->stadium->address_line_1,
                                'address_line_2' => $event->stadium->address_line_2,
                                'postcode' => $event->stadium->postcode,
                                'country' => $event->stadium->country->{$currentLocale} ?? null,
                            ] : null,

                            'league' => isset($event->league) ? [
                                'id' => $event->league->id,
                                'name' => $event->league->name->{$currentLocale} ?? null,
                            ] : null,

                            'restrictions' => isset($event->restrictions) ? collect($event->restrictions)->map(function ($restriction) use ($currentLocale) {
                                return [
                                    'id' => $restriction->id,
                                    'type' => $restriction->type,
                                    'name' => $restriction->name->{$currentLocale} ?? null,
                                ];
                            }) : [],
                        ];
                    })() : null,
                ];
            })() : null,

            'combined_restrictions' => $this->order_meta_data ? (function () {
                $currentLocale = app()->getLocale();
                $ticketRestrictions = isset($this->order_meta_data->ticket->restrictions) ?
                    collect($this->order_meta_data->ticket->restrictions) : collect([]);
                $eventRestrictions = isset($this->order_meta_data->event->restrictions) ?
                    collect($this->order_meta_data->event->restrictions) : collect([]);

                return $ticketRestrictions->merge($eventRestrictions)
                    ->unique('id')
                    ->map(function ($restriction) use ($currentLocale) {
                        return [
                            'id' => $restriction->id,
                            'type' => $restriction->type,
                            'name' => $restriction->name->{$currentLocale} ?? null,
                        ];
                    })->values();
            })() : [],

            // TODO: change this to has one
            'transactions' => $this->whenLoaded('transactions', function () {
                return $this->transactions->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'currency_code' => strtoupper($transaction->currency_code),
                        'total_amount' => $transaction->total_amount,
                        'paid_at' => $transaction->paid_at,
                        'payment_method_type' => $transaction->payment_method_type,
                        'card_brand' => strtoupper($transaction->card_brand),
                        'card_last_four' => $transaction->card_last_four,
                    ];
                });
            }),

            'attendees' => $this->whenLoaded('attendees', function () {
                return $this->attendees->map(function ($attendee) {
                    return [
                        'id' => $attendee->id,
                        'name' => $attendee->name,
                        'email' => $attendee->email,
                        'gender' => $attendee->gender,
                        'dob' => $attendee->dob,
                    ];
                });
            }),
        ];
    }
}
