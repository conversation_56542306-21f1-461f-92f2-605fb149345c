{"__meta": {"id": "X044343397804dde347bb072519670715", "datetime": "2025-07-01 09:15:48", "utime": **********.885068, "method": "GET", "uri": "/api/v1/events/event-jaunita-runolfsdottir-7", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.715318, "end": **********.885081, "duration": 0.16976308822631836, "duration_str": "170ms", "measures": [{"label": "Booting", "start": **********.715318, "relative_start": 0, "end": **********.787926, "relative_end": **********.787926, "duration": 0.07260799407958984, "duration_str": "72.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.787945, "relative_start": 0.07262706756591797, "end": **********.885084, "relative_end": 2.86102294921875e-06, "duration": 0.09713888168334961, "duration_str": "97.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7350776, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/{slug}", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@show", "namespace": null, "prefix": "api/v1/events", "where": [], "as": "api.events.show", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=111\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:111-124</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027970000000000002, "accumulated_duration_str": "27.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs' limit 1", "type": "query", "params": [], "bindings": ["huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.792812, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 4.147}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.798763, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 4.147, "width_percent": 5.148}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `stadium_id`, `league_id`, `home_club_id`, `guest_club_id`, `is_feature_event`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price`, (select MAX(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `max_price` from `events` where exists (select * from `slugs` where `events`.`id` = `slugs`.`sluggable_id` and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event' and `slug` = 'event-jaunita-runolfsdottir-7' and `locale` = 'en' and `locale` = 'en') and `is_published` = 1 and `events`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Event", "event-jaunita-runolfsdottir-7", "en", "en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.804881, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 9.296, "width_percent": 15.588}, {"sql": "select `event_id`, `locale`, `name`, `description`, `meta_title`, `meta_description`, `meta_keywords` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8120809, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 24.884, "width_percent": 2.646}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (5) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.815041, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 27.529, "width_percent": 2.252}, {"sql": "select `stadium_id`, `locale`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.818613, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 29.782, "width_percent": 2.681}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.821643, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 32.463, "width_percent": 4.755}, {"sql": "select `country_id`, `locale`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 32, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.825892, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 37.218, "width_percent": 2.789}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8282151, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 40.007, "width_percent": 1.752}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (4) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8305898, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 41.759, "width_percent": 2.181}, {"sql": "select `league_id`, `locale`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8335679, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 43.94, "width_percent": 2.681}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.836251, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 46.621, "width_percent": 2.074}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (1) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.838559, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 48.695, "width_percent": 2.467}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.841187, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 51.162, "width_percent": 1.716}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.843657, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 52.878, "width_percent": 1.966}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (5) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.84571, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 54.844, "width_percent": 1.68}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.847742, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 56.525, "width_percent": 1.716}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.849495, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 58.241, "width_percent": 1.43}, {"sql": "select `stadium_sectors`.`id`, `event_stadium_sectors`.`event_id` as `pivot_event_id`, `event_stadium_sectors`.`stadium_sector_id` as `pivot_stadium_sector_id`, `event_stadium_sectors`.`created_at` as `pivot_created_at`, `event_stadium_sectors`.`updated_at` as `pivot_updated_at` from `stadium_sectors` inner join `event_stadium_sectors` on `stadium_sectors`.`id` = `event_stadium_sectors`.`stadium_sector_id` where `event_stadium_sectors`.`event_id` in (7) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8521929, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 59.671, "width_percent": 5.291}, {"sql": "select `stadium_sector_id`, `locale`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (44, 45, 48)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.8574839, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 64.962, "width_percent": 6.328}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (7) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.86215, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 71.291, "width_percent": 4.326}, {"sql": "select `restriction_id`, `locale`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (6, 7, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.866325, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 75.617, "width_percent": 4.398}, {"sql": "select * from `media` where `media`.`model_id` in (7) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.86939, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 80.014, "width_percent": 2.109}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.871619, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 82.124, "width_percent": 1.788}, {"sql": "select tickets.id, tickets.quantity - COALESCE(SUM(r.quantity), 0) as available_quantity from `tickets` left join `ticket_reservations` as `r` on `r`.`ticket_id` = `tickets`.`id` and `status` in ('active', 'processing') where `tickets`.`event_id` = 7 and `tickets`.`event_id` is not null and `tickets`.`deleted_at` is null and `tickets`.`deleted_at` is null group by `tickets`.`id`, `tickets`.`quantity`", "type": "query", "params": [], "bindings": ["active", "processing", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.87484, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "EventService.php:82", "source": {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FEventService.php&line=82", "ajax": false, "filename": "EventService.php", "line": "82"}, "connection": "ticketgol", "explain": null, "start_percent": 83.911, "width_percent": 12.692}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.8831189, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 96.604, "width_percent": 3.396}]}, "models": {"data": {"App\\Models\\Slug": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Restriction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}}, "count": 30, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders/24\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f490f1b-7d8f-4eb4-90ce-b58c10fc799d\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/event-jaunita-runolfsdottir-7", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6ImI0TmloVlg3WW4xTzFoaU9PSUFZOHc9PSIsInZhbHVlIjoiQmE5L2RBamJZWXErRlhucmM5WjBiUXZuTGJOSE16d2RKVGFDajNSOS9XZmF3aU10ZTk0b3lQVEwwZWRkdzQ5SzZuZFdlK2grb2hOOU56c1ZJUHRDaEpZWjB0aitjL3FXNWxjN1R4cEFCWmd2OC83T0wyYVZXWGhna2pxbGJ2UGoiLCJtYWMiOiJkNDE5NmE1MmJiZWE3NjcxOTU0ZjdhNDk0ZDU0YzMzOWIzM2FjODdmZTFlNmEzN2I0MjY2MDc1ZjRkMDczYjBhIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkphU0FjOXRmK1c5UEdzc0xKWWVkM2c9PSIsInZhbHVlIjoieFdLNVptYkh4NkVGVUlkWks5N1FhaUF0MTlGS3hMdFdMbEV6ZTN3dE1sMllWTXYydHZQVmIvZS8reFZGNXlzMmNybXJ5VkhFaWVDUXAzTWFGaVRHVEljcjBDM3ZkRjBZby9nUmIyZ2lDVlJNWFdPdTZjdEhic09kUXllRnZzS3ciLCJtYWMiOiJmYWI2OTJjNjNlMjZjYTE3ZTFlMjc0MGRhMTQ5YWZjMjM3MzhiY2I3MGI2NTY0NDMxYjE2NmQyMTM1NGM4NmM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://ticketgol.test/event-jaunita-runolfsdottir-7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImI0TmloVlg3WW4xTzFoaU9PSUFZOHc9PSIsInZhbHVlIjoiQmE5L2RBamJZWXErRlhucmM5WjBiUXZuTGJOSE16d2RKVGFDajNSOS9XZmF3aU10ZTk0b3lQVEwwZWRkdzQ5SzZuZFdlK2grb2hOOU56c1ZJUHRDaEpZWjB0aitjL3FXNWxjN1R4cEFCWmd2OC83T0wyYVZXWGhna2pxbGJ2UGoiLCJtYWMiOiJkNDE5NmE1MmJiZWE3NjcxOTU0ZjdhNDk0ZDU0YzMzOWIzM2FjODdmZTFlNmEzN2I0MjY2MDc1ZjRkMDczYjBhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-153877267 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153877267\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-635451018 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:15:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkJKQTN3dTRoVnhFRzZJaUxFLzB2OEE9PSIsInZhbHVlIjoiSDBkbFREcE00WDF0R1hWWDV1SUh0VVR0VHFmZ2ZobFdRemVhU2kwY3FvR2w1NDlPejZwU1JybVFYTXZWWGhvMW55WVRUbUcxUk9JTUhnbzVNUHVRSzZ5Z0tDY3ppYVpkSjcxQmVQZVFZKzFTcmQvVGhhbnpubFB3ZEs4YlNEZ1oiLCJtYWMiOiJlOWVjNzFmNTZiMTNhNTI2OWI2ZDFlYWU5N2JkMTJkNzgyNGFmZGE5OGY3Zjc4N2YwNGZhNzA5ZDYyZjFlMGM5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:15:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Ilh5dHFUVitrd3BWZzkxOEk3Q1I0TkE9PSIsInZhbHVlIjoiekhSaUxFZEk5L3UxMlg0SGp2VjVWazVvNTZuZ1Btb0hWNmpWYmhsOW5mb2Z6RWxzem1vQzNRRkd4SzloVCtqUnZBUng2R1JiTHEvWWNRNHU0TzB3VWJPR1dOWG11SEhBbEQzZkNrbVZCZmFzT3hVL3ZObHRxTVYwUmZFTjBCc24iLCJtYWMiOiIzN2FjNmI2ZTU0MzU5OGMwN2Y5OWQwZWRiYzg3YWYyZjljY2JiMDMyNjYzNjQwZGZlNjQwNmRjMWQyYWE5NjYwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:15:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJKQTN3dTRoVnhFRzZJaUxFLzB2OEE9PSIsInZhbHVlIjoiSDBkbFREcE00WDF0R1hWWDV1SUh0VVR0VHFmZ2ZobFdRemVhU2kwY3FvR2w1NDlPejZwU1JybVFYTXZWWGhvMW55WVRUbUcxUk9JTUhnbzVNUHVRSzZ5Z0tDY3ppYVpkSjcxQmVQZVFZKzFTcmQvVGhhbnpubFB3ZEs4YlNEZ1oiLCJtYWMiOiJlOWVjNzFmNTZiMTNhNTI2OWI2ZDFlYWU5N2JkMTJkNzgyNGFmZGE5OGY3Zjc4N2YwNGZhNzA5ZDYyZjFlMGM5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:15:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Ilh5dHFUVitrd3BWZzkxOEk3Q1I0TkE9PSIsInZhbHVlIjoiekhSaUxFZEk5L3UxMlg0SGp2VjVWazVvNTZuZ1Btb0hWNmpWYmhsOW5mb2Z6RWxzem1vQzNRRkd4SzloVCtqUnZBUng2R1JiTHEvWWNRNHU0TzB3VWJPR1dOWG11SEhBbEQzZkNrbVZCZmFzT3hVL3ZObHRxTVYwUmZFTjBCc24iLCJtYWMiOiIzN2FjNmI2ZTU0MzU5OGMwN2Y5OWQwZWRiYzg3YWYyZjljY2JiMDMyNjYzNjQwZGZlNjQwNmRjMWQyYWE5NjYwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:15:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635451018\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/24</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}