[2025-07-01 09:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z"},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z"}]}} 
[2025-07-01 09:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:17:01] local.INFO: Cron job executed {"time":"2025-07-01 09:17:01"} 
[2025-07-01 09:17:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:17:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:17:03] local.INFO: Cron job executed {"time":"2025-07-01 09:17:03"} 
[2025-07-01 09:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z"},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z"}]}} 
[2025-07-01 09:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:18:01] local.INFO: Cron job executed {"time":"2025-07-01 09:18:01"} 
[2025-07-01 09:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:18:03] local.INFO: Cron job executed {"time":"2025-07-01 09:18:03"} 
[2025-07-01 09:19:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z"},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z"}]}} 
[2025-07-01 09:19:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:19:01] local.INFO: Cron job executed {"time":"2025-07-01 09:19:01"} 
[2025-07-01 09:19:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:19:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:19:03] local.INFO: Cron job executed {"time":"2025-07-01 09:19:03"} 
[2025-07-01 09:19:25] local.INFO: Payment intent created {"payment_intent":{"Stripe\\PaymentIntent":{"id":"pi_3Rg0LYRhkfMMoe7t1wm4olLN","object":"payment_intent","amount":60510,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":0,"application":null,"application_fee_amount":null,"automatic_payment_methods":null,"canceled_at":null,"cancellation_reason":null,"capture_method":"automatic_async","client_secret":"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx","confirmation_method":"automatic","created":1751361564,"currency":"eur","customer":null,"description":null,"last_payment_error":null,"latest_charge":null,"livemode":false,"metadata":{"user_id":"7","order_id":"25","customer_email":"<EMAIL>","ticket_reservation_id":"28"},"next_action":null,"on_behalf_of":null,"payment_method":null,"payment_method_configuration_details":null,"payment_method_options":{"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"requires_payment_method","transfer_data":null,"transfer_group":null}}} 
[2025-07-01 09:19:28] local.INFO: Payment intent succeeded {"payment_intent":{"Stripe\\PaymentIntent":{"id":"pi_3Rg0LYRhkfMMoe7t1wm4olLN","object":"payment_intent","amount":60510,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":60510,"application":null,"application_fee_amount":null,"automatic_payment_methods":null,"canceled_at":null,"cancellation_reason":null,"capture_method":"automatic_async","client_secret":"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx","confirmation_method":"automatic","created":1751361564,"currency":"eur","customer":null,"description":null,"last_payment_error":null,"latest_charge":"ch_3Rg0LYRhkfMMoe7t1nEma9A4","livemode":false,"metadata":{"user_id":"7","order_id":"25","customer_email":"<EMAIL>","ticket_reservation_id":"28"},"next_action":null,"on_behalf_of":null,"payment_method":"pm_1Rg0LaRhkfMMoe7tDrKnoulg","payment_method_configuration_details":null,"payment_method_options":{"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-07-01 09:19:28] local.INFO: Order status updated to confirmed  
[2025-07-01 09:19:28] local.INFO: Transaction status updated to completed  
[2025-07-01 09:19:28] local.INFO: Reservation status updated to completed {"ticket_reservation":{"App\\Models\\TicketReservation":{"id":28,"ticket_id":20,"user_id":7,"quantity":"1","price":"489.57","status":"completed","expires_at":"2025-07-01T09:31:00.000000Z","created_at":"2025-07-01T09:16:00.000000Z","updated_at":"2025-07-01T09:19:28.000000Z"}}} 
[2025-07-01 09:19:28] local.INFO: reservedCounter is  {"coutner":{"Redis":[]}} 
[2025-07-01 09:19:28] local.INFO: Charge succeeded {"charge":{"Stripe\\Charge":{"id":"ch_3Rg0LYRhkfMMoe7t1nEma9A4","object":"charge","amount":60510,"amount_captured":60510,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":null,"line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":null,"phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1751361566,"currency":"eur","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":{"user_id":"7","order_id":"25","customer_email":"<EMAIL>","ticket_reservation_id":"28"},"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":62,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3Rg0LYRhkfMMoe7t1wm4olLN","payment_method":"pm_1Rg0LaRhkfMMoe7tDrKnoulg","payment_method_details":{"card":{"amount_authorized":60510,"authorization_code":"526200","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":"pass"},"country":"US","exp_month":4,"exp_year":2029,"extended_authorization":{"status":"disabled"},"fingerprint":"4qHsohpy0cF1sMdU","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"521137211511110","overcapture":{"maximum_amount_capturable":60510,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJ_QjsMGMgYfH1orBJ86LBYlv7MroKVZAJVmfO0ldXg3fI4O7OvOvTWW932avX_nbo154-WA17fozLg-","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-07-01 09:19:52] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z","order":{"id":20,"order_no":"TGO020","buyer_id":5,"ticket_id":8,"ticket_reservation_id":21,"quantity":"1","price":"443.68","total_price":"443.68","service_charge_amount":"88.74","tax_amount":"15.97","grand_total":"548.39","order_meta_data":null,"status":"pending","purchase_date":"2025-06-30","description":null,"created_by":5,"created_at":"2025-06-30T06:55:17.000000Z","updated_at":"2025-06-30T06:55:17.000000Z","deleted_at":null}},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z","order":{"id":21,"order_no":"TGO021","buyer_id":5,"ticket_id":20,"ticket_reservation_id":22,"quantity":"1","price":"489.57","total_price":"489.57","service_charge_amount":"97.91","tax_amount":"17.62","grand_total":"605.10","order_meta_data":null,"status":"pending","purchase_date":"2025-06-30","description":null,"created_by":5,"created_at":"2025-06-30T07:12:17.000000Z","updated_at":"2025-06-30T07:12:17.000000Z","deleted_at":null}}]}} 
[2025-07-01 09:19:52] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:19:52] local.INFO: Cron job executed {"time":"2025-07-01 09:19:52"} 
[2025-07-01 09:20:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z","order":{"id":20,"order_no":"TGO020","buyer_id":5,"ticket_id":8,"ticket_reservation_id":21,"quantity":"1","price":"443.68","total_price":"443.68","service_charge_amount":"88.74","tax_amount":"15.97","grand_total":"548.39","order_meta_data":null,"status":"pending","purchase_date":"2025-06-30","description":null,"created_by":5,"created_at":"2025-06-30T06:55:17.000000Z","updated_at":"2025-06-30T06:55:17.000000Z","deleted_at":null,"transaction":{"id":10,"status":"pending","order_id":20,"currency_code":"EUR","payment_intent_id":"pi_3RfbcXRhkfMMoe7t0Ro9mC9U","payment_method_id":null,"payment_method_type":null,"total_amount":null,"paid_at":null,"refunded_at":null,"card_brand":null,"card_last_four":null,"created_at":"2025-06-30T06:55:17.000000Z","updated_at":"2025-06-30T06:55:21.000000Z"}}},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z","order":{"id":21,"order_no":"TGO021","buyer_id":5,"ticket_id":20,"ticket_reservation_id":22,"quantity":"1","price":"489.57","total_price":"489.57","service_charge_amount":"97.91","tax_amount":"17.62","grand_total":"605.10","order_meta_data":null,"status":"pending","purchase_date":"2025-06-30","description":null,"created_by":5,"created_at":"2025-06-30T07:12:17.000000Z","updated_at":"2025-06-30T07:12:17.000000Z","deleted_at":null,"transaction":{"id":11,"status":"pending","order_id":21,"currency_code":"EUR","payment_intent_id":"pi_3RfbsyRhkfMMoe7t1uYScbpA","payment_method_id":null,"payment_method_type":null,"total_amount":null,"paid_at":null,"refunded_at":null,"card_brand":null,"card_last_four":null,"created_at":"2025-06-30T07:12:17.000000Z","updated_at":"2025-06-30T07:12:17.000000Z"}}}]}} 
[2025-07-01 09:20:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:20:01] local.INFO: Cron job executed {"time":"2025-07-01 09:20:01"} 
[2025-07-01 09:20:09] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:20:09] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:20:09] local.INFO: Cron job executed {"time":"2025-07-01 09:20:09"} 
[2025-07-01 09:20:12] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:20:12] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:20:12] local.INFO: Cron job executed {"time":"2025-07-01 09:20:12"} 
[2025-07-01 09:20:19] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:20:19] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:20:19] local.INFO: Cron job executed {"time":"2025-07-01 09:20:19"} 
[2025-07-01 09:21:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:21:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:21:01] local.INFO: Cron job executed {"time":"2025-07-01 09:21:01"} 
[2025-07-01 09:21:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:21:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:21:02] local.INFO: Cron job executed {"time":"2025-07-01 09:21:02"} 
[2025-07-01 09:22:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:22:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:22:01] local.INFO: Cron job executed {"time":"2025-07-01 09:22:01"} 
[2025-07-01 09:22:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:22:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:22:02] local.INFO: Cron job executed {"time":"2025-07-01 09:22:02"} 
[2025-07-01 09:23:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:23:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:23:01] local.INFO: Cron job executed {"time":"2025-07-01 09:23:01"} 
[2025-07-01 09:23:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:23:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:23:03] local.INFO: Cron job executed {"time":"2025-07-01 09:23:03"} 
[2025-07-01 09:24:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:24:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:24:01] local.INFO: Cron job executed {"time":"2025-07-01 09:24:01"} 
[2025-07-01 09:24:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:24:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:24:02] local.INFO: Cron job executed {"time":"2025-07-01 09:24:02"} 
[2025-07-01 09:25:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:25:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:25:01] local.INFO: Cron job executed {"time":"2025-07-01 09:25:01"} 
[2025-07-01 09:25:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:25:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:25:02] local.INFO: Cron job executed {"time":"2025-07-01 09:25:02"} 
[2025-07-01 09:26:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:26:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:26:01] local.INFO: Cron job executed {"time":"2025-07-01 09:26:01"} 
[2025-07-01 09:26:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:26:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:26:02] local.INFO: Cron job executed {"time":"2025-07-01 09:26:02"} 
[2025-07-01 09:27:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:27:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:27:01] local.INFO: Cron job executed {"time":"2025-07-01 09:27:01"} 
[2025-07-01 09:27:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:27:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:27:02] local.INFO: Cron job executed {"time":"2025-07-01 09:27:02"} 
[2025-07-01 09:28:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:28:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:28:01] local.INFO: Cron job executed {"time":"2025-07-01 09:28:01"} 
[2025-07-01 09:28:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:28:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:28:02] local.INFO: Cron job executed {"time":"2025-07-01 09:28:02"} 
[2025-07-01 09:29:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:29:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:29:01] local.INFO: Cron job executed {"time":"2025-07-01 09:29:01"} 
[2025-07-01 09:29:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:29:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:29:02] local.INFO: Cron job executed {"time":"2025-07-01 09:29:02"} 
[2025-07-01 09:30:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:30:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:30:01] local.INFO: Cron job executed {"time":"2025-07-01 09:30:01"} 
[2025-07-01 09:30:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:30:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:30:03] local.INFO: Cron job executed {"time":"2025-07-01 09:30:03"} 
[2025-07-01 09:31:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:31:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:31:01] local.INFO: Cron job executed {"time":"2025-07-01 09:31:01"} 
[2025-07-01 09:31:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:31:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:31:02] local.INFO: Cron job executed {"time":"2025-07-01 09:31:02"} 
[2025-07-01 09:32:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:32:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:32:01] local.INFO: Cron job executed {"time":"2025-07-01 09:32:01"} 
[2025-07-01 09:32:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:32:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:32:03] local.INFO: Cron job executed {"time":"2025-07-01 09:32:03"} 
[2025-07-01 09:33:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:33:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:33:01] local.INFO: Cron job executed {"time":"2025-07-01 09:33:01"} 
[2025-07-01 09:33:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:33:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:33:03] local.INFO: Cron job executed {"time":"2025-07-01 09:33:03"} 
[2025-07-01 09:34:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:34:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:34:01] local.INFO: Cron job executed {"time":"2025-07-01 09:34:01"} 
[2025-07-01 09:34:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:34:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:34:02] local.INFO: Cron job executed {"time":"2025-07-01 09:34:02"} 
[2025-07-01 09:35:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:35:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:35:01] local.INFO: Cron job executed {"time":"2025-07-01 09:35:01"} 
[2025-07-01 09:35:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:35:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:35:02] local.INFO: Cron job executed {"time":"2025-07-01 09:35:02"} 
[2025-07-01 09:36:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:36:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:36:01] local.INFO: Cron job executed {"time":"2025-07-01 09:36:01"} 
[2025-07-01 09:36:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:36:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:36:03] local.INFO: Cron job executed {"time":"2025-07-01 09:36:03"} 
[2025-07-01 09:37:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:37:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:37:01] local.INFO: Cron job executed {"time":"2025-07-01 09:37:01"} 
[2025-07-01 09:37:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:37:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:37:03] local.INFO: Cron job executed {"time":"2025-07-01 09:37:03"} 
[2025-07-01 09:38:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:38:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:38:01] local.INFO: Cron job executed {"time":"2025-07-01 09:38:01"} 
[2025-07-01 09:38:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:38:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:38:03] local.INFO: Cron job executed {"time":"2025-07-01 09:38:03"} 
[2025-07-01 09:39:02] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:39:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:39:02] local.INFO: Cron job executed {"time":"2025-07-01 09:39:02"} 
[2025-07-01 09:39:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:39:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:39:03] local.INFO: Cron job executed {"time":"2025-07-01 09:39:03"} 
[2025-07-01 09:40:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:40:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:40:01] local.INFO: Cron job executed {"time":"2025-07-01 09:40:01"} 
[2025-07-01 09:40:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:40:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:40:02] local.INFO: Cron job executed {"time":"2025-07-01 09:40:02"} 
[2025-07-01 09:41:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:41:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:41:01] local.INFO: Cron job executed {"time":"2025-07-01 09:41:01"} 
[2025-07-01 09:41:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:41:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:41:03] local.INFO: Cron job executed {"time":"2025-07-01 09:41:03"} 
[2025-07-01 09:42:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:42:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:42:01] local.INFO: Cron job executed {"time":"2025-07-01 09:42:01"} 
[2025-07-01 09:42:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:42:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:42:02] local.INFO: Cron job executed {"time":"2025-07-01 09:42:02"} 
[2025-07-01 09:43:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:43:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:43:01] local.INFO: Cron job executed {"time":"2025-07-01 09:43:01"} 
[2025-07-01 09:43:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:43:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:43:02] local.INFO: Cron job executed {"time":"2025-07-01 09:43:02"} 
[2025-07-01 09:44:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:44:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:44:01] local.INFO: Cron job executed {"time":"2025-07-01 09:44:01"} 
[2025-07-01 09:44:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:44:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:44:02] local.INFO: Cron job executed {"time":"2025-07-01 09:44:02"} 
[2025-07-01 09:45:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:45:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:45:01] local.INFO: Cron job executed {"time":"2025-07-01 09:45:01"} 
[2025-07-01 09:45:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:45:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:45:02] local.INFO: Cron job executed {"time":"2025-07-01 09:45:02"} 
[2025-07-01 09:46:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:46:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:46:01] local.INFO: Cron job executed {"time":"2025-07-01 09:46:01"} 
[2025-07-01 09:46:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:46:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:46:03] local.INFO: Cron job executed {"time":"2025-07-01 09:46:03"} 
[2025-07-01 09:47:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:47:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:47:01] local.INFO: Cron job executed {"time":"2025-07-01 09:47:01"} 
[2025-07-01 09:47:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:47:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:47:02] local.INFO: Cron job executed {"time":"2025-07-01 09:47:02"} 
[2025-07-01 09:48:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:48:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:48:01] local.INFO: Cron job executed {"time":"2025-07-01 09:48:01"} 
[2025-07-01 09:48:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:48:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:48:02] local.INFO: Cron job executed {"time":"2025-07-01 09:48:02"} 
[2025-07-01 09:49:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:49:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:49:01] local.INFO: Cron job executed {"time":"2025-07-01 09:49:01"} 
[2025-07-01 09:49:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:49:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:49:02] local.INFO: Cron job executed {"time":"2025-07-01 09:49:02"} 
[2025-07-01 09:50:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:50:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:50:01] local.INFO: Cron job executed {"time":"2025-07-01 09:50:01"} 
[2025-07-01 09:50:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:50:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:50:03] local.INFO: Cron job executed {"time":"2025-07-01 09:50:03"} 
[2025-07-01 09:51:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:51:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:51:01] local.INFO: Cron job executed {"time":"2025-07-01 09:51:01"} 
[2025-07-01 09:51:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:51:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:51:02] local.INFO: Cron job executed {"time":"2025-07-01 09:51:02"} 
[2025-07-01 09:52:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:52:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:52:01] local.INFO: Cron job executed {"time":"2025-07-01 09:52:01"} 
[2025-07-01 09:52:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:52:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:52:03] local.INFO: Cron job executed {"time":"2025-07-01 09:52:03"} 
[2025-07-01 09:53:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:53:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:53:01] local.INFO: Cron job executed {"time":"2025-07-01 09:53:01"} 
[2025-07-01 09:53:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:53:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:53:02] local.INFO: Cron job executed {"time":"2025-07-01 09:53:02"} 
[2025-07-01 09:54:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:54:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:54:01] local.INFO: Cron job executed {"time":"2025-07-01 09:54:01"} 
[2025-07-01 09:54:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:54:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:54:02] local.INFO: Cron job executed {"time":"2025-07-01 09:54:02"} 
[2025-07-01 09:55:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:55:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:55:01] local.INFO: Cron job executed {"time":"2025-07-01 09:55:01"} 
[2025-07-01 09:55:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:55:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:55:03] local.INFO: Cron job executed {"time":"2025-07-01 09:55:03"} 
[2025-07-01 09:56:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:56:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:56:01] local.INFO: Cron job executed {"time":"2025-07-01 09:56:01"} 
[2025-07-01 09:56:02] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:56:02] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:56:02] local.INFO: Cron job executed {"time":"2025-07-01 09:56:02"} 
