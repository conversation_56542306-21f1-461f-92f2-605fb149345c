[2025-07-01 09:17:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z"},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z"}]}} 
[2025-07-01 09:17:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:17:01] local.INFO: Cron job executed {"time":"2025-07-01 09:17:01"} 
[2025-07-01 09:17:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:17:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:17:03] local.INFO: Cron job executed {"time":"2025-07-01 09:17:03"} 
[2025-07-01 09:18:01] local.INFO: Expired processing reservations {"processing_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":21,"ticket_id":8,"user_id":5,"quantity":"1","price":"443.68","status":"processing","expires_at":"2025-06-30T07:03:33.000000Z","created_at":"2025-06-30T06:48:33.000000Z","updated_at":"2025-06-30T06:55:17.000000Z"},{"id":22,"ticket_id":20,"user_id":5,"quantity":"1","price":"489.57","status":"processing","expires_at":"2025-06-30T07:24:51.000000Z","created_at":"2025-06-30T07:09:51.000000Z","updated_at":"2025-06-30T07:12:17.000000Z"}]}} 
[2025-07-01 09:18:01] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[21,22]}} 
[2025-07-01 09:18:01] local.INFO: Cron job executed {"time":"2025-07-01 09:18:01"} 
[2025-07-01 09:18:03] local.INFO: Expired active reservations {"active_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-01 09:18:03] local.INFO: Updating expired records {"ids":{"Illuminate\\Support\\Collection":[]}} 
[2025-07-01 09:18:03] local.INFO: Cron job executed {"time":"2025-07-01 09:18:03"} 
