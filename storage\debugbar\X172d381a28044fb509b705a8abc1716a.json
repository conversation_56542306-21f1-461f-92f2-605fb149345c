{"__meta": {"id": "X172d381a28044fb509b705a8abc1716a", "datetime": "2025-07-01 09:19:25", "utime": **********.058926, "method": "POST", "uri": "/api/v1/orders/create", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751361563.847374, "end": **********.058943, "duration": 1.21156907081604, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1751361563.847374, "relative_start": 0, "end": 1751361563.995947, "relative_end": 1751361563.995947, "duration": 0.1485729217529297, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751361563.995961, "relative_start": 0.14858698844909668, "end": **********.058946, "relative_end": 2.86102294921875e-06, "duration": 1.0629849433898926, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 8721272, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/orders/create", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@store", "namespace": null, "prefix": "api/v1/orders", "where": [], "as": "api.orders.store", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=34\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:34-58</a>"}, "queries": {"nb_statements": 38, "nb_visible_statements": 40, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05519, "accumulated_duration_str": "55.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs' limit 1", "type": "query", "params": [], "bindings": ["huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.0078652, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.142}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.01168, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.142, "width_percent": 1.395}, {"sql": "select `id`, `ticket_id`, `user_id`, `quantity`, `price`, `status`, `expires_at` from `ticket_reservations` where `ticket_reservations`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 20, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 21, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.031374, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 2.537, "width_percent": 1.196}, {"sql": "select `id`, `ticket_type`, `event_id`, `sector_id`, `currency_code`, `quantity` from `tickets` where `tickets`.`id` in (20) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 24, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 25, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 26, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.03444, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 3.733, "width_percent": 0.689}, {"sql": "select `id`, `date`, `time`, `timezone`, `stadium_id` from `events` where `events`.`id` in (7) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 29, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 31, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.036966, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 4.421, "width_percent": 1.25}, {"sql": "select `event_id`, `name`, `locale` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.040259, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 5.671, "width_percent": 1.033}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (5) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.0422819, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 6.704, "width_percent": 0.743}, {"sql": "select `stadium_id`, `name`, `locale` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.0443919, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 7.447, "width_percent": 0.67}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.046174, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 8.117, "width_percent": 0.652}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 43, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 44, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 45, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 46, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 47, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.0479329, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 43, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 8.77, "width_percent": 0.743}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.050095, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 9.513, "width_percent": 0.924}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (7) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 33, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 34, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 35, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.053169, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 10.437, "width_percent": 1.486}, {"sql": "select `restriction_id`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (6, 7, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 37, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 38, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 39, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 40, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 41, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.056435, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 37, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 11.922, "width_percent": 1.25}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.0587711, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 13.173, "width_percent": 0.852}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (45) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 29, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 31, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.0607908, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 14.024, "width_percent": 0.779}, {"sql": "select `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (45)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.062664, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 14.803, "width_percent": 0.725}, {"sql": "select `restrictions`.`id`, `ticket_restrictions`.`ticket_id` as `pivot_ticket_id`, `ticket_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `ticket_restrictions` on `restrictions`.`id` = `ticket_restrictions`.`restriction_id` where `ticket_restrictions`.`ticket_id` in (20) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 28, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 29, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 30, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.06444, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 27, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 15.528, "width_percent": 3.569}, {"sql": "select `restriction_id`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (3, 4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 33, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 34, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 35, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.067663, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 19.098, "width_percent": 0.779}, {"sql": "select `id`, `name`, `email`, `user_type` from `users` where `users`.`id` in (7) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 24, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 25, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 26, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.06958, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 19.877, "width_percent": 0.978}, {"sql": "select `user_id`, `address`, `city`, `phone`, `zip`, `country_id` from `user_details` where `user_details`.`user_id` in (7) and `user_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 29, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 31, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.071959, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 20.855, "width_percent": 3.098}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (192) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.075002, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 23.954, "width_percent": 0.689}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (192)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.076565, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 24.642, "width_percent": 0.707}, {"sql": "select `id`, `ticket_reservation_id` from `orders` where `orders`.`ticket_reservation_id` in (28) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 24, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 25, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 26, "namespace": null, "name": "app/Rules/ValidateTicketReservationKey.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateTicketReservationKey.php", "line": 29}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}], "start": **********.0779462, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 25.349, "width_percent": 0.833}, {"sql": "select count(*) as aggregate from `events` where `id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 982}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 953}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}], "start": **********.08022, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 26.182, "width_percent": 0.87}, {"sql": "select * from `ticket_reservations` where `ticket_reservations`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Rules/ValidateEventMatchesTicketReservation.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateEventMatchesTicketReservation.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 885}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 674}], "start": **********.081828, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 27.052, "width_percent": 0.797}, {"sql": "select * from `tickets` where `tickets`.`id` = 20 and `tickets`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Rules/ValidateEventMatchesTicketReservation.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateEventMatchesTicketReservation.php", "line": 33}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/InvokableValidationRule.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\InvokableValidationRule.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 885}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 674}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}], "start": **********.083229, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ValidateEventMatchesTicketReservation.php:33", "source": {"index": 21, "namespace": null, "name": "app/Rules/ValidateEventMatchesTicketReservation.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Rules\\ValidateEventMatchesTicketReservation.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRules%2FValidateEventMatchesTicketReservation.php&line=33", "ajax": false, "filename": "ValidateEventMatchesTicketReservation.php", "line": "33"}, "connection": "ticketgol", "explain": null, "start_percent": 27.849, "width_percent": 0.616}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 37}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.089913, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OrderController.php:37", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=37", "ajax": false, "filename": "OrderController.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 28.465, "width_percent": 0}, {"sql": "select * from `ticket_reservations` where `ticket_reservations`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Http/Requests/Api/V1/OrderStoreRequest.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Requests\\Api\\V1\\OrderStoreRequest.php", "line": 37}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.0907419, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 28.465, "width_percent": 1.667}, {"sql": "insert into `orders` (`buyer_id`, `ticket_id`, `ticket_reservation_id`, `quantity`, `price`, `total_price`, `service_charge_amount`, `tax_amount`, `grand_total`, `status`, `created_by`, `purchase_date`, `updated_at`, `created_at`) values (7, 20, '28', 1, '489.57', '489.57', '97.91', '17.62', '605.1', 'pending', 7, '2025-07-01 09:19:24', '2025-07-01 09:19:24', '2025-07-01 09:19:24')", "type": "query", "params": [], "bindings": [7, 20, "28", 1, "489.57", "489.57", "97.91", "17.62", "605.1", "pending", 7, "2025-07-01 09:19:24", "2025-07-01 09:19:24", "2025-07-01 09:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 40}, {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 50}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.093735, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:40", "source": {"index": 20, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=40", "ajax": false, "filename": "OrderRepository.php", "line": "40"}, "connection": "ticketgol", "explain": null, "start_percent": 30.132, "width_percent": 4.33}, {"sql": "update `orders` set `buyer_id` = 7, `ticket_id` = 20, `ticket_reservation_id` = '28', `quantity` = 1, `price` = '489.57', `total_price` = '489.57', `service_charge_amount` = '97.91', `tax_amount` = '17.62', `grand_total` = '605.1', `status` = 'pending', `created_by` = 7, `purchase_date` = '2025-07-01 09:19:24', `created_at` = '2025-07-01 09:19:24', `id` = 25, `order_no` = 'TGO025', `orders`.`updated_at` = '2025-07-01 09:19:24' where `id` = 25", "type": "query", "params": [], "bindings": [7, 20, "28", 1, "489.57", "489.57", "97.91", "17.62", "605.1", "pending", 7, "2025-07-01 09:19:24", "2025-07-01 09:19:24", 25, "TGO025", "2025-07-01 09:19:24", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Order.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Models\\Order.php", "line": 31}, {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 40}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 50}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 40}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.0990498, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Order.php:31", "source": {"index": 14, "namespace": null, "name": "app/Models/Order.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Models\\Order.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=31", "ajax": false, "filename": "Order.php", "line": "31"}, "connection": "ticketgol", "explain": null, "start_percent": 34.463, "width_percent": 2.374}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `batch_uuid`, `event`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('Resource', '{\\\"buyer_id\\\":7,\\\"ticket_id\\\":20,\\\"ticket_reservation_id\\\":\\\"28\\\",\\\"quantity\\\":1,\\\"price\\\":\\\"489.57\\\",\\\"total_price\\\":\\\"489.57\\\",\\\"service_charge_amount\\\":\\\"97.91\\\",\\\"tax_amount\\\":\\\"17.62\\\",\\\"grand_total\\\":\\\"605.1\\\",\\\"status\\\":\\\"pending\\\",\\\"created_by\\\":7,\\\"purchase_date\\\":\\\"2025-07-01T09:19:24.092759Z\\\",\\\"updated_at\\\":\\\"2025-07-01 09:19:24\\\",\\\"created_at\\\":\\\"2025-07-01 09:19:24\\\",\\\"id\\\":25,\\\"order_no\\\":\\\"TGO025\\\"}', 7, 'App\\\\Models\\\\User', null, 'Updated', 25, 'App\\\\Models\\\\Order', 'Order Updated by <PERSON><PERSON>', '2025-07-01 09:19:24', '2025-07-01 09:19:24')", "type": "query", "params": [], "bindings": ["Resource", "{\"buyer_id\":7,\"ticket_id\":20,\"ticket_reservation_id\":\"28\",\"quantity\":1,\"price\":\"489.57\",\"total_price\":\"489.57\",\"service_charge_amount\":\"97.91\",\"tax_amount\":\"17.62\",\"grand_total\":\"605.1\",\"status\":\"pending\",\"created_by\":7,\"purchase_date\":\"2025-07-01T09:19:24.092759Z\",\"updated_at\":\"2025-07-01 09:19:24\",\"created_at\":\"2025-07-01 09:19:24\",\"id\":25,\"order_no\":\"TGO025\"}", 7, "App\\Models\\User", null, "Updated", 25, "App\\Models\\Order", "Order Updated by <PERSON><PERSON><PERSON>", "2025-07-01 09:19:24", "2025-07-01 09:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 91}, {"index": 24, "namespace": null, "name": "app/Models/Order.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Models\\Order.php", "line": 31}, {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 40}], "start": **********.111993, "duration": 0.00878, "duration_str": "8.78ms", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 36.836, "width_percent": 15.909}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `batch_uuid`, `event`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('Resource', '{\\\"buyer_id\\\":7,\\\"ticket_id\\\":20,\\\"ticket_reservation_id\\\":\\\"28\\\",\\\"quantity\\\":1,\\\"price\\\":\\\"489.57\\\",\\\"total_price\\\":\\\"489.57\\\",\\\"service_charge_amount\\\":\\\"97.91\\\",\\\"tax_amount\\\":\\\"17.62\\\",\\\"grand_total\\\":\\\"605.1\\\",\\\"status\\\":\\\"pending\\\",\\\"created_by\\\":7,\\\"purchase_date\\\":\\\"2025-07-01T09:19:24.092759Z\\\",\\\"updated_at\\\":\\\"2025-07-01 09:19:24\\\",\\\"created_at\\\":\\\"2025-07-01 09:19:24\\\",\\\"id\\\":25,\\\"order_no\\\":\\\"TGO025\\\"}', 7, 'App\\\\Models\\\\User', null, 'Created', 25, 'App\\\\Models\\\\Order', 'Order Created by Ms. <PERSON>', '2025-07-01 09:19:24', '2025-07-01 09:19:24')", "type": "query", "params": [], "bindings": ["Resource", "{\"buyer_id\":7,\"ticket_id\":20,\"ticket_reservation_id\":\"28\",\"quantity\":1,\"price\":\"489.57\",\"total_price\":\"489.57\",\"service_charge_amount\":\"97.91\",\"tax_amount\":\"17.62\",\"grand_total\":\"605.1\",\"status\":\"pending\",\"created_by\":7,\"purchase_date\":\"2025-07-01T09:19:24.092759Z\",\"updated_at\":\"2025-07-01 09:19:24\",\"created_at\":\"2025-07-01 09:19:24\",\"id\":25,\"order_no\":\"TGO025\"}", 7, "App\\Models\\User", null, "Created", 25, "App\\Models\\Order", "Order Created by <PERSON>. <PERSON><PERSON>", "2025-07-01 09:19:24", "2025-07-01 09:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 79}, {"index": 29, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 40}, {"index": 30, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 50}], "start": **********.123766, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 52.745, "width_percent": 1.196}, {"sql": "insert into `attendees` (`name`, `email`, `gender`, `dob`, `order_id`, `updated_at`, `created_at`) values ('Rhoda Chambers', '<EMAIL>', 'male', '2025-04-08', 25, '2025-07-01 09:19:24', '2025-07-01 09:19:24')", "type": "query", "params": [], "bindings": ["Rhoda Chambers", "<EMAIL>", "male", "2025-04-08", 25, "2025-07-01 09:19:24", "2025-07-01 09:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 55}, {"index": 20, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 50}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1267722, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:55", "source": {"index": 19, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=55", "ajax": false, "filename": "OrderRepository.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 53.941, "width_percent": 4.385}, {"sql": "insert into `order_transactions` (`status`, `order_id`, `currency_code`, `updated_at`, `created_at`) values ('pending', 25, 'EUR', '2025-07-01 09:19:24', '2025-07-01 09:19:24')", "type": "query", "params": [], "bindings": ["pending", 25, "EUR", "2025-07-01 09:19:24", "2025-07-01 09:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 17}, {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 52}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.131912, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "OrderTransactionRepository.php:17", "source": {"index": 20, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderTransactionRepository.php&line=17", "ajax": false, "filename": "OrderTransactionRepository.php", "line": "17"}, "connection": "ticketgol", "explain": null, "start_percent": 58.326, "width_percent": 4.113}, {"sql": "select * from `ticket_reservations` where `ticket_reservations`.`id` = '28' limit 1", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 124}, {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 54}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.135843, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "TicketReservationRepository.php:124", "source": {"index": 20, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FTicketReservationRepository.php&line=124", "ajax": false, "filename": "TicketReservationRepository.php", "line": "124"}, "connection": "ticketgol", "explain": null, "start_percent": 62.439, "width_percent": 1.196}, {"sql": "update `ticket_reservations` set `status` = 'processing', `ticket_reservations`.`updated_at` = '2025-07-01 09:19:24' where `id` = 28", "type": "query", "params": [], "bindings": ["processing", "2025-07-01 09:19:24", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 54}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.138552, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "TicketReservationRepository.php:126", "source": {"index": 15, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FTicketReservationRepository.php&line=126", "ajax": false, "filename": "TicketReservationRepository.php", "line": "126"}, "connection": "ticketgol", "explain": null, "start_percent": 63.635, "width_percent": 2.464}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"16be6e4c-c14a-4858-bd62-0ee85339a8c0\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateOrderMetaDataJob\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\UpdateOrderMetaDataJob\\\",\\\"command\\\":\\\"O:31:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\UpdateOrderMetaDataJob\\\\\\\":1:{s:10:\\\\\\\"\\\\u0000*\\\\u0000orderId\\\\\\\";i:25;}\\\"},\\\"telescope_uuid\\\":\\\"9f491063-f403-4962-b976-18fe0113f55f\\\",\\\"sentry_baggage_data\\\":\\\"sentry-trace_id=0121850810ca42c099f7ad72c479b1ca,sentry-sample_rate=1,sentry-transaction=%2Fapi%2Fv1%2Forders%2Fcreate,sentry-public_key=a15e9d05dc1534625d8ed86a4c6054c4,sentry-environment=local,sentry-sampled=true,sentry-sample_rand=0.731464\\\",\\\"sentry_trace_parent_data\\\":\\\"0121850810ca42c099f7ad72c479b1ca-06b270bfc6024003-1\\\",\\\"sentry_publish_time\\\":**********.150341}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"16be6e4c-c14a-4858-bd62-0ee85339a8c0\",\"displayName\":\"App\\\\Jobs\\\\UpdateOrderMetaDataJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQ<PERSON>ued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\UpdateOrderMetaDataJob\",\"command\":\"O:31:\\\"App\\\\Jobs\\\\UpdateOrderMetaDataJob\\\":1:{s:10:\\\"\\u0000*\\u0000orderId\\\";i:25;}\"},\"telescope_uuid\":\"9f491063-f403-4962-b976-18fe0113f55f\",\"sentry_baggage_data\":\"sentry-trace_id=0121850810ca42c099f7ad72c479b1ca,sentry-sample_rate=1,sentry-transaction=%2Fapi%2Fv1%2Forders%2Fcreate,sentry-public_key=a15e9d05dc1534625d8ed86a4c6054c4,sentry-environment=local,sentry-sampled=true,sentry-sample_rand=0.731464\",\"sentry_trace_parent_data\":\"0121850810ca42c099f7ad72c479b1ca-06b270bfc6024003-1\",\"sentry_publish_time\":**********.150341}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 99}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 338}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 244}], "start": **********.152601, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:188", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=188", "ajax": false, "filename": "DatabaseQueue.php", "line": "188"}, "connection": "ticketgol", "explain": null, "start_percent": 66.099, "width_percent": 3.406}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 41}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.16967, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OrderController.php:41", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=41", "ajax": false, "filename": "OrderController.php", "line": "41"}, "connection": "ticketgol", "explain": null, "start_percent": 69.505, "width_percent": 0}, {"sql": "update `order_transactions` set `payment_intent_id` = 'pi_3Rg0LYRhkfMMoe7t1wm4olLN', `order_transactions`.`updated_at` = '2025-07-01 09:19:25' where `id` = 15", "type": "query", "params": [], "bindings": ["pi_3Rg0LYRhkfMMoe7t1wm4olLN", "2025-07-01 09:19:25", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.035109, "duration": 0.00923, "duration_str": "9.23ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:44", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=44", "ajax": false, "filename": "OrderController.php", "line": "44"}, "connection": "ticketgol", "explain": null, "start_percent": 69.505, "width_percent": 16.724}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.049365, "duration": 0.0076, "duration_str": "7.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 86.229, "width_percent": 13.771}]}, "models": {"data": {"App\\Models\\Restriction": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\TicketReservation": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicketReservation.php&line=1", "ajax": false, "filename": "TicketReservation.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\Country": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\UserDetail": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUserDetail.php&line=1", "ajax": false, "filename": "UserDetail.php", "line": "?"}}}, "count": 31, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders/24\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f491065-59ca-43f4-8b82-b7b25371e820\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders/create", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ticket_reservation_id</span>\" => \"<span class=sf-dump-str title=\"200 characters\">eyJpdiI6IjFnazhPdDY3S2p1ejNpK1prSUszZmc9PSIsInZhbHVlIjoiS2h0MUI0MnEwUFdLWVd6WjFKQVpIZz09IiwibWFjIjoiOGZkYmIxMmRlNDIxZTc2MzM0ZTRjMGNiMDFhNzQ5NjA0YmJiMmY4YjMzZTI1NmMxODQxMjQ3YTFjOGU3MWJkMyIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>currency_code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>attendees</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Rhoda Chambers</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n      \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-04-08</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-849589152 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"910 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6IjRGaWRxUkZHcTFoMHFYT0lQdmo5a3c9PSIsInZhbHVlIjoiQjFWSm9BTTNVbFJacEtFSzJOZUhNUWVrNDV5L3F6RXZnWDRmVjg3cHFMUXVDSUw3NjlFSGF5TzRraENLM2FUSVM2TTB2NEV3OGQzWWZHeFRaNVRMblRyY3QrbURxV05HQng2Y0ZMYUxFLzh4S0FrcENPYmxzMFM4bFRhbEpleXQiLCJtYWMiOiI1OGIwZTY2ZGE5YTc4ZDE1OGZhMzhkODEzNTdkYzgzZjk0YTQwOGRlYTFiNDliMDk4OTQyOWNiMmMxZWY1MjVmIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InNvZjFnTnd0dldkQm0zWTkxbnZoM1E9PSIsInZhbHVlIjoiYWF6OUYrRCtmVlFHRTNnQ1pRNXVOZmpOL0J1cE5HMHFGWWg0QW5mR05Da0tXU1I5TXZYeENGb2Q0K1B3NHUrQXVnUVEwWVg5bnp3cndrb2hnamNNWk9nL3pUbW5pTnNRT1VJVCtsRVl2QkdFTUV0M1orSHFKd2pMNGREMG03b0MiLCJtYWMiOiIxMjYwNjg4NTFjNTU0NmNhMDllNmJkZmExMjJiZDMwYmZjZThiN2Q1NjgzOWNlNWJjYmNkYzVjZTg0ZjQ5MTRkIiwidGFnIjoiIn0%3D; __stripe_sid=fbeba680-a0c5-44e0-b5f7-7f6c49bc688dd7312d</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"238 characters\">http://ticketgol.test/ticket/checkout/eyJpdiI6IjFnazhPdDY3S2p1ejNpK1prSUszZmc9PSIsInZhbHVlIjoiS2h0MUI0MnEwUFdLWVd6WjFKQVpIZz09IiwibWFjIjoiOGZkYmIxMmRlNDIxZTc2MzM0ZTRjMGNiMDFhNzQ5NjA0YmJiMmY4YjMzZTI1NmMxODQxMjQ3YTFjOGU3MWJkMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRGaWRxUkZHcTFoMHFYT0lQdmo5a3c9PSIsInZhbHVlIjoiQjFWSm9BTTNVbFJacEtFSzJOZUhNUWVrNDV5L3F6RXZnWDRmVjg3cHFMUXVDSUw3NjlFSGF5TzRraENLM2FUSVM2TTB2NEV3OGQzWWZHeFRaNVRMblRyY3QrbURxV05HQng2Y0ZMYUxFLzh4S0FrcENPYmxzMFM4bFRhbEpleXQiLCJtYWMiOiI1OGIwZTY2ZGE5YTc4ZDE1OGZhMzhkODEzNTdkYzgzZjk0YTQwOGRlYTFiNDliMDk4OTQyOWNiMmMxZWY1MjVmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">369</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849589152\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-309703150 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs</span>\"\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309703150\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1589159371 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:19:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlIxTnZ2T1NtWit5YVJpaDlJSXhZR1E9PSIsInZhbHVlIjoiREx4UjF5R2RjREFGTy8wSmtLb3FlSTg4bHRVVkxKcUhpQVBFdmEzb3RMZGJQVERLR2hySmtDZS93UkxlQ1hUT2Z2RlhxOFB6K2RjV29kbTZCR2NQZTJUV0Z5M3plalVFN3p2dS9rT3VOamRabElqUGd4Tk5SNGl2VFlCZUNob2IiLCJtYWMiOiI0NDMzMzkwNjExOTk3N2JhOGEyODMyMmI0NGNlZmEwYWIyMDFhMTk5ZjAzY2E2NzJjYTMyZGUwZTQ4OTk4NDFlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:19:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IitvYm5JbkJ5enJjcmZIV2V1Z1Z4L0E9PSIsInZhbHVlIjoidjFIMHoya2VzeFZaZXhHTGlZRVJZbFhzSzcwMm5hbG5EblMzNEtGY2NJOElCMTRjMFRzbVJEcm1DMnMrSllPVXpWRTRIVGFDMDRrRmJBd3VDdUV2Qmg4ZDZHRFZwU2laa093M2gyanBXVEJnWW9GL2R6WjNud3ZWWkZPVWJoMUEiLCJtYWMiOiI4N2NkMmViMGU5YTMyNDRjNjQzZmM5N2YzYmMwNDRmYjAyZTgxMjNlMTNkOTI1MzczZWI3ZWZiZGU0MDRjMGUzIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:19:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlIxTnZ2T1NtWit5YVJpaDlJSXhZR1E9PSIsInZhbHVlIjoiREx4UjF5R2RjREFGTy8wSmtLb3FlSTg4bHRVVkxKcUhpQVBFdmEzb3RMZGJQVERLR2hySmtDZS93UkxlQ1hUT2Z2RlhxOFB6K2RjV29kbTZCR2NQZTJUV0Z5M3plalVFN3p2dS9rT3VOamRabElqUGd4Tk5SNGl2VFlCZUNob2IiLCJtYWMiOiI0NDMzMzkwNjExOTk3N2JhOGEyODMyMmI0NGNlZmEwYWIyMDFhMTk5ZjAzY2E2NzJjYTMyZGUwZTQ4OTk4NDFlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:19:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IitvYm5JbkJ5enJjcmZIV2V1Z1Z4L0E9PSIsInZhbHVlIjoidjFIMHoya2VzeFZaZXhHTGlZRVJZbFhzSzcwMm5hbG5EblMzNEtGY2NJOElCMTRjMFRzbVJEcm1DMnMrSllPVXpWRTRIVGFDMDRrRmJBd3VDdUV2Qmg4ZDZHRFZwU2laa093M2gyanBXVEJnWW9GL2R6WjNud3ZWWkZPVWJoMUEiLCJtYWMiOiI4N2NkMmViMGU5YTMyNDRjNjQzZmM5N2YzYmMwNDRmYjAyZTgxMjNlMTNkOTI1MzczZWI3ZWZiZGU0MDRjMGUzIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:19:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589159371\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/24</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}