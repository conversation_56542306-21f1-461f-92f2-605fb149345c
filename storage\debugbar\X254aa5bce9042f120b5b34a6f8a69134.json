{"__meta": {"id": "X254aa5bce9042f120b5b34a6f8a69134", "datetime": "2025-07-01 09:19:25", "utime": **********.409528, "method": "POST", "uri": "/api/checkout/webhook", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[09:19:25] LOG.info: Payment intent created {\n    \"payment_intent\": {\n        \"id\": \"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\n        \"object\": \"payment_intent\",\n        \"amount\": 60510,\n        \"amount_capturable\": 0,\n        \"amount_details\": {\n            \"tip\": []\n        },\n        \"amount_received\": 0,\n        \"application\": null,\n        \"application_fee_amount\": null,\n        \"automatic_payment_methods\": null,\n        \"canceled_at\": null,\n        \"cancellation_reason\": null,\n        \"capture_method\": \"automatic_async\",\n        \"client_secret\": \"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx\",\n        \"confirmation_method\": \"automatic\",\n        \"created\": 1751361564,\n        \"currency\": \"eur\",\n        \"customer\": null,\n        \"description\": null,\n        \"last_payment_error\": null,\n        \"latest_charge\": null,\n        \"livemode\": false,\n        \"metadata\": {\n            \"user_id\": \"7\",\n            \"order_id\": \"25\",\n            \"customer_email\": \"<EMAIL>\",\n            \"ticket_reservation_id\": \"28\"\n        },\n        \"next_action\": null,\n        \"on_behalf_of\": null,\n        \"payment_method\": null,\n        \"payment_method_configuration_details\": null,\n        \"payment_method_options\": {\n            \"card\": {\n                \"installments\": null,\n                \"mandate_options\": null,\n                \"network\": null,\n                \"request_three_d_secure\": \"automatic\"\n            }\n        },\n        \"payment_method_types\": [\n            \"card\"\n        ],\n        \"processing\": null,\n        \"receipt_email\": null,\n        \"review\": null,\n        \"setup_future_usage\": null,\n        \"shipping\": null,\n        \"source\": null,\n        \"statement_descriptor\": null,\n        \"statement_descriptor_suffix\": null,\n        \"status\": \"requires_payment_method\",\n        \"transfer_data\": null,\n        \"transfer_group\": null\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.391602, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.240703, "end": **********.409545, "duration": 0.16884183883666992, "duration_str": "169ms", "measures": [{"label": "Booting", "start": **********.240703, "relative_start": 0, "end": **********.32535, "relative_end": **********.32535, "duration": 0.08464694023132324, "duration_str": "84.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.32536, "relative_start": 0.08465695381164551, "end": **********.409547, "relative_end": 2.1457672119140625e-06, "duration": 0.08418703079223633, "duration_str": "84.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6951648, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/checkout/webhook", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\StripeWebHookController@handleCheckoutWebhook", "namespace": null, "prefix": "api", "where": [], "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "as": "checkout.webhook", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/StripeWebHookController.php:20-57</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01166, "accumulated_duration_str": "11.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values ('25', 'evt_3Rg0LYRhkfMMoe7t15opCEC2', 'payment_intent.created', 'pi_3Rg0LYRhkfMMoe7t1wm4olLN', 'requires_payment_method', '{\\\"id\\\":\\\"evt_3Rg0LYRhkfMMoe7t15opCEC2\\\",\\\"object\\\":\\\"event\\\",\\\"api_version\\\":\\\"2025-03-31.basil\\\",\\\"created\\\":1751361564,\\\"data\\\":{\\\"object\\\":{\\\"id\\\":\\\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\\\",\\\"object\\\":\\\"payment_intent\\\",\\\"amount\\\":60510,\\\"amount_capturable\\\":0,\\\"amount_details\\\":{\\\"tip\\\":[]},\\\"amount_received\\\":0,\\\"application\\\":null,\\\"application_fee_amount\\\":null,\\\"automatic_payment_methods\\\":null,\\\"canceled_at\\\":null,\\\"cancellation_reason\\\":null,\\\"capture_method\\\":\\\"automatic_async\\\",\\\"client_secret\\\":\\\"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx\\\",\\\"confirmation_method\\\":\\\"automatic\\\",\\\"created\\\":1751361564,\\\"currency\\\":\\\"eur\\\",\\\"customer\\\":null,\\\"description\\\":null,\\\"last_payment_error\\\":null,\\\"latest_charge\\\":null,\\\"livemode\\\":false,\\\"metadata\\\":{\\\"user_id\\\":\\\"7\\\",\\\"order_id\\\":\\\"25\\\",\\\"customer_email\\\":\\\"<EMAIL>\\\",\\\"ticket_reservation_id\\\":\\\"28\\\"},\\\"next_action\\\":null,\\\"on_behalf_of\\\":null,\\\"payment_method\\\":null,\\\"payment_method_configuration_details\\\":null,\\\"payment_method_options\\\":{\\\"card\\\":{\\\"installments\\\":null,\\\"mandate_options\\\":null,\\\"network\\\":null,\\\"request_three_d_secure\\\":\\\"automatic\\\"}},\\\"payment_method_types\\\":[\\\"card\\\"],\\\"processing\\\":null,\\\"receipt_email\\\":null,\\\"review\\\":null,\\\"setup_future_usage\\\":null,\\\"shipping\\\":null,\\\"source\\\":null,\\\"statement_descriptor\\\":null,\\\"statement_descriptor_suffix\\\":null,\\\"status\\\":\\\"requires_payment_method\\\",\\\"transfer_data\\\":null,\\\"transfer_group\\\":null}},\\\"livemode\\\":false,\\\"pending_webhooks\\\":3,\\\"request\\\":{\\\"id\\\":\\\"req_C1Zt9ldj45wfql\\\",\\\"idempotency_key\\\":\\\"4290123c-26de-4117-ba07-7c4042a3daee\\\"},\\\"type\\\":\\\"payment_intent.created\\\"}', '2025-07-01 09:19:25', '2025-07-01 09:19:25')", "type": "query", "params": [], "bindings": ["25", "evt_3Rg0LYRhkfMMoe7t15opCEC2", "payment_intent.created", "pi_3Rg0LYRhkfMMoe7t1wm4olLN", "requires_payment_method", "{\"id\":\"evt_3Rg0LYRhkfMMoe7t15opCEC2\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751361564,\"data\":{\"object\":{\"id\":\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\"object\":\"payment_intent\",\"amount\":60510,\"amount_capturable\":0,\"amount_details\":{\"tip\":[]},\"amount_received\":0,\"application\":null,\"application_fee_amount\":null,\"automatic_payment_methods\":null,\"canceled_at\":null,\"cancellation_reason\":null,\"capture_method\":\"automatic_async\",\"client_secret\":\"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx\",\"confirmation_method\":\"automatic\",\"created\":1751361564,\"currency\":\"eur\",\"customer\":null,\"description\":null,\"last_payment_error\":null,\"latest_charge\":null,\"livemode\":false,\"metadata\":{\"user_id\":\"7\",\"order_id\":\"25\",\"customer_email\":\"<EMAIL>\",\"ticket_reservation_id\":\"28\"},\"next_action\":null,\"on_behalf_of\":null,\"payment_method\":null,\"payment_method_configuration_details\":null,\"payment_method_options\":{\"card\":{\"installments\":null,\"mandate_options\":null,\"network\":null,\"request_three_d_secure\":\"automatic\"}},\"payment_method_types\":[\"card\"],\"processing\":null,\"receipt_email\":null,\"review\":null,\"setup_future_usage\":null,\"shipping\":null,\"source\":null,\"statement_descriptor\":null,\"statement_descriptor_suffix\":null,\"status\":\"requires_payment_method\",\"transfer_data\":null,\"transfer_group\":null}},\"livemode\":false,\"pending_webhooks\":3,\"request\":{\"id\":\"req_C1Zt9ldj45wfql\",\"idempotency_key\":\"4290123c-26de-4117-ba07-7c4042a3daee\"},\"type\":\"payment_intent.created\"}", "2025-07-01 09:19:25", "2025-07-01 09:19:25"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.37577, "duration": 0.00855, "duration_str": "8.55ms", "memory": 0, "memory_str": null, "filename": "PaymentLogRepository.php:18", "source": {"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FPaymentLogRepository.php&line=18", "ajax": false, "filename": "PaymentLogRepository.php", "line": "18"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 73.328}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.389062, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:35", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=35", "ajax": false, "filename": "StripeWebHookController.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 73.328, "width_percent": 0}, {"sql": "select * from `order_transactions` where `order_id` = '25' limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 45}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3928, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "OrderTransactionRepository.php:26", "source": {"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderTransactionRepository.php&line=26", "ajax": false, "filename": "OrderTransactionRepository.php", "line": "26"}, "connection": "ticketgol", "explain": null, "start_percent": 73.328, "width_percent": 13.208}, {"sql": "update `order_transactions` set `payment_method_type` = 'card', `order_transactions`.`updated_at` = '2025-07-01 09:19:25' where `id` = 15", "type": "query", "params": [], "bindings": ["card", "2025-07-01 09:19:25", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.39682, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "StripeWebhookService.php:48", "source": {"index": 15, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStripeWebhookService.php&line=48", "ajax": false, "filename": "StripeWebhookService.php", "line": "48"}, "connection": "ticketgol", "explain": null, "start_percent": 86.535, "width_percent": 7.804}, {"sql": "update `order_transactions` set `currency_code` = 'eur', `total_amount` = 605.1, `order_transactions`.`updated_at` = '2025-07-01 09:19:25' where `id` = 15", "type": "query", "params": [], "bindings": ["eur", 605.1, "2025-07-01 09:19:25", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 66}, {"index": 16, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 53}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3994582, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StripeWebhookService.php:66", "source": {"index": 15, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStripeWebhookService.php&line=66", "ajax": false, "filename": "StripeWebhookService.php", "line": "66"}, "connection": "ticketgol", "explain": null, "start_percent": 94.34, "width_percent": 5.66}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.408168, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:49", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=49", "ajax": false, "filename": "StripeWebHookController.php", "line": "49"}, "connection": "ticketgol", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\OrderTransaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f491065-e0af-4042-a802-fd9ce61a50ce\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/checkout/webhook", "status_code": "<pre class=sf-dump id=sf-dump-1856423357 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1856423357\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-904479538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-904479538\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1021635856 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">evt_3Rg0LYRhkfMMoe7t15opCEC2</span>\"\n  \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"5 characters\">event</span>\"\n  \"<span class=sf-dump-key>api_version</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-03-31.basil</span>\"\n  \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361564</span>\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>object</span>\" => <span class=sf-dump-note>array:39</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pi_3Rg0LYRhkfMMoe7t1wm4olLN</span>\"\n      \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payment_intent</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>amount_capturable</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>amount_details</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>tip</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>amount_received</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>application</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>automatic_payment_methods</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>canceled_at</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>cancellation_reason</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>capture_method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">automatic_async</span>\"\n      \"<span class=sf-dump-key>client_secret</span>\" => \"<span class=sf-dump-str title=\"60 characters\">pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx</span>\"\n      \"<span class=sf-dump-key>confirmation_method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">automatic</span>\"\n      \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361564</span>\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n      \"<span class=sf-dump-key>customer</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>last_payment_error</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>latest_charge</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>metadata</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n        \"<span class=sf-dump-key>order_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n        \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>ticket_reservation_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>next_action</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>on_behalf_of</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payment_method</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payment_method_configuration_details</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payment_method_options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>installments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mandate_options</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>network</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>request_three_d_secure</span>\" => \"<span class=sf-dump-str title=\"9 characters\">automatic</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>payment_method_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">card</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>processing</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_email</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>review</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>setup_future_usage</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>source</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor_suffix</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"23 characters\">requires_payment_method</span>\"\n      \"<span class=sf-dump-key>transfer_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>transfer_group</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>pending_webhooks</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>request</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"18 characters\">req_C1Zt9ldj45wfql</span>\"\n    \"<span class=sf-dump-key>idempotency_key</span>\" => \"<span class=sf-dump-str title=\"36 characters\">4290123c-26de-4117-ba07-7c4042a3daee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"22 characters\">payment_intent.created</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021635856\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-958996893 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>stripe-signature</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">t=1751361564,v1=29a70d1bc62edca33e3a38a9b97530f22000ebca937950cc7da42fd4c743de7b,v0=123a1f3d53cc0d01a6354c274e2a80d93bbf0036d045b2fd48016e5d876df3ac</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">application/json; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">*/*; q=0.5, application/xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2014</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">Stripe/1.0 (+https://stripe.com/docs/webhooks)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958996893\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-493412082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-493412082\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-555825305 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:19:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555825305\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1570061529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1570061529\", {\"maxDepth\":0})</script>\n"}}