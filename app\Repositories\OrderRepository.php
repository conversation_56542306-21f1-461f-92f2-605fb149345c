<?php

namespace App\Repositories;

use App\DTO\OrderFilterDTO;
use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;

class OrderRepository extends BaseRepository
{
    public function __construct(Order $model)
    {
        $this->model = $model;
    }

    public function createOrderWithAttendees($checkoutDTO, $ticket)
    {
        $user = Auth::user();

        $order = $this->model->create([
            'buyer_id' => $user->id,
            'ticket_id' => $ticket->id,
            'quantity' => $checkoutDTO->quantity,
            'total_price' => $checkoutDTO->quantity * $ticket->price,
            'status' => OrderStatus::PENDING,
            'created_by' => $user->id,
            'purchase_date' => now(),
        ]);

        $order->attendees()->createMany($checkoutDTO->attendees);

        return $order;
    }

    public function createOrder($orderDTO)
    {
        $user = Auth::user();

        $order = $this->model->create([
            'buyer_id' => $user->id,
            'ticket_id' => $orderDTO->ticketId,
            'ticket_reservation_id' => $orderDTO->tempTicketReservationId,
            'quantity' => $orderDTO->quantity,
            'price' => $orderDTO->price,
            'total_price' => $orderDTO->totalPrice,
            'service_charge_amount' => $orderDTO->serviceChargeAmount,
            'tax_amount' => $orderDTO->taxAmount,
            'grand_total' => $orderDTO->grandTotal,
            'status' => OrderStatus::PENDING,
            'created_by' => $user->id,
            'purchase_date' => now(),
        ]);

        $order->attendees()->createMany($orderDTO->attendees);

        return $order;
    }

    public function cancelOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::CANCELED,
        ]);
    }

    public function expireOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::EXPIRED,
        ]);
    }

    public function confirmOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::CONFIRMED,
            'purchase_date' => now(),
        ]);
    }

    public function getOrdersForUser($userId, OrderFilterDTO $filtersDTO)
    {
        $query = $this->model->with([
            'ticket:id,event_id,ticket_no,ticket_type,sector_id',
            'ticket.event:id,date,time,home_club_id,guest_club_id,stadium_id',
            'ticket.event.translation:id,event_id,name',
            'ticket.event.stadium:id',
            'ticket.event.stadium.translation:id,stadium_id,name',
            'ticket.event.stadium.media',
            'ticket.event.homeClub:id',
            'ticket.event.homeClub.translation:club_id,name',
            'ticket.event.guestClub:id',
            'ticket.event.guestClub.translation:club_id,name',
            'ticket.sector:id',
            'ticket.sector.translation:stadium_sector_id,name',
        ])->where('buyer_id', $userId);

        if ($filtersDTO->search) {
            $query->where(function ($q) use ($filtersDTO) {
                $q->where('order_no', 'like', '%'.$filtersDTO->search.'%')
                    ->orWhereHas('ticket', function ($q) use ($filtersDTO) {
                        $q->where('ticket_no', 'like', '%'.$filtersDTO->search.'%');
                    });
            });
        }

        if ($filtersDTO->status) {
            $query->where('status', $filtersDTO->status);
        }

        if ($filtersDTO->dateFrom) {
            $query->whereDate('created_at', '>=', $filtersDTO->dateFrom);
        }

        if ($filtersDTO->dateTo) {
            $query->whereDate('created_at', '<=', $filtersDTO->dateTo);
        }

        return $query->orderBy('created_at', 'desc');
    }

    public function getOrderWithDetails($userId, $orderId)
    {
        $order = $this->model->with(
            [
                'buyer:id,name,email,user_name',
                'buyer.userDetail:id,user_id,address,phone',
                'transactions:id,order_id,currency_code,payment_method_type,total_amount,paid_at,card_brand,card_last_four',
                'attendees:id,order_id,name,email,gender,dob',
                'ticket.seller:id,name,user_name,email',
                'ticket.seller.userDetail:id,user_id,address,phone,company,description,city,country_id',
                'ticket.seller.userDetail.country.translation:id,name',
            ]
        )
            ->select(['id', 'order_no', 'buyer_id', 'ticket_id', 'quantity', 'total_price', 'status', 'purchase_date', 'description', 'order_meta_data'])
            ->where('buyer_id', $userId)
            ->where('id', $orderId)
            ->first();

        return $order;
    }
}
