{"__meta": {"id": "X6bbd7c2f4cbb3eb1d0a2bca4560578c8", "datetime": "2025-07-01 09:19:28", "utime": **********.355657, "method": "POST", "uri": "/api/checkout/webhook", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 5, "messages": [{"message": "[09:19:28] LOG.info: Payment intent succeeded {\n    \"payment_intent\": {\n        \"id\": \"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\n        \"object\": \"payment_intent\",\n        \"amount\": 60510,\n        \"amount_capturable\": 0,\n        \"amount_details\": {\n            \"tip\": []\n        },\n        \"amount_received\": 60510,\n        \"application\": null,\n        \"application_fee_amount\": null,\n        \"automatic_payment_methods\": null,\n        \"canceled_at\": null,\n        \"cancellation_reason\": null,\n        \"capture_method\": \"automatic_async\",\n        \"client_secret\": \"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx\",\n        \"confirmation_method\": \"automatic\",\n        \"created\": 1751361564,\n        \"currency\": \"eur\",\n        \"customer\": null,\n        \"description\": null,\n        \"last_payment_error\": null,\n        \"latest_charge\": \"ch_3Rg0LYRhkfMMoe7t1nEma9A4\",\n        \"livemode\": false,\n        \"metadata\": {\n            \"user_id\": \"7\",\n            \"order_id\": \"25\",\n            \"customer_email\": \"<EMAIL>\",\n            \"ticket_reservation_id\": \"28\"\n        },\n        \"next_action\": null,\n        \"on_behalf_of\": null,\n        \"payment_method\": \"pm_1Rg0LaRhkfMMoe7tDrKnoulg\",\n        \"payment_method_configuration_details\": null,\n        \"payment_method_options\": {\n            \"card\": {\n                \"installments\": null,\n                \"mandate_options\": null,\n                \"network\": null,\n                \"request_three_d_secure\": \"automatic\"\n            }\n        },\n        \"payment_method_types\": [\n            \"card\"\n        ],\n        \"processing\": null,\n        \"receipt_email\": null,\n        \"review\": null,\n        \"setup_future_usage\": null,\n        \"shipping\": null,\n        \"source\": null,\n        \"statement_descriptor\": null,\n        \"statement_descriptor_suffix\": null,\n        \"status\": \"succeeded\",\n        \"transfer_data\": null,\n        \"transfer_group\": null\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.227262, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:28] LOG.info: Order status updated to confirmed", "message_html": null, "is_string": false, "label": "info", "time": **********.245613, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:28] LOG.info: Transaction status updated to completed", "message_html": null, "is_string": false, "label": "info", "time": **********.2533, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:28] LOG.info: Reservation status updated to completed {\n    \"ticket_reservation\": {\n        \"id\": 28,\n        \"ticket_id\": 20,\n        \"user_id\": 7,\n        \"quantity\": \"1\",\n        \"price\": \"489.57\",\n        \"status\": \"completed\",\n        \"expires_at\": \"2025-07-01T09:31:00.000000Z\",\n        \"created_at\": \"2025-07-01T09:16:00.000000Z\",\n        \"updated_at\": \"2025-07-01T09:19:28.000000Z\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275896, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:28] LOG.info: reservedCounter is  {\n    \"coutner\": {}\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.281822, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.125891, "end": **********.355747, "duration": 0.22985601425170898, "duration_str": "230ms", "measures": [{"label": "Booting", "start": **********.125891, "relative_start": 0, "end": **********.183025, "relative_end": **********.183025, "duration": 0.05713391304016113, "duration_str": "57.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.183044, "relative_start": 0.05715298652648926, "end": **********.355752, "relative_end": 5.0067901611328125e-06, "duration": 0.17270803451538086, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7351808, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/checkout/webhook", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\StripeWebHookController@handleCheckoutWebhook", "namespace": null, "prefix": "api", "where": [], "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "as": "checkout.webhook", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/StripeWebHookController.php:20-57</a>"}, "queries": {"nb_statements": 17, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03547, "accumulated_duration_str": "35.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values ('25', 'evt_3Rg0LYRhkfMMoe7t1FPEJhQz', 'payment_intent.succeeded', 'pi_3Rg0LYRhkfMMoe7t1wm4olLN', 'succeeded', '{\\\"id\\\":\\\"evt_3Rg0LYRhkfMMoe7t1FPEJhQz\\\",\\\"object\\\":\\\"event\\\",\\\"api_version\\\":\\\"2025-03-31.basil\\\",\\\"created\\\":1751361566,\\\"data\\\":{\\\"object\\\":{\\\"id\\\":\\\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\\\",\\\"object\\\":\\\"payment_intent\\\",\\\"amount\\\":60510,\\\"amount_capturable\\\":0,\\\"amount_details\\\":{\\\"tip\\\":[]},\\\"amount_received\\\":60510,\\\"application\\\":null,\\\"application_fee_amount\\\":null,\\\"automatic_payment_methods\\\":null,\\\"canceled_at\\\":null,\\\"cancellation_reason\\\":null,\\\"capture_method\\\":\\\"automatic_async\\\",\\\"client_secret\\\":\\\"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx\\\",\\\"confirmation_method\\\":\\\"automatic\\\",\\\"created\\\":1751361564,\\\"currency\\\":\\\"eur\\\",\\\"customer\\\":null,\\\"description\\\":null,\\\"last_payment_error\\\":null,\\\"latest_charge\\\":\\\"ch_3Rg0LYRhkfMMoe7t1nEma9A4\\\",\\\"livemode\\\":false,\\\"metadata\\\":{\\\"user_id\\\":\\\"7\\\",\\\"order_id\\\":\\\"25\\\",\\\"customer_email\\\":\\\"<EMAIL>\\\",\\\"ticket_reservation_id\\\":\\\"28\\\"},\\\"next_action\\\":null,\\\"on_behalf_of\\\":null,\\\"payment_method\\\":\\\"pm_1Rg0LaRhkfMMoe7tDrKnoulg\\\",\\\"payment_method_configuration_details\\\":null,\\\"payment_method_options\\\":{\\\"card\\\":{\\\"installments\\\":null,\\\"mandate_options\\\":null,\\\"network\\\":null,\\\"request_three_d_secure\\\":\\\"automatic\\\"}},\\\"payment_method_types\\\":[\\\"card\\\"],\\\"processing\\\":null,\\\"receipt_email\\\":null,\\\"review\\\":null,\\\"setup_future_usage\\\":null,\\\"shipping\\\":null,\\\"source\\\":null,\\\"statement_descriptor\\\":null,\\\"statement_descriptor_suffix\\\":null,\\\"status\\\":\\\"succeeded\\\",\\\"transfer_data\\\":null,\\\"transfer_group\\\":null}},\\\"livemode\\\":false,\\\"pending_webhooks\\\":2,\\\"request\\\":{\\\"id\\\":\\\"req_5E0EQySila4yOc\\\",\\\"idempotency_key\\\":\\\"9affd8cd-0f1a-4eb2-8065-4a2ef68655cb\\\"},\\\"type\\\":\\\"payment_intent.succeeded\\\"}', '2025-07-01 09:19:28', '2025-07-01 09:19:28')", "type": "query", "params": [], "bindings": ["25", "evt_3Rg0LYRhkfMMoe7t1FPEJhQz", "payment_intent.succeeded", "pi_3Rg0LYRhkfMMoe7t1wm4olLN", "succeeded", "{\"id\":\"evt_3Rg0LYRhkfMMoe7t1FPEJhQz\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751361566,\"data\":{\"object\":{\"id\":\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\"object\":\"payment_intent\",\"amount\":60510,\"amount_capturable\":0,\"amount_details\":{\"tip\":[]},\"amount_received\":60510,\"application\":null,\"application_fee_amount\":null,\"automatic_payment_methods\":null,\"canceled_at\":null,\"cancellation_reason\":null,\"capture_method\":\"automatic_async\",\"client_secret\":\"pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx\",\"confirmation_method\":\"automatic\",\"created\":1751361564,\"currency\":\"eur\",\"customer\":null,\"description\":null,\"last_payment_error\":null,\"latest_charge\":\"ch_3Rg0LYRhkfMMoe7t1nEma9A4\",\"livemode\":false,\"metadata\":{\"user_id\":\"7\",\"order_id\":\"25\",\"customer_email\":\"<EMAIL>\",\"ticket_reservation_id\":\"28\"},\"next_action\":null,\"on_behalf_of\":null,\"payment_method\":\"pm_1Rg0LaRhkfMMoe7tDrKnoulg\",\"payment_method_configuration_details\":null,\"payment_method_options\":{\"card\":{\"installments\":null,\"mandate_options\":null,\"network\":null,\"request_three_d_secure\":\"automatic\"}},\"payment_method_types\":[\"card\"],\"processing\":null,\"receipt_email\":null,\"review\":null,\"setup_future_usage\":null,\"shipping\":null,\"source\":null,\"statement_descriptor\":null,\"statement_descriptor_suffix\":null,\"status\":\"succeeded\",\"transfer_data\":null,\"transfer_group\":null}},\"livemode\":false,\"pending_webhooks\":2,\"request\":{\"id\":\"req_5E0EQySila4yOc\",\"idempotency_key\":\"9affd8cd-0f1a-4eb2-8065-4a2ef68655cb\"},\"type\":\"payment_intent.succeeded\"}", "2025-07-01 09:19:28", "2025-07-01 09:19:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.211731, "duration": 0.00879, "duration_str": "8.79ms", "memory": 0, "memory_str": null, "filename": "PaymentLogRepository.php:18", "source": {"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FPaymentLogRepository.php&line=18", "ajax": false, "filename": "PaymentLogRepository.php", "line": "18"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 24.782}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.225318, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:35", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=35", "ajax": false, "filename": "StripeWebHookController.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 24.782, "width_percent": 0}, {"sql": "select * from `order_transactions` where `payment_intent_id` = 'pi_3Rg0LYRhkfMMoe7t1wm4olLN' limit 1", "type": "query", "params": [], "bindings": ["pi_3Rg0LYRhkfMMoe7t1wm4olLN"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 36}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 98}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.228178, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "OrderTransactionRepository.php:36", "source": {"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderTransactionRepository.php&line=36", "ajax": false, "filename": "OrderTransactionRepository.php", "line": "36"}, "connection": "ticketgol", "explain": null, "start_percent": 24.782, "width_percent": 3.017}, {"sql": "select * from `orders` where `orders`.`id` = 25 and `orders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 107}, {"index": 22, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.231513, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "StripeWebhookService.php:107", "source": {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStripeWebhookService.php&line=107", "ajax": false, "filename": "StripeWebhookService.php", "line": "107"}, "connection": "ticketgol", "explain": null, "start_percent": 27.798, "width_percent": 2.34}, {"sql": "select * from `orders` where `orders`.`id` = 25 and `orders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 123}, {"index": 20, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 76}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}], "start": **********.23471, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 30.138, "width_percent": 1.945}, {"sql": "update `orders` set `status` = 'confirmed', `purchase_date` = '2025-07-01 09:19:28', `orders`.`updated_at` = '2025-07-01 09:19:28' where `id` = 25", "type": "query", "params": [], "bindings": ["confirmed", "2025-07-01 09:19:28", "2025-07-01 09:19:28", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 76}, {"index": 17, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 109}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}], "start": **********.237752, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:125", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=125", "ajax": false, "filename": "BaseRepository.php", "line": "125"}, "connection": "ticketgol", "explain": null, "start_percent": 32.083, "width_percent": 2.566}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `batch_uuid`, `event`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('Resource', '{\\\"status\\\":\\\"confirmed\\\",\\\"purchase_date\\\":\\\"2025-07-01T09:19:28.233979Z\\\",\\\"updated_at\\\":\\\"2025-07-01 09:19:28\\\"}', null, 'Updated', 25, 'App\\\\Models\\\\Order', 'Order Updated', '2025-07-01 09:19:28', '2025-07-01 09:19:28')", "type": "query", "params": [], "bindings": ["Resource", "{\"status\":\"confirmed\",\"purchase_date\":\"2025-07-01T09:19:28.233979Z\",\"updated_at\":\"2025-07-01 09:19:28\"}", null, "Updated", 25, "App\\Models\\Order", "Order Updated", "2025-07-01 09:19:28", "2025-07-01 09:19:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 91}, {"index": 25, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 76}], "start": **********.2423089, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 34.649, "width_percent": 4.624}, {"sql": "select * from `order_transactions` where `order_transactions`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 123}, {"index": 20, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 55}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 112}, {"index": 22, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}], "start": **********.246257, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 39.273, "width_percent": 2.002}, {"sql": "update `order_transactions` set `status` = 'completed', `paid_at` = '2025-07-01 09:19:28', `order_transactions`.`updated_at` = '2025-07-01 09:19:28' where `id` = 15", "type": "query", "params": [], "bindings": ["completed", "2025-07-01 09:19:28", "2025-07-01 09:19:28", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, {"index": 16, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 55}, {"index": 17, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 112}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}], "start": **********.248722, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:125", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=125", "ajax": false, "filename": "BaseRepository.php", "line": "125"}, "connection": "ticketgol", "explain": null, "start_percent": 41.274, "width_percent": 8.43}, {"sql": "select * from `tickets` where `tickets`.`id` = 20 and `tickets`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 245}, {"index": 20, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 116}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}], "start": **********.2543728, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 49.704, "width_percent": 2.143}, {"sql": "update `tickets` set `quantity` = `quantity` - 1, `tickets`.`updated_at` = '2025-07-01 09:19:28' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-07-01 09:19:28", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 247}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 116}, {"index": 19, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.2582161, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "TicketRepository.php:247", "source": {"index": 17, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 247}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FTicketRepository.php&line=247", "ajax": false, "filename": "TicketRepository.php", "line": "247"}, "connection": "ticketgol", "explain": null, "start_percent": 51.847, "width_percent": 2.876}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `batch_uuid`, `event`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('Resource', '{\\\"quantity\\\":6}', null, 'Updated', 20, 'App\\\\Models\\\\Ticket', 'Ticket Updated', '2025-07-01 09:19:28', '2025-07-01 09:19:28')", "type": "query", "params": [], "bindings": ["Resource", "{\"quantity\":6}", null, "Updated", 20, "App\\Models\\Ticket", "Ticket Updated", "2025-07-01 09:19:28", "2025-07-01 09:19:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 91}, {"index": 27, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 247}, {"index": 28, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 116}], "start": **********.26139, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 54.722, "width_percent": 2.312}, {"sql": "update `tickets` set `sold_quantity` = `sold_quantity` + 1, `tickets`.`updated_at` = '2025-07-01 09:19:28' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-07-01 09:19:28", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 248}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 116}, {"index": 19, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.264221, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "TicketRepository.php:248", "source": {"index": 17, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 248}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FTicketRepository.php&line=248", "ajax": false, "filename": "TicketRepository.php", "line": "248"}, "connection": "ticketgol", "explain": null, "start_percent": 57.034, "width_percent": 3.524}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `batch_uuid`, `event`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('Resource', '{\\\"sold_quantity\\\":1}', null, 'Updated', 20, 'App\\\\Models\\\\Ticket', 'Ticket Updated', '2025-07-01 09:19:28', '2025-07-01 09:19:28')", "type": "query", "params": [], "bindings": ["Resource", "{\"sold_quantity\":1}", null, "Updated", 20, "App\\Models\\Ticket", "Ticket Updated", "2025-07-01 09:19:28", "2025-07-01 09:19:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 91}, {"index": 27, "namespace": null, "name": "app/Repositories/TicketRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketRepository.php", "line": 248}, {"index": 28, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 116}], "start": **********.267349, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 60.558, "width_percent": 1.917}, {"sql": "select * from `ticket_reservations` where `ticket_reservations`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 135}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 126}, {"index": 22, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.269651, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TicketReservationRepository.php:135", "source": {"index": 20, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FTicketReservationRepository.php&line=135", "ajax": false, "filename": "TicketReservationRepository.php", "line": "135"}, "connection": "ticketgol", "explain": null, "start_percent": 62.475, "width_percent": 1.438}, {"sql": "update `ticket_reservations` set `status` = 'completed', `ticket_reservations`.`updated_at` = '2025-07-01 09:19:28' where `id` = 28", "type": "query", "params": [], "bindings": ["completed", "2025-07-01 09:19:28", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 137}, {"index": 16, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 126}, {"index": 17, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.272105, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "TicketReservationRepository.php:137", "source": {"index": 15, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FTicketReservationRepository.php&line=137", "ajax": false, "filename": "TicketReservationRepository.php", "line": "137"}, "connection": "ticketgol", "explain": null, "start_percent": 63.913, "width_percent": 4.06}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"0a5656c7-9acf-44fb-a534-11b78fdcb617\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\SendOrderConfirmationEmailJob\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\SendOrderConfirmationEmailJob\\\",\\\"command\\\":\\\"O:38:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\SendOrderConfirmationEmailJob\\\\\\\":1:{s:10:\\\\\\\"\\\\u0000*\\\\u0000orderId\\\\\\\";i:25;}\\\"},\\\"telescope_uuid\\\":\\\"9f49106a-521f-4924-a7b2-86d749d39452\\\",\\\"sentry_baggage_data\\\":\\\"sentry-trace_id=52eec7d573fa4aa59f3c019277849724,sentry-sample_rate=1,sentry-transaction=%2Fapi%2Fcheckout%2Fwebhook,sentry-public_key=a15e9d05dc1534625d8ed86a4c6054c4,sentry-environment=local,sentry-sampled=true,sentry-sample_rand=0.746658\\\",\\\"sentry_trace_parent_data\\\":\\\"52eec7d573fa4aa59f3c019277849724-e9361168295e4e67-1\\\",\\\"sentry_publish_time\\\":**********.3238}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"0a5656c7-9acf-44fb-a534-11b78fdcb617\",\"displayName\":\"App\\\\Jobs\\\\SendOrderConfirmationEmailJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQ<PERSON><PERSON><PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendOrderConfirmationEmailJob\",\"command\":\"O:38:\\\"App\\\\Jobs\\\\SendOrderConfirmationEmailJob\\\":1:{s:10:\\\"\\u0000*\\u0000orderId\\\";i:25;}\"},\"telescope_uuid\":\"9f49106a-521f-4924-a7b2-86d749d39452\",\"sentry_baggage_data\":\"sentry-trace_id=52eec7d573fa4aa59f3c019277849724,sentry-sample_rate=1,sentry-transaction=%2Fapi%2Fcheckout%2Fwebhook,sentry-public_key=a15e9d05dc1534625d8ed86a4c6054c4,sentry-environment=local,sentry-sampled=true,sentry-sample_rand=0.746658\",\"sentry_trace_parent_data\":\"52eec7d573fa4aa59f3c019277849724-e9361168295e4e67-1\",\"sentry_publish_time\":**********.3238}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 99}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 338}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 244}], "start": **********.325939, "duration": 0.00984, "duration_str": "9.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:188", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=188", "ajax": false, "filename": "DatabaseQueue.php", "line": "188"}, "connection": "ticketgol", "explain": null, "start_percent": 67.973, "width_percent": 27.742}, {"sql": "select * from `order_transactions` where `order_id` = '25' limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 26}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 131}, {"index": 19, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 100}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.33972, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "OrderTransactionRepository.php:26", "source": {"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderTransactionRepository.php&line=26", "ajax": false, "filename": "OrderTransactionRepository.php", "line": "26"}, "connection": "ticketgol", "explain": null, "start_percent": 95.715, "width_percent": 4.285}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.353341, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:49", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=49", "ajax": false, "filename": "StripeWebHookController.php", "line": "49"}, "connection": "ticketgol", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\OrderTransaction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}, "App\\Models\\Order": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\TicketReservation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicketReservation.php&line=1", "ajax": false, "filename": "TicketReservation.php", "line": "?"}}}, "count": 7, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f49106a-60a0-4dc2-90a6-62906fcb22bc\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/checkout/webhook", "status_code": "<pre class=sf-dump id=sf-dump-710781735 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-710781735\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1412503204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1412503204\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1864937382 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">evt_3Rg0LYRhkfMMoe7t1FPEJhQz</span>\"\n  \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"5 characters\">event</span>\"\n  \"<span class=sf-dump-key>api_version</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-03-31.basil</span>\"\n  \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361566</span>\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>object</span>\" => <span class=sf-dump-note>array:39</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pi_3Rg0LYRhkfMMoe7t1wm4olLN</span>\"\n      \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payment_intent</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>amount_capturable</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>amount_details</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>tip</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>amount_received</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>application</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>automatic_payment_methods</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>canceled_at</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>cancellation_reason</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>capture_method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">automatic_async</span>\"\n      \"<span class=sf-dump-key>client_secret</span>\" => \"<span class=sf-dump-str title=\"60 characters\">pi_3Rg0LYRhkfMMoe7t1wm4olLN_secret_2Hc4hUj8pXdAtAqQ08MezgTdx</span>\"\n      \"<span class=sf-dump-key>confirmation_method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">automatic</span>\"\n      \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361564</span>\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n      \"<span class=sf-dump-key>customer</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>last_payment_error</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>latest_charge</span>\" => \"<span class=sf-dump-str title=\"27 characters\">ch_3Rg0LYRhkfMMoe7t1nEma9A4</span>\"\n      \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>metadata</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n        \"<span class=sf-dump-key>order_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n        \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>ticket_reservation_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>next_action</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>on_behalf_of</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pm_1Rg0LaRhkfMMoe7tDrKnoulg</span>\"\n      \"<span class=sf-dump-key>payment_method_configuration_details</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>payment_method_options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>installments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mandate_options</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>network</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>request_three_d_secure</span>\" => \"<span class=sf-dump-str title=\"9 characters\">automatic</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>payment_method_types</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">card</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>processing</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_email</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>review</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>setup_future_usage</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>source</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor_suffix</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">succeeded</span>\"\n      \"<span class=sf-dump-key>transfer_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>transfer_group</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>pending_webhooks</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>request</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"18 characters\">req_5E0EQySila4yOc</span>\"\n    \"<span class=sf-dump-key>idempotency_key</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9affd8cd-0f1a-4eb2-8065-4a2ef68655cb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"24 characters\">payment_intent.succeeded</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864937382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2064707791 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>stripe-signature</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">t=1751361567,v1=620705d62a0771996130c699c68b3bec42858c9bed9a5aff8a3f3ab42522d01d,v0=8a21e459bfdcd9eda08d01f6a2647b3050ebc09cfb1f19ce95a3a20055aa5a39</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">application/json; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">*/*; q=0.5, application/xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2056</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">Stripe/1.0 (+https://stripe.com/docs/webhooks)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064707791\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-837422086 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-837422086\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1754612000 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:19:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754612000\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2138417505 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2138417505\", {\"maxDepth\":0})</script>\n"}}