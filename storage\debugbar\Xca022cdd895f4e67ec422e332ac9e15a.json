{"__meta": {"id": "Xca022cdd895f4e67ec422e332ac9e15a", "datetime": "2025-07-01 09:19:29", "utime": **********.125505, "method": "POST", "uri": "/api/checkout/webhook", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[09:19:28] LOG.info: Charge succeeded {\n    \"charge\": {\n        \"id\": \"ch_3Rg0LYRhkfMMoe7t1nEma9A4\",\n        \"object\": \"charge\",\n        \"amount\": 60510,\n        \"amount_captured\": 60510,\n        \"amount_refunded\": 0,\n        \"application\": null,\n        \"application_fee\": null,\n        \"application_fee_amount\": null,\n        \"balance_transaction\": null,\n        \"billing_details\": {\n            \"address\": {\n                \"city\": null,\n                \"country\": null,\n                \"line1\": null,\n                \"line2\": null,\n                \"postal_code\": null,\n                \"state\": null\n            },\n            \"email\": null,\n            \"name\": null,\n            \"phone\": null,\n            \"tax_id\": null\n        },\n        \"calculated_statement_descriptor\": \"Stripe\",\n        \"captured\": true,\n        \"created\": 1751361566,\n        \"currency\": \"eur\",\n        \"customer\": null,\n        \"description\": null,\n        \"destination\": null,\n        \"dispute\": null,\n        \"disputed\": false,\n        \"failure_balance_transaction\": null,\n        \"failure_code\": null,\n        \"failure_message\": null,\n        \"fraud_details\": [],\n        \"livemode\": false,\n        \"metadata\": {\n            \"user_id\": \"7\",\n            \"order_id\": \"25\",\n            \"customer_email\": \"<EMAIL>\",\n            \"ticket_reservation_id\": \"28\"\n        },\n        \"on_behalf_of\": null,\n        \"order\": null,\n        \"outcome\": {\n            \"advice_code\": null,\n            \"network_advice_code\": null,\n            \"network_decline_code\": null,\n            \"network_status\": \"approved_by_network\",\n            \"reason\": null,\n            \"risk_level\": \"normal\",\n            \"risk_score\": 62,\n            \"seller_message\": \"Payment complete.\",\n            \"type\": \"authorized\"\n        },\n        \"paid\": true,\n        \"payment_intent\": \"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\n        \"payment_method\": \"pm_1Rg0LaRhkfMMoe7tDrKnoulg\",\n        \"payment_method_details\": {\n            \"card\": {\n                \"amount_authorized\": 60510,\n                \"authorization_code\": \"526200\",\n                \"brand\": \"visa\",\n                \"checks\": {\n                    \"address_line1_check\": null,\n                    \"address_postal_code_check\": null,\n                    \"cvc_check\": \"pass\"\n                },\n                \"country\": \"US\",\n                \"exp_month\": 4,\n                \"exp_year\": 2029,\n                \"extended_authorization\": {\n                    \"status\": \"disabled\"\n                },\n                \"fingerprint\": \"4qHsohpy0cF1sMdU\",\n                \"funding\": \"credit\",\n                \"incremental_authorization\": {\n                    \"status\": \"unavailable\"\n                },\n                \"installments\": null,\n                \"last4\": \"4242\",\n                \"mandate\": null,\n                \"multicapture\": {\n                    \"status\": \"unavailable\"\n                },\n                \"network\": \"visa\",\n                \"network_token\": {\n                    \"used\": false\n                },\n                \"network_transaction_id\": \"521137211511110\",\n                \"overcapture\": {\n                    \"maximum_amount_capturable\": 60510,\n                    \"status\": \"unavailable\"\n                },\n                \"regulated_status\": \"unregulated\",\n                \"three_d_secure\": null,\n                \"wallet\": null\n            },\n            \"type\": \"card\"\n        },\n        \"radar_options\": [],\n        \"receipt_email\": null,\n        \"receipt_number\": null,\n        \"receipt_url\": \"https:\\/\\/pay.stripe.com\\/receipts\\/payment\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJ_QjsMGMgYfH1orBJ86LBYlv7MroKVZAJVmfO0ldXg3fI4O7OvOvTWW932avX_nbo154-WA17fozLg-\",\n        \"refunded\": false,\n        \"review\": null,\n        \"shipping\": null,\n        \"source\": null,\n        \"source_transfer\": null,\n        \"statement_descriptor\": null,\n        \"statement_descriptor_suffix\": null,\n        \"status\": \"succeeded\",\n        \"transfer_data\": null,\n        \"transfer_group\": null\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.441961, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.334683, "end": **********.125525, "duration": 0.7908420562744141, "duration_str": "791ms", "measures": [{"label": "Booting", "start": **********.334683, "relative_start": 0, "end": **********.426907, "relative_end": **********.426907, "duration": 0.09222412109375, "duration_str": "92.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.426917, "relative_start": 0.09223413467407227, "end": **********.125528, "relative_end": 3.0994415283203125e-06, "duration": 0.6986110210418701, "duration_str": "699ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7085264, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/checkout/webhook", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\V1\\StripeWebHookController@handleCheckoutWebhook", "namespace": null, "prefix": "api", "where": [], "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "as": "checkout.webhook", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=20\" onclick=\"\">app/Http/Controllers/Api/V1/StripeWebHookController.php:20-57</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01064, "accumulated_duration_str": "10.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "insert into `payment_logs` (`order_id`, `stripe_event_id`, `event_type`, `event_data_id`, `status`, `webhook_data`, `updated_at`, `created_at`) values ('25', 'evt_3Rg0LYRhkfMMoe7t1G5X4hsa', 'charge.succeeded', 'ch_3Rg0LYRhkfMMoe7t1nEma9A4', 'succeeded', '{\\\"id\\\":\\\"evt_3Rg0LYRhkfMMoe7t1G5X4hsa\\\",\\\"object\\\":\\\"event\\\",\\\"api_version\\\":\\\"2025-03-31.basil\\\",\\\"created\\\":1751361566,\\\"data\\\":{\\\"object\\\":{\\\"id\\\":\\\"ch_3Rg0LYRhkfMMoe7t1nEma9A4\\\",\\\"object\\\":\\\"charge\\\",\\\"amount\\\":60510,\\\"amount_captured\\\":60510,\\\"amount_refunded\\\":0,\\\"application\\\":null,\\\"application_fee\\\":null,\\\"application_fee_amount\\\":null,\\\"balance_transaction\\\":null,\\\"billing_details\\\":{\\\"address\\\":{\\\"city\\\":null,\\\"country\\\":null,\\\"line1\\\":null,\\\"line2\\\":null,\\\"postal_code\\\":null,\\\"state\\\":null},\\\"email\\\":null,\\\"name\\\":null,\\\"phone\\\":null,\\\"tax_id\\\":null},\\\"calculated_statement_descriptor\\\":\\\"Stripe\\\",\\\"captured\\\":true,\\\"created\\\":1751361566,\\\"currency\\\":\\\"eur\\\",\\\"customer\\\":null,\\\"description\\\":null,\\\"destination\\\":null,\\\"dispute\\\":null,\\\"disputed\\\":false,\\\"failure_balance_transaction\\\":null,\\\"failure_code\\\":null,\\\"failure_message\\\":null,\\\"fraud_details\\\":[],\\\"livemode\\\":false,\\\"metadata\\\":{\\\"user_id\\\":\\\"7\\\",\\\"order_id\\\":\\\"25\\\",\\\"customer_email\\\":\\\"<EMAIL>\\\",\\\"ticket_reservation_id\\\":\\\"28\\\"},\\\"on_behalf_of\\\":null,\\\"order\\\":null,\\\"outcome\\\":{\\\"advice_code\\\":null,\\\"network_advice_code\\\":null,\\\"network_decline_code\\\":null,\\\"network_status\\\":\\\"approved_by_network\\\",\\\"reason\\\":null,\\\"risk_level\\\":\\\"normal\\\",\\\"risk_score\\\":62,\\\"seller_message\\\":\\\"Payment complete.\\\",\\\"type\\\":\\\"authorized\\\"},\\\"paid\\\":true,\\\"payment_intent\\\":\\\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\\\",\\\"payment_method\\\":\\\"pm_1Rg0LaRhkfMMoe7tDrKnoulg\\\",\\\"payment_method_details\\\":{\\\"card\\\":{\\\"amount_authorized\\\":60510,\\\"authorization_code\\\":\\\"526200\\\",\\\"brand\\\":\\\"visa\\\",\\\"checks\\\":{\\\"address_line1_check\\\":null,\\\"address_postal_code_check\\\":null,\\\"cvc_check\\\":\\\"pass\\\"},\\\"country\\\":\\\"US\\\",\\\"exp_month\\\":4,\\\"exp_year\\\":2029,\\\"extended_authorization\\\":{\\\"status\\\":\\\"disabled\\\"},\\\"fingerprint\\\":\\\"4qHsohpy0cF1sMdU\\\",\\\"funding\\\":\\\"credit\\\",\\\"incremental_authorization\\\":{\\\"status\\\":\\\"unavailable\\\"},\\\"installments\\\":null,\\\"last4\\\":\\\"4242\\\",\\\"mandate\\\":null,\\\"multicapture\\\":{\\\"status\\\":\\\"unavailable\\\"},\\\"network\\\":\\\"visa\\\",\\\"network_token\\\":{\\\"used\\\":false},\\\"network_transaction_id\\\":\\\"521137211511110\\\",\\\"overcapture\\\":{\\\"maximum_amount_capturable\\\":60510,\\\"status\\\":\\\"unavailable\\\"},\\\"regulated_status\\\":\\\"unregulated\\\",\\\"three_d_secure\\\":null,\\\"wallet\\\":null},\\\"type\\\":\\\"card\\\"},\\\"radar_options\\\":[],\\\"receipt_email\\\":null,\\\"receipt_number\\\":null,\\\"receipt_url\\\":\\\"https:\\\\/\\\\/pay.stripe.com\\\\/receipts\\\\/payment\\\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJ_QjsMGMgYfH1orBJ86LBYlv7MroKVZAJVmfO0ldXg3fI4O7OvOvTWW932avX_nbo154-WA17fozLg-\\\",\\\"refunded\\\":false,\\\"review\\\":null,\\\"shipping\\\":null,\\\"source\\\":null,\\\"source_transfer\\\":null,\\\"statement_descriptor\\\":null,\\\"statement_descriptor_suffix\\\":null,\\\"status\\\":\\\"succeeded\\\",\\\"transfer_data\\\":null,\\\"transfer_group\\\":null}},\\\"livemode\\\":false,\\\"pending_webhooks\\\":3,\\\"request\\\":{\\\"id\\\":\\\"req_5E0EQySila4yOc\\\",\\\"idempotency_key\\\":\\\"9affd8cd-0f1a-4eb2-8065-4a2ef68655cb\\\"},\\\"type\\\":\\\"charge.succeeded\\\"}', '2025-07-01 09:19:28', '2025-07-01 09:19:28')", "type": "query", "params": [], "bindings": ["25", "evt_3Rg0LYRhkfMMoe7t1G5X4hsa", "charge.succeeded", "ch_3Rg0LYRhkfMMoe7t1nEma9A4", "succeeded", "{\"id\":\"evt_3Rg0LYRhkfMMoe7t1G5X4hsa\",\"object\":\"event\",\"api_version\":\"2025-03-31.basil\",\"created\":1751361566,\"data\":{\"object\":{\"id\":\"ch_3Rg0LYRhkfMMoe7t1nEma9A4\",\"object\":\"charge\",\"amount\":60510,\"amount_captured\":60510,\"amount_refunded\":0,\"application\":null,\"application_fee\":null,\"application_fee_amount\":null,\"balance_transaction\":null,\"billing_details\":{\"address\":{\"city\":null,\"country\":null,\"line1\":null,\"line2\":null,\"postal_code\":null,\"state\":null},\"email\":null,\"name\":null,\"phone\":null,\"tax_id\":null},\"calculated_statement_descriptor\":\"Stripe\",\"captured\":true,\"created\":1751361566,\"currency\":\"eur\",\"customer\":null,\"description\":null,\"destination\":null,\"dispute\":null,\"disputed\":false,\"failure_balance_transaction\":null,\"failure_code\":null,\"failure_message\":null,\"fraud_details\":[],\"livemode\":false,\"metadata\":{\"user_id\":\"7\",\"order_id\":\"25\",\"customer_email\":\"<EMAIL>\",\"ticket_reservation_id\":\"28\"},\"on_behalf_of\":null,\"order\":null,\"outcome\":{\"advice_code\":null,\"network_advice_code\":null,\"network_decline_code\":null,\"network_status\":\"approved_by_network\",\"reason\":null,\"risk_level\":\"normal\",\"risk_score\":62,\"seller_message\":\"Payment complete.\",\"type\":\"authorized\"},\"paid\":true,\"payment_intent\":\"pi_3Rg0LYRhkfMMoe7t1wm4olLN\",\"payment_method\":\"pm_1Rg0LaRhkfMMoe7tDrKnoulg\",\"payment_method_details\":{\"card\":{\"amount_authorized\":60510,\"authorization_code\":\"526200\",\"brand\":\"visa\",\"checks\":{\"address_line1_check\":null,\"address_postal_code_check\":null,\"cvc_check\":\"pass\"},\"country\":\"US\",\"exp_month\":4,\"exp_year\":2029,\"extended_authorization\":{\"status\":\"disabled\"},\"fingerprint\":\"4qHsohpy0cF1sMdU\",\"funding\":\"credit\",\"incremental_authorization\":{\"status\":\"unavailable\"},\"installments\":null,\"last4\":\"4242\",\"mandate\":null,\"multicapture\":{\"status\":\"unavailable\"},\"network\":\"visa\",\"network_token\":{\"used\":false},\"network_transaction_id\":\"521137211511110\",\"overcapture\":{\"maximum_amount_capturable\":60510,\"status\":\"unavailable\"},\"regulated_status\":\"unregulated\",\"three_d_secure\":null,\"wallet\":null},\"type\":\"card\"},\"radar_options\":[],\"receipt_email\":null,\"receipt_number\":null,\"receipt_url\":\"https:\\/\\/pay.stripe.com\\/receipts\\/payment\\/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJ_QjsMGMgYfH1orBJ86LBYlv7MroKVZAJVmfO0ldXg3fI4O7OvOvTWW932avX_nbo154-WA17fozLg-\",\"refunded\":false,\"review\":null,\"shipping\":null,\"source\":null,\"source_transfer\":null,\"statement_descriptor\":null,\"statement_descriptor_suffix\":null,\"status\":\"succeeded\",\"transfer_data\":null,\"transfer_group\":null}},\"livemode\":false,\"pending_webhooks\":3,\"request\":{\"id\":\"req_5E0EQySila4yOc\",\"idempotency_key\":\"9affd8cd-0f1a-4eb2-8065-4a2ef68655cb\"},\"type\":\"charge.succeeded\"}", "2025-07-01 09:19:28", "2025-07-01 09:19:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, {"index": 21, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4308949, "duration": 0.00762, "duration_str": "7.62ms", "memory": 0, "memory_str": null, "filename": "PaymentLogRepository.php:18", "source": {"index": 20, "namespace": null, "name": "app/Repositories/PaymentLogRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\PaymentLogRepository.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FPaymentLogRepository.php&line=18", "ajax": false, "filename": "PaymentLogRepository.php", "line": "18"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 71.617}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.440902, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:35", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=35", "ajax": false, "filename": "StripeWebHookController.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 71.617, "width_percent": 0}, {"sql": "select * from `order_transactions` where `payment_intent_id` = 'pi_3Rg0LYRhkfMMoe7t1wm4olLN' limit 1", "type": "query", "params": [], "bindings": ["pi_3Rg0LYRhkfMMoe7t1wm4olLN"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 36}, {"index": 18, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.442752, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "OrderTransactionRepository.php:36", "source": {"index": 17, "namespace": null, "name": "app/Repositories/OrderTransactionRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderTransactionRepository.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderTransactionRepository.php&line=36", "ajax": false, "filename": "OrderTransactionRepository.php", "line": "36"}, "connection": "ticketgol", "explain": null, "start_percent": 71.617, "width_percent": 10.056}, {"sql": "select * from `order_transactions` where `order_transactions`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 123}, {"index": 20, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.4452221, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 81.673, "width_percent": 4.511}, {"sql": "update `order_transactions` set `payment_method_id` = 'pm_1Rg0LaRhkfMMoe7tDrKnoulg', `card_brand` = 'visa', `card_last_four` = '4242', `order_transactions`.`updated_at` = '2025-07-01 09:19:28' where `id` = 15", "type": "query", "params": [], "bindings": ["pm_1Rg0LaRhkfMMoe7tDrKnoulg", "visa", "4242", "2025-07-01 09:19:28", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, {"index": 16, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 79}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4472659, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:125", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=125", "ajax": false, "filename": "BaseRepository.php", "line": "125"}, "connection": "ticketgol", "explain": null, "start_percent": 86.184, "width_percent": 5.545}, {"sql": "update `order_transactions` set `total_amount` = 605.1, `order_transactions`.`updated_at` = '2025-07-01 09:19:29' where `id` = 15", "type": "query", "params": [], "bindings": [605.1, "2025-07-01 09:19:29", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 66}, {"index": 16, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 89}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.108794, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "StripeWebhookService.php:66", "source": {"index": 15, "namespace": null, "name": "app/Services/StripeWebhookService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StripeWebhookService.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FStripeWebhookService.php&line=66", "ajax": false, "filename": "StripeWebhookService.php", "line": "66"}, "connection": "ticketgol", "explain": null, "start_percent": 91.729, "width_percent": 8.271}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 11, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 12, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 20}], "start": **********.124074, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "StripeWebHookController.php:49", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Api/V1/StripeWebHookController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\StripeWebHookController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FStripeWebHookController.php&line=49", "ajax": false, "filename": "StripeWebHookController.php", "line": "49"}, "connection": "ticketgol", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\OrderTransaction": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f49106b-8c7c-44ca-962d-36b43938f9a1\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/checkout/webhook", "status_code": "<pre class=sf-dump id=sf-dump-1638358051 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1638358051\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1356476875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356476875\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1869298870 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">evt_3Rg0LYRhkfMMoe7t1G5X4hsa</span>\"\n  \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"5 characters\">event</span>\"\n  \"<span class=sf-dump-key>api_version</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-03-31.basil</span>\"\n  \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361566</span>\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>object</span>\" => <span class=sf-dump-note>array:46</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"27 characters\">ch_3Rg0LYRhkfMMoe7t1nEma9A4</span>\"\n      \"<span class=sf-dump-key>object</span>\" => \"<span class=sf-dump-str title=\"6 characters\">charge</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>amount_captured</span>\" => <span class=sf-dump-num>60510</span>\n      \"<span class=sf-dump-key>amount_refunded</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>application</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>application_fee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>balance_transaction</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>billing_details</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>line1</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>line2</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>postal_code</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      \"<span class=sf-dump-key>calculated_statement_descriptor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Stripe</span>\"\n      \"<span class=sf-dump-key>captured</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>created</span>\" => <span class=sf-dump-num>1751361566</span>\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n      \"<span class=sf-dump-key>customer</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>destination</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>dispute</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>disputed</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>failure_balance_transaction</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>failure_code</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>failure_message</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>fraud_details</span>\" => []\n      \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>metadata</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n        \"<span class=sf-dump-key>order_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n        \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>ticket_reservation_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>on_behalf_of</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>outcome</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>advice_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>network_advice_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>network_decline_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>network_status</span>\" => \"<span class=sf-dump-str title=\"19 characters\">approved_by_network</span>\"\n        \"<span class=sf-dump-key>reason</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>risk_level</span>\" => \"<span class=sf-dump-str title=\"6 characters\">normal</span>\"\n        \"<span class=sf-dump-key>risk_score</span>\" => <span class=sf-dump-num>62</span>\n        \"<span class=sf-dump-key>seller_message</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Payment complete.</span>\"\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">authorized</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>paid</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>payment_intent</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pi_3Rg0LYRhkfMMoe7t1wm4olLN</span>\"\n      \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"27 characters\">pm_1Rg0LaRhkfMMoe7tDrKnoulg</span>\"\n      \"<span class=sf-dump-key>payment_method_details</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>card</span>\" => <span class=sf-dump-note>array:22</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>amount_authorized</span>\" => <span class=sf-dump-num>60510</span>\n          \"<span class=sf-dump-key>authorization_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">526200</span>\"\n          \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n          \"<span class=sf-dump-key>checks</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>address_line1_check</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>address_postal_code_check</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>cvc_check</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pass</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">US</span>\"\n          \"<span class=sf-dump-key>exp_month</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>exp_year</span>\" => <span class=sf-dump-num>2029</span>\n          \"<span class=sf-dump-key>extended_authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">disabled</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>fingerprint</span>\" => \"<span class=sf-dump-str title=\"16 characters\">4qHsohpy0cF1sMdU</span>\"\n          \"<span class=sf-dump-key>funding</span>\" => \"<span class=sf-dump-str title=\"6 characters\">credit</span>\"\n          \"<span class=sf-dump-key>incremental_authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unavailable</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>installments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>last4</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4242</span>\"\n          \"<span class=sf-dump-key>mandate</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>multicapture</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unavailable</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>network</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n          \"<span class=sf-dump-key>network_token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>used</span>\" => <span class=sf-dump-const>false</span>\n          </samp>]\n          \"<span class=sf-dump-key>network_transaction_id</span>\" => \"<span class=sf-dump-str title=\"15 characters\">521137211511110</span>\"\n          \"<span class=sf-dump-key>overcapture</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>maximum_amount_capturable</span>\" => <span class=sf-dump-num>60510</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unavailable</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>regulated_status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">unregulated</span>\"\n          \"<span class=sf-dump-key>three_d_secure</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>wallet</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">card</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>radar_options</span>\" => []\n      \"<span class=sf-dump-key>receipt_email</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_number</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>receipt_url</span>\" => \"<span class=sf-dump-str title=\"156 characters\">https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJ_QjsMGMgYfH1orBJ86LBYlv7MroKVZAJVmfO0ldXg3fI4O7OvOvTWW932avX_nbo154-WA17fozLg-</span>\"\n      \"<span class=sf-dump-key>refunded</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>review</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>source</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>source_transfer</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>statement_descriptor_suffix</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">succeeded</span>\"\n      \"<span class=sf-dump-key>transfer_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>transfer_group</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>livemode</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>pending_webhooks</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>request</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"18 characters\">req_5E0EQySila4yOc</span>\"\n    \"<span class=sf-dump-key>idempotency_key</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9affd8cd-0f1a-4eb2-8065-4a2ef68655cb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">charge.succeeded</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869298870\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-865273465 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>stripe-signature</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"148 characters\">t=1751361567,v1=3245f62d84836e6d5ce94750c66f6faa419096f34deddf6a7277a619087012b8,v0=6770ce378a1d38bb104c82451999f8059e59274c504b39c7958ddde0a01b7e47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">application/json; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">*/*; q=0.5, application/xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3834</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">Stripe/1.0 (+https://stripe.com/docs/webhooks)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865273465\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1952884804 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1952884804\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2044941016 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:19:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044941016\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-506213037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-506213037\", {\"maxDepth\":0})</script>\n"}}