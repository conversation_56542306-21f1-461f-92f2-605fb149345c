{"__meta": {"id": "X54e2f6efe7e653d0645f3638a87165aa", "datetime": "2025-07-01 07:05:01", "utime": **********.384028, "method": "GET", "uri": "/api/v1/events/event-west<PERSON>-<PERSON><PERSON><PERSON>-5", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.203732, "end": **********.384046, "duration": 0.1803140640258789, "duration_str": "180ms", "measures": [{"label": "Booting", "start": **********.203732, "relative_start": 0, "end": **********.270587, "relative_end": **********.270587, "duration": 0.06685495376586914, "duration_str": "66.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.270602, "relative_start": 0.06686997413635254, "end": **********.384049, "relative_end": 2.86102294921875e-06, "duration": 0.11344695091247559, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7365392, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/{slug}", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@show", "namespace": null, "prefix": "api/v1/events", "where": [], "as": "api.events.show", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=111\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:111-124</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04291, "accumulated_duration_str": "42.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs' limit 1", "type": "query", "params": [], "bindings": ["huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.275277, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.701}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.278529, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.701, "width_percent": 1.655}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `stadium_id`, `league_id`, `home_club_id`, `guest_club_id`, `is_feature_event`, (select MIN(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `min_price`, (select MAX(price) from `tickets` where `event_id` = `events`.`id` and `tickets`.`deleted_at` is null) as `max_price` from `events` where exists (select * from `slugs` where `events`.`id` = `slugs`.`sluggable_id` and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event' and `slug` = 'event-westley-eichmann-5' and `locale` = 'en' and `locale` = 'en') and `is_published` = 1 and `events`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Event", "event-westley-<PERSON><PERSON>mann-5", "en", "en", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.2843268, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 16, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 3.356, "width_percent": 6.735}, {"sql": "select `event_id`, `locale`, `name`, `description`, `meta_title`, `meta_description`, `meta_keywords` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.289451, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 10.091, "width_percent": 1.328}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (1) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.292291, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 11.419, "width_percent": 1.328}, {"sql": "select `stadium_id`, `locale`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.295124, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 12.748, "width_percent": 1.608}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (112) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.2986228, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 14.356, "width_percent": 1.771}, {"sql": "select `country_id`, `locale`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (112)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 32, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.302171, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 31, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 16.127, "width_percent": 1.398}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3047721, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 17.525, "width_percent": 1.515}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (7) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.30751, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 19.04, "width_percent": 1.235}, {"sql": "select `league_id`, `locale`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.310175, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 20.275, "width_percent": 1.282}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3137581, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 21.557, "width_percent": 1.515}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (9) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.316432, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 23.072, "width_percent": 1.468}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.319282, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 24.54, "width_percent": 1.305}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (9) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.321596, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 25.845, "width_percent": 1.631}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (4) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.323874, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 27.476, "width_percent": 0.909}, {"sql": "select `club_id`, `locale`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.326281, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 28.385, "width_percent": 1.608}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 27, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.329306, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 26, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 29.993, "width_percent": 1.771}, {"sql": "select `stadium_sectors`.`id`, `event_stadium_sectors`.`event_id` as `pivot_event_id`, `event_stadium_sectors`.`stadium_sector_id` as `pivot_stadium_sector_id`, `event_stadium_sectors`.`created_at` as `pivot_created_at`, `event_stadium_sectors`.`updated_at` as `pivot_updated_at` from `stadium_sectors` inner join `event_stadium_sectors` on `stadium_sectors`.`id` = `event_stadium_sectors`.`stadium_sector_id` where `event_stadium_sectors`.`event_id` in (5) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3320172, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 31.764, "width_percent": 1.911}, {"sql": "select `stadium_sector_id`, `locale`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (6, 7, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.335451, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 33.675, "width_percent": 1.422}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (5) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 21, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3378801, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 20, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 35.097, "width_percent": 1.072}, {"sql": "select `restriction_id`, `locale`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (2, 5, 7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 26, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.341429, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 25, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 36.169, "width_percent": 2.377}, {"sql": "select * from `media` where `media`.`model_id` in (5) and `media`.`model_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.3447871, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 38.546, "width_percent": 1.538}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, {"index": 22, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.347286, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EventRepository.php:222", "source": {"index": 21, "namespace": null, "name": "app/Repositories/EventRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\EventRepository.php", "line": 222}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FEventRepository.php&line=222", "ajax": false, "filename": "EventRepository.php", "line": "222"}, "connection": "ticketgol", "explain": null, "start_percent": 40.084, "width_percent": 1.422}, {"sql": "select tickets.id, tickets.quantity - COALESCE(SUM(r.quantity), 0) as available_quantity from `tickets` left join `ticket_reservations` as `r` on `r`.`ticket_id` = `tickets`.`id` and `status` in ('active', 'processing') where `tickets`.`event_id` = 5 and `tickets`.`event_id` is not null and `tickets`.`deleted_at` is null and `tickets`.`deleted_at` is null group by `tickets`.`id`, `tickets`.`quantity`", "type": "query", "params": [], "bindings": ["active", "processing", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 114}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.3507192, "duration": 0.01028, "duration_str": "10.28ms", "memory": 0, "memory_str": null, "filename": "EventService.php:82", "source": {"index": 17, "namespace": null, "name": "app/Services/EventService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\EventService.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FEventService.php&line=82", "ajax": false, "filename": "EventService.php", "line": "82"}, "connection": "ticketgol", "explain": null, "start_percent": 41.505, "width_percent": 23.957}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.367867, "duration": 0.01482, "duration_str": "14.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 65.463, "width_percent": 34.537}]}, "models": {"data": {"App\\Models\\Slug": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Restriction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 31, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders/24\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f48e055-2771-4ca6-ac9b-5b7183bc1fc2\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/event-west<PERSON>-<PERSON><PERSON><PERSON>-5", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Ii9PbzBFV1R4R1JZV2EvdFl1K2xRV2c9PSIsInZhbHVlIjoiaHdxUm5Rem4yMm9OeUxEeWpZemtiU3RvaUUyNHdXcnlOZ1lTWkhSSndjOG9hQ1JWVDFNMnV0R1ZlelpRanFGcDZYbHhaQkZBYWFTVTl5TXBIL1BMU0ZsNDRHM0RQZUxTUE5FVG1lTzRuQ3F3bDkzcE5BLzBhRkhGdjJxUnlZNEsiLCJtYWMiOiJlZDA0N2ZmY2RhMjQwYmM3YmQ4MTljZTYwYTIxNzE3YmE5NTRhZTY1NjlhYWI5ODI1OWRiZTVhOTg0OTMxMTk2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Inp1TEdhKzVqV2FCbnJEUDR4NTNMYUE9PSIsInZhbHVlIjoiNkJ2dmtjYmI5TVRNSDFBd1IwZWJvWmdlT24zSlNpNXJKN2xnUDhXWmxKS1ZmZlJBdTFhYzU0QnNqdlE3c2ZjV2V3QThub0YwWHBvaXlBOWhGcldGYmp0RExLSjBDVHFwbWZCeHN4aklRL3JjWnY4c2FUUERNRlYxbkdTUHN0KzUiLCJtYWMiOiJhNDE0ODJkMzhhNGUwOGIxMjNkNjNhZDgwNDI1MWI3Njc3YzJiM2I1NmQ4YTkwOTg3ZjliOWM5YjNkOGU2NDdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://ticketgol.test/event-westley-eichmann-5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ii9PbzBFV1R4R1JZV2EvdFl1K2xRV2c9PSIsInZhbHVlIjoiaHdxUm5Rem4yMm9OeUxEeWpZemtiU3RvaUUyNHdXcnlOZ1lTWkhSSndjOG9hQ1JWVDFNMnV0R1ZlelpRanFGcDZYbHhaQkZBYWFTVTl5TXBIL1BMU0ZsNDRHM0RQZUxTUE5FVG1lTzRuQ3F3bDkzcE5BLzBhRkhGdjJxUnlZNEsiLCJtYWMiOiJlZDA0N2ZmY2RhMjQwYmM3YmQ4MTljZTYwYTIxNzE3YmE5NTRhZTY1NjlhYWI5ODI1OWRiZTVhOTg0OTMxMTk2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-860876268 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860876268\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1932471149 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 07:05:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjdOYmk3d2FxTXd4YXJGaTVLUnhMSXc9PSIsInZhbHVlIjoiakRORlZNNUdMeXV5UVExSmJpYjJhcGhxenprZ3A5Rm5nLzM0ZGp3ekZkdC9lNzZtRnJkbkFxOS9uOHU5ZUtFU1NqV1lOUVNjcjZ5ZEhHbnZ6ODlyaVZJeGdqZ2tZZlQ1VjRzZTIwRU9rMDhCVzZRR3BIM094c2NzTWJJVEM4eW0iLCJtYWMiOiI1OTE1MGZhMGEzZmM4YTY3MDIyMDgyNWYwOWQ1NTk2ZjQyZDE1NTRmMmMzMTYzYmVmMWM2YzhhZDE0ZDk0YmJmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:05:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkM0dXFiaGVsL0d5QWIxeUtJRjJuZlE9PSIsInZhbHVlIjoiUXBjVjJYdHYyN1FTRWhDTWR4MldWbkNySU5Fc3BRb0hHSnR1d2pPUVlkM29qZ3A3Y2tPS085VU9DbUFWeFZIYldQMndMYzhkYitteXpVWStvNDJrMnhIRFZLSUg1TlZ2V0p4T3JXQ29Sbm0yd0lJWXZ4Q2NFVVVEQU5JMGtpQmEiLCJtYWMiOiJhMTNiN2E1MTBiNTM4YjU3Y2QwZTE5ZTM5MDVmMWI4NTMxNDVlMWQzMGFkNmM0MzI0ZDQ4YzM5N2FjYjE2NDBmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:05:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjdOYmk3d2FxTXd4YXJGaTVLUnhMSXc9PSIsInZhbHVlIjoiakRORlZNNUdMeXV5UVExSmJpYjJhcGhxenprZ3A5Rm5nLzM0ZGp3ekZkdC9lNzZtRnJkbkFxOS9uOHU5ZUtFU1NqV1lOUVNjcjZ5ZEhHbnZ6ODlyaVZJeGdqZ2tZZlQ1VjRzZTIwRU9rMDhCVzZRR3BIM094c2NzTWJJVEM4eW0iLCJtYWMiOiI1OTE1MGZhMGEzZmM4YTY3MDIyMDgyNWYwOWQ1NTk2ZjQyZDE1NTRmMmMzMTYzYmVmMWM2YzhhZDE0ZDk0YmJmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:05:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkM0dXFiaGVsL0d5QWIxeUtJRjJuZlE9PSIsInZhbHVlIjoiUXBjVjJYdHYyN1FTRWhDTWR4MldWbkNySU5Fc3BRb0hHSnR1d2pPUVlkM29qZ3A3Y2tPS085VU9DbUFWeFZIYldQMndMYzhkYitteXpVWStvNDJrMnhIRFZLSUg1TlZ2V0p4T3JXQ29Sbm0yd0lJWXZ4Q2NFVVVEQU5JMGtpQmEiLCJtYWMiOiJhMTNiN2E1MTBiNTM4YjU3Y2QwZTE5ZTM5MDVmMWI4NTMxNDVlMWQzMGFkNmM0MzI0ZDQ4YzM5N2FjYjE2NDBmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:05:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932471149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/24</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}