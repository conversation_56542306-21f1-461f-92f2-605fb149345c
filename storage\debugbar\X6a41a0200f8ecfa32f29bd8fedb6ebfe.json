{"__meta": {"id": "X6a41a0200f8ecfa32f29bd8fedb6ebfe", "datetime": "2025-07-01 06:40:58", "utime": 1751352058.433238, "method": "GET", "uri": "/events", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751352033.940128, "end": 1751352058.433252, "duration": 24.49312400817871, "duration_str": "24.49s", "measures": [{"label": "Booting", "start": 1751352033.940128, "relative_start": 0, "end": **********.101858, "relative_end": **********.101858, "duration": 0.1617298126220703, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101871, "relative_start": 0.1617429256439209, "end": 1751352058.433254, "relative_end": 1.9073486328125e-06, "duration": 24.331382989883423, "duration_str": "24.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 17778224, "peak_usage_str": "17MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 34, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": 1751352048.146152, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": 1751352048.538569, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": 1751352049.616329, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751352051.051544, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": 1751352051.440763, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1751352051.459169, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1751352051.461901, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352054.834115, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.049428, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.41355, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.41972, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.468739, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.472499, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.517805, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.521053, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.570774, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.576491, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.625004, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.628739, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.681913, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.685078, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.734869, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.740282, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.785516, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.788467, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.834787, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751352055.840037, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": 1751352055.907942, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1751352058.199193, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1751352058.21312, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::f07f1a332c895be3dafc362336ba959c", "param_count": null, "params": [], "start": 1751352058.286326, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/f07f1a332c895be3dafc362336ba959c.blade.php__components::f07f1a332c895be3dafc362336ba959c", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Ff07f1a332c895be3dafc362336ba959c.blade.php&line=1", "ajax": false, "filename": "f07f1a332c895be3dafc362336ba959c.blade.php", "line": "?"}}, {"name": "filament-language-switch::language-switch", "param_count": null, "params": [], "start": 1751352058.290934, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/language-switch.blade.phpfilament-language-switch::language-switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Flanguage-switch.blade.php&line=1", "ajax": false, "filename": "language-switch.blade.php", "line": "?"}}, {"name": "filament-language-switch::switch", "param_count": null, "params": [], "start": 1751352058.29194, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/switch.blade.phpfilament-language-switch::switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": 1751352058.421087, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}]}, "route": {"uri": "GET events", "domain": "admin.ticketgol.test", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents@__invoke", "as": "filament.admin.resources.events.index", "namespace": null, "where": [], "prefix": "events", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 41, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03616, "accumulated_duration_str": "36.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7' limit 1", "type": "query", "params": [], "bindings": ["zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.116627, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.853}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.120869, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.853, "width_percent": 1.991}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.142356, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "ticketgol", "explain": null, "start_percent": 3.844, "width_percent": 1.963}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.1478808, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "ticketgol", "explain": null, "start_percent": 5.808, "width_percent": 2.655}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.150852, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "ticketgol", "explain": null, "start_percent": 8.462, "width_percent": 1.549}, {"sql": "select count(*) as aggregate from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751352047.378322, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "ticketgol", "explain": null, "start_percent": 10.011, "width_percent": 4.01}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751352047.4027848, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 14.021, "width_percent": 5.061}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751352047.40714, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 19.082, "width_percent": 2.378}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": 1751352047.410779, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 21.46, "width_percent": 4.038}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": 1751352047.413738, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 25.498, "width_percent": 1.576}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:40:58' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.218723, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 27.074, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 06:40:58' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2241452, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 28.899, "width_percent": 1.715}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 06:40:58' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2284808, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 30.614, "width_percent": 3.07}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 06:40:58' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2326941, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 33.684, "width_percent": 1.742}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:40:58' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.237136, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 35.426, "width_percent": 1.632}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 06:40:58' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.24126, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 37.058, "width_percent": 1.963}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 06:40:58' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2448459, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 39.021, "width_percent": 1.521}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 06:40:58' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.248738, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 40.542, "width_percent": 1.438}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 06:40:58' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.252405, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 41.98, "width_percent": 1.466}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 06:40:58' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2563689, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 43.446, "width_percent": 1.521}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2602658, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 44.967, "width_percent": 1.189}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 06:40:58' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.264097, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 46.156, "width_percent": 2.019}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 06:40:58' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2686398, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 48.175, "width_percent": 2.102}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 06:40:58' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2728748, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 50.277, "width_percent": 2.074}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 06:40:58' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.2770119, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 52.351, "width_percent": 1.576}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:40:58' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.314201, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 53.927, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 06:40:58' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.319134, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 55.752, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 06:40:58' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.322926, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 57.577, "width_percent": 1.604}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 06:40:58' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.327331, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 59.181, "width_percent": 1.908}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:40:58' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.332474, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 61.09, "width_percent": 1.687}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 06:40:58' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.335999, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 62.777, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 06:40:58' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.33981, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 64.602, "width_percent": 1.493}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 06:40:58' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.343307, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 66.095, "width_percent": 1.244}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 06:40:58' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.347574, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 67.34, "width_percent": 1.715}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 06:40:58' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.351275, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 69.054, "width_percent": 1.687}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.3550582, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 70.741, "width_percent": 1.3}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 06:40:58' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.3584259, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 72.041, "width_percent": 1.604}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 06:40:58' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.362173, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 73.645, "width_percent": 1.853}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 06:40:58' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.367537, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 75.498, "width_percent": 1.521}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 06:40:58' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751352058.37178, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 77.019, "width_percent": 1.659}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9', `last_activity` = 1751352058, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9", 1751352058, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": 1751352058.424196, "duration": 0.00771, "duration_str": "7.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 78.678, "width_percent": 21.322}]}, "models": {"data": {"App\\Models\\Event": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Slug": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"app.filament.resources.event-resource.pages.manage-events #VCnbU38dHdfHsFNtCNk8": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:2 [\n      \"created_at\" => false\n      \"updated_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.event-resource.pages.manage-events\"\n  \"component\" => \"App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents\"\n  \"id\" => \"VCnbU38dHdfHsFNtCNk8\"\n]", "filament-language-switch #Kx9jUKodn4tM3WY95chK": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"Kx9jUKodn4tM3WY95chK\"\n]", "filament.livewire.notifications #A0ZD320qiqPoiYWxY2uJ": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#8944\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"A0ZD320qiqPoiYWxY2uJ\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 172, "messages": [{"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1378988690 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378988690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.153728, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-331226506 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331226506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.15441, "xdebug_link": null}, {"message": "[\n  ability => create_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-638925402 data-indent-pad=\"  \"><span class=sf-dump-note>create_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">create_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-638925402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.202541, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-622622248 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622622248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.203263, "xdebug_link": null}, {"message": "[\n  ability => reorder_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-636846075 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reorder_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636846075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352035.352901, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1582889706 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582889706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352035.353603, "xdebug_link": null}, {"message": "[\n  ability => delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-427997608 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427997608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352038.015079, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-743502347 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743502347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352038.015975, "xdebug_link": null}, {"message": "[\n  ability => force_delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793994566 data-indent-pad=\"  \"><span class=sf-dump-note>force_delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">force_delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793994566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352038.044776, "xdebug_link": null}, {"message": "[\n  ability => forceDeleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1247896083 data-indent-pad=\"  \"><span class=sf-dump-note>forceDeleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">forceDeleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247896083\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352038.045478, "xdebug_link": null}, {"message": "[\n  ability => restore_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-813188496 data-indent-pad=\"  \"><span class=sf-dump-note>restore_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">restore_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813188496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352038.076399, "xdebug_link": null}, {"message": "[\n  ability => restoreAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1486134498 data-indent-pad=\"  \"><span class=sf-dump-note>restoreAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restoreAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486134498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352038.076894, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-780554429 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780554429\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352052.856226, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-682174292 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682174292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352052.856764, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-772951392 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772951392\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352052.859152, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1820315351 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820315351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352052.859843, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1322007222 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322007222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352054.675774, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-694921426 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694921426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352054.676205, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1013089150 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013089150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352054.830119, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-568922764 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568922764\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352054.830519, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1874993956 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874993956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.367749, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2444216 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2444216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.368615, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-576662785 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576662785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.371365, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-87586998 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87586998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.371991, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2073102710 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073102710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.40627, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1647986058 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647986058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.406662, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-976262686 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976262686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.4087, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-292315794 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292315794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.409078, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-317284403 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317284403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.412527, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1363399055 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363399055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.412881, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1224768110 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224768110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.418771, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-87795268 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87795268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.41911, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059968192 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059968192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.424315, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1037325247 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037325247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.424658, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085651789 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085651789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.426201, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1455634698 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455634698\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.426503, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1091875281 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091875281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.462917, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-829992979 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829992979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.463423, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1968992075 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968992075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.467254, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2056768307 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056768307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.467958, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1679659442 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679659442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.479879, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1637553496 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637553496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.480387, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-452717084 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452717084\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.482132, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-848016601 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848016601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.482472, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1439613663 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439613663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.513072, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-76891972 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76891972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.513417, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1584801852 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584801852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.516757, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1233414835 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233414835\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.517136, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-538047324 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538047324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.526227, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1143595236 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143595236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.526684, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-145339974 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145339974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.528553, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1507039011 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507039011\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.528955, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-494375751 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494375751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.56238, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-529568371 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529568371\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.562853, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-510538524 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510538524\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.565046, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985671833 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985671833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.565478, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-394010555 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394010555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.569586, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1303290355 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303290355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.570049, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1774544929 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774544929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.575268, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-787740661 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787740661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.575743, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-394064704 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394064704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.58152, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1587052235 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587052235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.58203, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-346417437 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346417437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.584703, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-597730479 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597730479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.585128, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-420411346 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420411346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.619676, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-383149885 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383149885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.620124, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-523710650 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523710650\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.62371, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1801818920 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801818920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.624179, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1402636797 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402636797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.636298, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1844873612 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844873612\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.636864, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-337758026 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337758026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.63886, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-87559362 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87559362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.639286, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1702063108 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702063108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.675355, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-756413673 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756413673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.676057, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1697719025 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697719025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.680782, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1465787123 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465787123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.68122, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1013867232 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013867232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.690127, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1558463015 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558463015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.690547, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1630802890 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630802890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.693721, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-378591366 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378591366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.694392, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2108415224 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108415224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.72836, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1206491332 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206491332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.728796, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-606177239 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606177239\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.730443, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1967447850 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967447850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.730746, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2015940645 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015940645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.733872, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1362597723 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362597723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.734207, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-798829504 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798829504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.73918, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1477702701 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477702701\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.739626, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-822142458 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822142458\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.745077, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1930301116 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930301116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.745477, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2051015397 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051015397\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.74708, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1209096787 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209096787\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.747392, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-128851717 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128851717\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.780145, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-469014891 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-469014891\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.780521, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1729988792 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729988792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.784154, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1680624743 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680624743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.78469, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-378036809 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378036809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.792977, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1850709898 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850709898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.793396, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1058670899 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058670899\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.795297, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2010562884 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010562884\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.795749, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2003507796 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003507796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.826789, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1537573260 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537573260\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.827202, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1501437150 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501437150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.829003, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-929181608 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929181608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.82936, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-808577491 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808577491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.833626, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-527758233 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527758233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.834084, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1202411445 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202411445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.838981, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-728365536 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728365536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352055.839365, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1196426752 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196426752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.216307, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-354797894 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354797894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.217662, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1014296624 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014296624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.222072, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-161303089 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161303089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.2226, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-853858550 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853858550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.22664, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1965867889 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965867889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.227209, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1122822478 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122822478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.231342, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-25540873 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25540873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.231726, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1647258055 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647258055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.235316, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1271829222 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271829222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.235752, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1865772275 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865772275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.239657, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-91218750 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91218750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.240162, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-935307476 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935307476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.243588, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-141349663 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141349663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.24399, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-795671954 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795671954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.247288, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1817460960 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817460960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.247717, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-900904379 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900904379\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.250955, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-774272743 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774272743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.25129, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-327469305 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327469305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.254956, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-670064013 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-670064013\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.255373, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2101192905 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101192905\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.258686, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2146489722 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146489722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.259021, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2021223715 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021223715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.262512, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1032075741 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032075741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.262915, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1999754237 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999754237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.266878, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1931729613 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931729613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.267376, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-722906849 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722906849\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.271272, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1602881582 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602881582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.27173, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2042047792 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042047792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.27559, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-382654035 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382654035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.276044, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-649947258 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649947258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.279445, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-100642344 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100642344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.279874, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1773539474 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773539474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.312081, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1670684972 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670684972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.31303, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114378320 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114378320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.317442, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2069785640 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069785640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.318012, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985852125 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985852125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.321392, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-407969397 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407969397\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.321785, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1100796861 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1100796861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.325585, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627453394 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627453394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.326119, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-466396682 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466396682\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.330869, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-868929718 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868929718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.3314, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084588877 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084588877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.334712, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-635669629 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635669629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.335096, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1448600337 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448600337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.338366, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1769103059 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769103059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.338818, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-409437006 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409437006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.341945, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-873002069 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873002069\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.342328, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-220341481 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220341481\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.345648, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1223186579 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223186579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.346255, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1920843776 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920843776\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.349864, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1952659432 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952659432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.350265, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-167540872 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167540872\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.35381, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2053116102 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053116102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.354249, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1303747611 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303747611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.357152, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1573041979 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573041979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.35752, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-695059825 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695059825\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.360709, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-112992172 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112992172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.361078, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1630304353 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630304353\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.365519, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1577115892 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577115892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.36621, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1154627189 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154627189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.370066, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2144205207 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144205207\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.370784, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1554278385 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554278385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.373868, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2060228957 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060228957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751352058.374308, "xdebug_link": null}]}, "session": {"_token": "u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j", "url": "[]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://admin.ticketgol.test/events\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2"}, "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f48d7bb-62c4-444b-9c95-8a74505a3c9e\" target=\"_blank\">View in Telescope</a>", "path_info": "/events", "status_code": "<pre class=sf-dump id=sf-dump-1813980677 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1813980677\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2132786135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2132786135\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-236661197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-236661197\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-690662761 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1147 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Inp4dXZ4UnFrV2RUYVFwNnFJakE3Znc9PSIsInZhbHVlIjoiUDVRenU3cTg3SVV6UWdPSVdUQzRFRGI2ck12ZW9LNkRFQURvaG5YTHJxZTR2bUZlZkk4aTAxK1BNVnRuUXBHT0c2SFZRbGQzbkdqUTNxTG0vTDVRNkphQmlyZGtaN1cyeUNkVWxPNXhyY0wyQVhjT3FmWEc5eFoyODl1TUJ0UVgiLCJtYWMiOiI1ZjE1YzNhYjZjMzQyYmVkYWE4NGEzMmY4NDcxZDdjMWNkYzhhNGIyNjllNTVjNjhlZTY5ODIzNTJjNWVmM2E4IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InBWaEgzN29uczN4QmJwd1JobFZKOGc9PSIsInZhbHVlIjoia3RaMzVIVmY1MmYwZ0xpV05zOXdadUxzTDBhbTNpZ1Erbmw5bjY2bWpLczhiN3BUQVc2K0FPMCtTY3duRDVXNDJCbjAwV1lQTUJNSUpZZGEvTVdpTzVEeUF0cEtpUFlSOFZXaUQ5NmpkZzZ4UzNVSjNCU1JXMDV6b3JQK0RpZHUiLCJtYWMiOiIzMjZmM2Y1ZDZkZjk1NWQ1YzEyZGJlNjA4ODY0YWFmY2NmYzM3YjZhNmE1NmJiMjQ1ZjZjZTliYWJkYzAzOTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://admin.ticketgol.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690662761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1642204799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642204799\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893758383 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 06:40:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii8xRW95eWtha0Eza1lZMUpDSmxrS1E9PSIsInZhbHVlIjoiVWRzTFJiNm83dlVkSXdDdlBXSktiNWFBUlV5cU55SHZ3SlI3ckJKNzhSTnFWdFd6SXJXd21xekc0aWNma2YrdGxWdStyMHg4OXJSZzloenEzakxuazd5VitkamJ6MGtGeDBhSks0akZsVmpsVGplb2FXdlNCTnNib0xPNDRIT1ciLCJtYWMiOiIzMDE4NjA4NzlmMjdiZjJlM2M1NTQ4NTk4NGJhZTRjNDFiNTFkMjE4MGVkYTU2Y2M2NjFjNWExZjY3ZTkzMDVjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 08:40:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkZkV09SOFFLSE1SWDM0VUdwMmF3b3c9PSIsInZhbHVlIjoiOEZnQ2V4ZE1EaFgrdkNnOEFnSTRkRitNakpqeWpieFRlTW5JeGtjdDZDalU3aFBlUXk3cDRIbkpERUY3U2lLRnc4ZHQ2VFdEUm5BcjRqc3BGblpXT3p4amswbDE4d3o1R1ZsbDRycVZZUTUwNUZPRnBqZStBM0FmcTF3L0tid1kiLCJtYWMiOiJhMTQ5MjBhMTRmMDQ5OWI2OGY5ZDMyNDBmMWIzMGM0YTU0NTAzNTBjMzg3ODU4MmYyYjc0ODM2ZTcwNmU0YzkwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 08:40:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii8xRW95eWtha0Eza1lZMUpDSmxrS1E9PSIsInZhbHVlIjoiVWRzTFJiNm83dlVkSXdDdlBXSktiNWFBUlV5cU55SHZ3SlI3ckJKNzhSTnFWdFd6SXJXd21xekc0aWNma2YrdGxWdStyMHg4OXJSZzloenEzakxuazd5VitkamJ6MGtGeDBhSks0akZsVmpsVGplb2FXdlNCTnNib0xPNDRIT1ciLCJtYWMiOiIzMDE4NjA4NzlmMjdiZjJlM2M1NTQ4NTk4NGJhZTRjNDFiNTFkMjE4MGVkYTU2Y2M2NjFjNWExZjY3ZTkzMDVjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 08:40:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkZkV09SOFFLSE1SWDM0VUdwMmF3b3c9PSIsInZhbHVlIjoiOEZnQ2V4ZE1EaFgrdkNnOEFnSTRkRitNakpqeWpieFRlTW5JeGtjdDZDalU3aFBlUXk3cDRIbkpERUY3U2lLRnc4ZHQ2VFdEUm5BcjRqc3BGblpXT3p4amswbDE4d3o1R1ZsbDRycVZZUTUwNUZPRnBqZStBM0FmcTF3L0tid1kiLCJtYWMiOiJhMTQ5MjBhMTRmMDQ5OWI2OGY5ZDMyNDBmMWIzMGM0YTU0NTAzNTBjMzg3ODU4MmYyYjc0ODM2ZTcwNmU0YzkwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 08:40:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893758383\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1771433500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771433500\", {\"maxDepth\":0})</script>\n"}}