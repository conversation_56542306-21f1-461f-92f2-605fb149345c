{"__meta": {"id": "X1a0e5b803d20eb869cf113e22eeabb81", "datetime": "2025-07-01 06:58:11", "utime": 1751353091.297327, "method": "GET", "uri": "/events", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.513526, "end": 1751353091.297341, "duration": 7.783815145492554, "duration_str": "7.78s", "measures": [{"label": "Booting", "start": **********.513526, "relative_start": 0, "end": **********.573779, "relative_end": **********.573779, "duration": 0.060253143310546875, "duration_str": "60.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.573789, "relative_start": 0.06026291847229004, "end": 1751353091.297343, "relative_end": 1.9073486328125e-06, "duration": 7.7235541343688965, "duration_str": "7.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 17514712, "peak_usage_str": "17MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 34, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.640384, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.646855, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.655291, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.665464, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.670569, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.674831, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.67909, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.733548, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.737761, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.782526, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.790869, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.831972, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.835349, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.877783, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.880756, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.927734, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.936582, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.977327, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.980997, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.021717, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.025702, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.080968, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.089165, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.131386, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.135239, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.17689, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353084.183477, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": 1751353084.188409, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1751353084.200589, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1751353084.217737, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::f07f1a332c895be3dafc362336ba959c", "param_count": null, "params": [], "start": 1751353086.058916, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/f07f1a332c895be3dafc362336ba959c.blade.php__components::f07f1a332c895be3dafc362336ba959c", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Ff07f1a332c895be3dafc362336ba959c.blade.php&line=1", "ajax": false, "filename": "f07f1a332c895be3dafc362336ba959c.blade.php", "line": "?"}}, {"name": "filament-language-switch::language-switch", "param_count": null, "params": [], "start": 1751353086.08345, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/language-switch.blade.phpfilament-language-switch::language-switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Flanguage-switch.blade.php&line=1", "ajax": false, "filename": "language-switch.blade.php", "line": "?"}}, {"name": "filament-language-switch::switch", "param_count": null, "params": [], "start": 1751353086.087111, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/switch.blade.phpfilament-language-switch::switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": 1751353091.250803, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}]}, "route": {"uri": "GET events", "domain": "admin.ticketgol.test", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents@__invoke", "as": "filament.admin.resources.events.index", "namespace": null, "prefix": "/events", "where": [], "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 41, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "42.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7' limit 1", "type": "query", "params": [], "bindings": ["zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.5768511, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.513}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.579111, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.513, "width_percent": 1.654}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.583847, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "ticketgol", "explain": null, "start_percent": 3.167, "width_percent": 1.536}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.589802, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "ticketgol", "explain": null, "start_percent": 4.703, "width_percent": 2.293}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.592587, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "ticketgol", "explain": null, "start_percent": 6.996, "width_percent": 13.165}, {"sql": "select count(*) as aggregate from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.618392, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "ticketgol", "explain": null, "start_percent": 20.161, "width_percent": 3.522}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6216218, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 23.682, "width_percent": 3.474}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.625466, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 27.157, "width_percent": 1.914}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.629981, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 29.071, "width_percent": 3.025}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6340258, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 32.096, "width_percent": 2.198}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:04' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.260181, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 34.294, "width_percent": 1.56}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 06:58:04' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.265766, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 35.854, "width_percent": 0.898}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 06:58:04' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.270683, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 36.753, "width_percent": 2.529}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 06:58:04' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.276595, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 39.281, "width_percent": 1.56}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:04' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.281163, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 40.841, "width_percent": 1.702}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 06:58:04' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.286812, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 42.543, "width_percent": 4.94}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 06:58:04' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.2937722, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 47.483, "width_percent": 1.891}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 06:58:04' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.298471, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 49.374, "width_percent": 1.654}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 06:58:04' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.304277, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 51.028, "width_percent": 1.725}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 06:58:04' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.308307, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 52.753, "width_percent": 1.371}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.3125541, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 54.124, "width_percent": 1.087}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 06:58:04' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.3166988, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 55.212, "width_percent": 2.694}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 06:58:04' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.322217, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 57.906, "width_percent": 1.3}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 06:58:04' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.325663, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 59.206, "width_percent": 2.765}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 06:58:04' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353084.330329, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 61.971, "width_percent": 0.898}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:11' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.1376338, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 62.869, "width_percent": 1.418}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 06:58:11' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.141215, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 64.287, "width_percent": 1.276}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 06:58:11' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.1445649, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 65.564, "width_percent": 1.394}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 06:58:11' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.14864, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 66.958, "width_percent": 1.182}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 06:58:11' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.152149, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 68.14, "width_percent": 1.536}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 06:58:11' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.1561909, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 69.676, "width_percent": 0.874}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 06:58:11' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.159379, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 70.551, "width_percent": 1.489}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 06:58:11' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.163459, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 72.04, "width_percent": 1.253}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 06:58:11' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.167322, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 73.292, "width_percent": 1.418}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 06:58:11' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.172287, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 74.71, "width_percent": 1.111}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.175358, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 75.821, "width_percent": 0.827}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 06:58:11' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.179262, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 76.649, "width_percent": 0.804}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 06:58:11' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.183074, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 77.452, "width_percent": 1.253}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 06:58:11' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.186446, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 78.705, "width_percent": 1.158}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 06:58:11' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 06:58:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353091.1899, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 79.863, "width_percent": 1.3}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9', `last_activity` = 1751353091, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9", 1751353091, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": 1751353091.2884068, "duration": 0.00797, "duration_str": "7.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 81.163, "width_percent": 18.837}]}, "models": {"data": {"App\\Models\\Event": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Slug": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"app.filament.resources.event-resource.pages.manage-events #PTrT1ekOK4q2HU1t28ux": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:2 [\n      \"created_at\" => false\n      \"updated_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.event-resource.pages.manage-events\"\n  \"component\" => \"App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents\"\n  \"id\" => \"PTrT1ekOK4q2HU1t28ux\"\n]", "filament-language-switch #juiNy71tjJ7UMcIIloe4": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"juiNy71tjJ7UMcIIloe4\"\n]", "filament.livewire.notifications #RlkJgFGmikDZwBz9r3Kp": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#9157\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"RlkJgFGmikDZwBz9r3Kp\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 172, "messages": [{"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-708181979 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708181979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599827, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-958802691 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958802691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.600253, "xdebug_link": null}, {"message": "[\n  ability => create_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-335994032 data-indent-pad=\"  \"><span class=sf-dump-note>create_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">create_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335994032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60158, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1091027209 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091027209\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60189, "xdebug_link": null}, {"message": "[\n  ability => reorder_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1644263873 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reorder_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644263873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60345, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-230276353 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230276353\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603745, "xdebug_link": null}, {"message": "[\n  ability => delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1155544976 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155544976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606068, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1189198075 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189198075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606424, "xdebug_link": null}, {"message": "[\n  ability => force_delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-688062607 data-indent-pad=\"  \"><span class=sf-dump-note>force_delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">force_delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688062607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607438, "xdebug_link": null}, {"message": "[\n  ability => forceDeleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-764332727 data-indent-pad=\"  \"><span class=sf-dump-note>forceDeleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">forceDeleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764332727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607735, "xdebug_link": null}, {"message": "[\n  ability => restore_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059570424 data-indent-pad=\"  \"><span class=sf-dump-note>restore_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">restore_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059570424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.608702, "xdebug_link": null}, {"message": "[\n  ability => restoreAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1556329904 data-indent-pad=\"  \"><span class=sf-dump-note>restoreAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restoreAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556329904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.609045, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-259501134 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259501134\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.69992, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1408143451 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408143451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.700564, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1296194866 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296194866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.702838, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1957037901 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957037901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.703694, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1505318192 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505318192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727025, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1827482752 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827482752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727584, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-720846562 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720846562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.732108, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2084290571 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2084290571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.732574, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-640417293 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-640417293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.74397, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1954988833 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954988833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.744804, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-362085150 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362085150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.748626, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1484320957 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484320957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749227, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-147463721 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147463721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773712, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1712773108 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712773108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774098, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-925943304 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925943304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776035, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298679002 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298679002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776437, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1272105269 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272105269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780238, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-589006734 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589006734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780795, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107798627 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107798627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789227, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-976569010 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976569010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789858, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793073539 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793073539\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.79795, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1814492801 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814492801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.798647, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-810380006 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810380006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.801447, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852760908 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852760908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.802016, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1822685010 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822685010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.824556, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1004127293 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004127293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.824971, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1468174735 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468174735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.83076, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-66807365 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66807365\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.831258, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1067713391 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067713391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.840583, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1517468085 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517468085\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.841232, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-570675394 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570675394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.845755, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-540342221 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540342221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.84622, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-228563610 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228563610\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870167, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1607450672 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607450672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870703, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1198431231 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198431231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876338, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-167071898 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167071898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876975, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1859434289 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859434289\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885075, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1672289038 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672289038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885497, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1207348663 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207348663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887387, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-852356308 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852356308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.888249, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2063032740 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063032740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.916773, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1810175215 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810175215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917371, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-598862597 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598862597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92053, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-909624928 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909624928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.921193, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-117634009 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117634009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.926121, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-37806423 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37806423\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92678, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1854804727 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854804727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.934305, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-554931188 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554931188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.935066, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-451398459 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451398459\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943878, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2059105117 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059105117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944557, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1149673748 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149673748\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.947252, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1941282414 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941282414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948159, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1957067932 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957067932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971575, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-883848832 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883848832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972037, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-136073993 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136073993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.975886, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1766259563 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766259563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.976497, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-578572244 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578572244\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.98608, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-339038002 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339038002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.986606, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-849273965 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849273965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.989656, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1625404536 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625404536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.990316, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107317503 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107317503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.015488, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-134279932 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134279932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.016095, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-13228808 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13228808\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.020157, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1380167745 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380167745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.020692, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1759062891 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759062891\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.034276, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1364095665 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364095665\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.034934, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-252334990 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252334990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.037985, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-933925883 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933925883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.038795, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176967071 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176967071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.069455, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-903196418 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903196418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.069969, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1370152966 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370152966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.073065, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1900390359 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900390359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.073783, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-416092498 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416092498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.079379, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-714776944 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714776944\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.079965, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-48149985 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48149985\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.086791, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-143018660 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143018660\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.087464, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-328412837 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328412837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.095936, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1036903768 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036903768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.096634, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1219335288 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219335288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.099324, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-853336415 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853336415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.0999, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1146157565 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146157565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.124731, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-549753317 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549753317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.125297, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1467264267 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467264267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.130003, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2090008210 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090008210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.13053, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1659058735 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1659058735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.141627, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1125105658 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125105658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.142218, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-51387391 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51387391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.144678, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-890295618 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890295618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.145222, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1069711030 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069711030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.168617, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-181018346 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181018346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.169098, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-508274002 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508274002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.171208, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-686118170 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686118170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.171695, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1168029461 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168029461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.175323, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1571797455 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571797455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.17583, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-954930886 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954930886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.182085, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-887550062 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887550062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.182657, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-876860871 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876860871\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.257747, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-396023565 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396023565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.259013, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-702399374 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702399374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.264063, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1817583507 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817583507\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.264626, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1211179386 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211179386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.268713, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-139812545 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139812545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.269259, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2078205383 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078205383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.274543, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1016819441 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016819441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.275167, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1848370835 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848370835\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.279534, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-541741395 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541741395\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.280101, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2050832182 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050832182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.284899, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-888056049 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888056049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.285419, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-144229734 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144229734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.291326, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1605052162 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605052162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.291917, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-7558862 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7558862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.296539, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-582268295 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582268295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.296985, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1104531054 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104531054\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.301924, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1180616510 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180616510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.30271, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2110759465 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110759465\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.306886, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1197734616 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197734616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.30728, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2001770216 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001770216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.310699, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1533006047 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533006047\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.311128, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-667226813 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667226813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.315114, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-651079714 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651079714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.315524, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-44951937 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44951937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.320747, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1947752690 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947752690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.321305, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1414704299 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414704299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.324372, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1910898927 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910898927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.324687, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1823021142 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823021142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.32886, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309373427 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309373427\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.329306, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1885136249 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885136249\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.332452, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-948040284 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948040284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353084.332962, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-951320327 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951320327\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.135831, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1601746839 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601746839\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.136695, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-65887085 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65887085\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.139998, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1729383336 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729383336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.140354, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-885582441 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885582441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.143287, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1244482969 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244482969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.143679, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1757516828 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757516828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.1471, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-404530232 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404530232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.147558, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-586265221 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586265221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.150706, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2048459564 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048459564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.151123, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-755117892 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755117892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.154478, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1753037999 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753037999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.155087, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1446343852 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446343852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.158132, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-184700767 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184700767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.158503, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-203046196 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203046196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.161935, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-490982599 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490982599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.162509, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1357968180 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357968180\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.165642, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2023803225 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023803225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.166141, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-186453135 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186453135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.169675, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1681420652 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681420652\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.170751, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1419942075 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419942075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.174233, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1584363925 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584363925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.174582, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-855224511 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855224511\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.177777, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1239303065 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239303065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.178244, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-329740858 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329740858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.181407, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-781810413 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781810413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.181857, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1825517328 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825517328\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.185204, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-185550826 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185550826\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.18557, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-574676892 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574676892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.188604, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1126935501 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126935501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.189014, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2069673185 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069673185\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.192151, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1841740357 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841740357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353091.192636, "xdebug_link": null}]}, "session": {"_token": "u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j", "url": "[]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://admin.ticketgol.test/events\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2"}, "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f48dde3-6a1b-4531-8710-50a5203cf8e7\" target=\"_blank\">View in Telescope</a>", "path_info": "/events", "status_code": "<pre class=sf-dump id=sf-dump-1509746838 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1509746838\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1164321651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1164321651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1347978647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1347978647\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-399405613 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1147 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6Ii8xRW95eWtha0Eza1lZMUpDSmxrS1E9PSIsInZhbHVlIjoiVWRzTFJiNm83dlVkSXdDdlBXSktiNWFBUlV5cU55SHZ3SlI3ckJKNzhSTnFWdFd6SXJXd21xekc0aWNma2YrdGxWdStyMHg4OXJSZzloenEzakxuazd5VitkamJ6MGtGeDBhSks0akZsVmpsVGplb2FXdlNCTnNib0xPNDRIT1ciLCJtYWMiOiIzMDE4NjA4NzlmMjdiZjJlM2M1NTQ4NTk4NGJhZTRjNDFiNTFkMjE4MGVkYTU2Y2M2NjFjNWExZjY3ZTkzMDVjIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkZkV09SOFFLSE1SWDM0VUdwMmF3b3c9PSIsInZhbHVlIjoiOEZnQ2V4ZE1EaFgrdkNnOEFnSTRkRitNakpqeWpieFRlTW5JeGtjdDZDalU3aFBlUXk3cDRIbkpERUY3U2lLRnc4ZHQ2VFdEUm5BcjRqc3BGblpXT3p4amswbDE4d3o1R1ZsbDRycVZZUTUwNUZPRnBqZStBM0FmcTF3L0tid1kiLCJtYWMiOiJhMTQ5MjBhMTRmMDQ5OWI2OGY5ZDMyNDBmMWIzMGM0YTU0NTAzNTBjMzg3ODU4MmYyYjc0ODM2ZTcwNmU0YzkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399405613\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1739236776 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739236776\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-611041776 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 06:58:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxoUzBCUHNQRFFJRzlJaUlic1BQWVE9PSIsInZhbHVlIjoielpiQ0ZZM2M4K2VyNzFMc1FnbjUzeVVNQ0k2UWR2ZTd4ZHVERVRKREQ4YTg2YzU4MTJxbVB4RmM3NTJQWnVsQytwYUlOMnFZa0puSU1XeTA3UVdTaGR2dmlxUC9vWmxqc1dma2dqUlhxWEVudmxIckZQaHdZeHR2b1MzL095djciLCJtYWMiOiJlZjA4NGFkMDViZWVmYjM3OTEzNWUyZTFjZTY3ZmU3ZTE0NzY1NDg3MTY0YWJlYjMzNTRhZmNlNzNmMGU0NzNmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 08:58:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6InJyVHV5MExISCtKbHU2clRwOEkvT0E9PSIsInZhbHVlIjoiU1FGTkczUGtOankySzZFOUptRmNZaTYrZmlvWExHVnZGb3IwSkNEMlorVjkyWTVaSzNybWU0eXg0T2tsREVPSGJmcnNRK3lPcHdoU3FCS29GVk1ScUVRdGhtWlBYN3RZMzlOMm1TWHlSZC9vckwzZ0FCbGxSNGx4U3Q1MGRVd08iLCJtYWMiOiI5OTY3ODFhZDFmMTBiNjVkMzIzNTM5OTkwY2M3NjE4ZjE4YjRmNzlmYjZjYjA3MjFjZmI1ODUxMTM1MzEwYTJiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 08:58:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxoUzBCUHNQRFFJRzlJaUlic1BQWVE9PSIsInZhbHVlIjoielpiQ0ZZM2M4K2VyNzFMc1FnbjUzeVVNQ0k2UWR2ZTd4ZHVERVRKREQ4YTg2YzU4MTJxbVB4RmM3NTJQWnVsQytwYUlOMnFZa0puSU1XeTA3UVdTaGR2dmlxUC9vWmxqc1dma2dqUlhxWEVudmxIckZQaHdZeHR2b1MzL095djciLCJtYWMiOiJlZjA4NGFkMDViZWVmYjM3OTEzNWUyZTFjZTY3ZmU3ZTE0NzY1NDg3MTY0YWJlYjMzNTRhZmNlNzNmMGU0NzNmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 08:58:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6InJyVHV5MExISCtKbHU2clRwOEkvT0E9PSIsInZhbHVlIjoiU1FGTkczUGtOankySzZFOUptRmNZaTYrZmlvWExHVnZGb3IwSkNEMlorVjkyWTVaSzNybWU0eXg0T2tsREVPSGJmcnNRK3lPcHdoU3FCS29GVk1ScUVRdGhtWlBYN3RZMzlOMm1TWHlSZC9vckwzZ0FCbGxSNGx4U3Q1MGRVd08iLCJtYWMiOiI5OTY3ODFhZDFmMTBiNjVkMzIzNTM5OTkwY2M3NjE4ZjE4YjRmNzlmYjZjYjA3MjFjZmI1ODUxMTM1MzEwYTJiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 08:58:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611041776\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1850055609 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850055609\", {\"maxDepth\":0})</script>\n"}}