{"__meta": {"id": "Xeebae45ddf474022fff6b6259e0233dd", "datetime": "2025-07-01 10:05:10", "utime": **********.392034, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751364309.901303, "end": **********.392059, "duration": 0.4907560348510742, "duration_str": "491ms", "measures": [{"label": "Booting", "start": 1751364309.901303, "relative_start": 0, "end": **********.007896, "relative_end": **********.007896, "duration": 0.10659289360046387, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.007913, "relative_start": 0.10661005973815918, "end": **********.392062, "relative_end": 2.86102294921875e-06, "duration": 0.38414883613586426, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 15935400, "peak_usage_str": "15MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 27, "templates": [{"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.234432, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.238505, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.241374, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.243959, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.245873, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.247537, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.252423, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.25488, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.256838, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.259379, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.262028, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.26437, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.269694, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.271486, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.273042, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.27486, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.276654, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.278056, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.284076, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.285984, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.287704, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.30575, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.30837, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::df99cc8a09fc17798ccfd34dd3ef1747", "param_count": null, "params": [], "start": **********.311721, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/df99cc8a09fc17798ccfd34dd3ef1747.blade.php__components::df99cc8a09fc17798ccfd34dd3ef1747", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fdf99cc8a09fc17798ccfd34dd3ef1747.blade.php&line=1", "ajax": false, "filename": "df99cc8a09fc17798ccfd34dd3ef1747.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.314038, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.319394, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.322195, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium@save", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" onclick=\"\">vendor/filament/filament/src/Resources/Pages/EditRecord.php:134-177</a>"}, "queries": {"nb_statements": 27, "nb_visible_statements": 29, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07045, "accumulated_duration_str": "70.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'VtjjOpilFuaemOVbFsrdjHugYxu9NJviG6B3aeou' limit 1", "type": "query", "params": [], "bindings": ["VtjjOpilFuaemOVbFsrdjHugYxu9NJviG6B3aeou"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.013884, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 0.965}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 35}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 83}], "start": **********.018362, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 0.965, "width_percent": 1.178}, {"sql": "select * from `stadiums` where `stadiums`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.030489, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "ticketgol", "explain": null, "start_percent": 2.143, "width_percent": 1.107}, {"sql": "select `name`, `locale` from `languages` where `is_active` = 1 and `languages`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/StadiumResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource.php", "line": 49}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 377}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 331}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 402}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 302}], "start": **********.036438, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "StadiumResource.php:49", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/StadiumResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FStadiumResource.php&line=49", "ajax": false, "filename": "StadiumResource.php", "line": "49"}, "connection": "ticketgol", "explain": null, "start_percent": 3.251, "width_percent": 1.093}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.0479102, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "ticketgol", "explain": null, "start_percent": 4.344, "width_percent": 1.391}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.055891, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "ticketgol", "explain": null, "start_percent": 5.735, "width_percent": 1.278}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.0597959, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "ticketgol", "explain": null, "start_percent": 7.012, "width_percent": 1.32}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 24}, {"index": 10, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 139}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.066137, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:24", "source": {"index": 9, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FConcerns%2FCanUseDatabaseTransactions.php&line=24", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "24"}, "connection": "ticketgol", "explain": null, "start_percent": 8.332, "width_percent": 0}, {"sql": "select count(*) as aggregate from `stadium_translations` where `name` = '<PERSON><PERSON>' and `stadium_id` <> '5' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "5", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1027}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.077791, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 8.332, "width_percent": 1.405}, {"sql": "select count(*) as aggregate from `slugs` where `slug` = 'stadium-lenore-waelchi-5' and (`locale` = 'en' and (`sluggable_id` != 5 or `sluggable_type` != 'App\\\\Models\\\\Stadium'))", "type": "query", "params": [], "bindings": ["stadium-lenore-waelchi-5", "en", 5, "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1027}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.081629, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 9.737, "width_percent": 1.221}, {"sql": "select count(*) as aggregate from `stadium_translations` where `name` = '<PERSON><PERSON>' and `stadium_id` <> '5' and `locale` = 'it'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "5", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1027}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.084648, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 10.958, "width_percent": 1.093}, {"sql": "select count(*) as aggregate from `slugs` where `slug` = 'stadium-angelina-prosacco-5' and (`locale` = 'it' and (`sluggable_id` != 5 or `sluggable_type` != 'App\\\\Models\\\\Stadium'))", "type": "query", "params": [], "bindings": ["stadium-angelina-prosacco-5", "it", 5, "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1027}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.087568, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 12.051, "width_percent": 1.178}, {"sql": "select count(*) as aggregate from `stadium_translations` where `name` = '<PERSON>' and `stadium_id` <> '5' and `locale` = 'es'", "type": "query", "params": [], "bindings": ["<PERSON>", "5", "es"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1027}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.090273, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 13.229, "width_percent": 1.036}, {"sql": "select count(*) as aggregate from `slugs` where `slug` = 'stadium-fatima-dietrich-i-5' and (`locale` = 'es' and (`sluggable_id` != 5 or `sluggable_type` != 'App\\\\Models\\\\Stadium'))", "type": "query", "params": [], "bindings": ["stadium-fatima-dietrich-i-5", "es", 5, "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1027}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.09286, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 14.265, "width_percent": 2.3}, {"sql": "select count(*) as aggregate from `countries` where `countries`.`id` = 211", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 982}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 953}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}], "start": **********.096333, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ticketgol", "explain": null, "start_percent": 16.565, "width_percent": 0.994}, {"sql": "select * from `media` where `media`.`model_id` in (5) and `media`.`model_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 534}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 256}, {"index": 26, "namespace": null, "name": "vendor/filament/spatie-laravel-media-library-plugin/src/Forms/Components/SpatieMediaLibraryFileUpload.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\spatie-laravel-media-library-plugin\\src\\Forms\\Components\\SpatieMediaLibraryFileUpload.php", "line": 253}, {"index": 27, "namespace": null, "name": "vendor/filament/spatie-laravel-media-library-plugin/src/Forms/Components/SpatieMediaLibraryFileUpload.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\spatie-laravel-media-library-plugin\\src\\Forms\\Components\\SpatieMediaLibraryFileUpload.php", "line": 126}], "start": **********.107431, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:534", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 534}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=534", "ajax": false, "filename": "InteractsWithMedia.php", "line": "534"}, "connection": "ticketgol", "explain": null, "start_percent": 17.559, "width_percent": 1.249}, {"sql": "select max(`order_column`) as aggregate from `media` where `model_type` = 'App\\\\Models\\\\Stadium' and `model_id` = 5", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Concerns/IsSorted.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Concerns\\IsSorted.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Concerns/IsSorted.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Concerns\\IsSorted.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Observers/MediaObserver.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Observers\\MediaObserver.php", "line": 15}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/FileAdder.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 454}, {"index": 28, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/FileAdder.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 441}], "start": **********.1451678, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "IsSorted.php:20", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Concerns/IsSorted.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Concerns\\IsSorted.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FConcerns%2FIsSorted.php&line=20", "ajax": false, "filename": "IsSorted.php", "line": "20"}, "connection": "ticketgol", "explain": null, "start_percent": 18.808, "width_percent": 1.604}, {"sql": "insert into `media` (`name`, `file_name`, `disk`, `conversions_disk`, `collection_name`, `mime_type`, `size`, `custom_properties`, `generated_conversions`, `responsive_images`, `manipulations`, `model_id`, `model_type`, `uuid`, `order_column`, `updated_at`, `created_at`) values ('localhost_5174_ (2)', '01JZ2Q5537MJ1PCM0QRV4HRA9A.png', 'admin', 'admin', 'default', 'image/png', 337645, '{\\\"alt\\\":\\\"stadium image\\\"}', '[]', '[]', '[]', 5, 'App\\\\Models\\\\Stadium', '4022ea87-a198-447b-8df2-7a289b050666', 1, '2025-07-01 10:05:10', '2025-07-01 10:05:10')", "type": "query", "params": [], "bindings": ["localhost_5174_ (2)", "01JZ2Q5537MJ1PCM0QRV4HRA9A.png", "admin", "admin", "default", "image/png", 337645, "{\"alt\":\"stadium image\"}", "[]", "[]", "[]", 5, "App\\Models\\Stadium", "4022ea87-a198-447b-8df2-7a289b050666", 1, "2025-07-01 10:05:10", "2025-07-01 10:05:10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/FileAdder.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 454}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/FileAdder.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 441}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/FileAdder.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 350}, {"index": 19, "namespace": null, "name": "vendor/filament/spatie-laravel-media-library-plugin/src/Forms/Components/SpatieMediaLibraryFileUpload.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\spatie-laravel-media-library-plugin\\src\\Forms\\Components\\SpatieMediaLibraryFileUpload.php", "line": 157}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.1482542, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "FileAdder.php:454", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/FileAdder.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\FileAdder.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FFileAdder.php&line=454", "ajax": false, "filename": "FileAdder.php", "line": "454"}, "connection": "ticketgol", "explain": null, "start_percent": 20.412, "width_percent": 1.675}, {"sql": "select * from `media` where `media`.`model_id` in (5) and `media`.`model_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/spatie-laravel-media-library-plugin/src/Forms/Components/SpatieMediaLibraryFileUpload.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\spatie-laravel-media-library-plugin\\src\\Forms\\Components\\SpatieMediaLibraryFileUpload.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/BelongsToModel.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Components\\Concerns\\BelongsToModel.php", "line": 87}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/BelongsToModel.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Concerns\\BelongsToModel.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 246}], "start": **********.162341, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "SpatieMediaLibraryFileUpload.php:57", "source": {"index": 20, "namespace": null, "name": "vendor/filament/spatie-laravel-media-library-plugin/src/Forms/Components/SpatieMediaLibraryFileUpload.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\spatie-laravel-media-library-plugin\\src\\Forms\\Components\\SpatieMediaLibraryFileUpload.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Fspatie-laravel-media-library-plugin%2Fsrc%2FForms%2FComponents%2FSpatieMediaLibraryFileUpload.php&line=57", "ajax": false, "filename": "SpatieMediaLibraryFileUpload.php", "line": "57"}, "connection": "ticketgol", "explain": null, "start_percent": 22.087, "width_percent": 1.207}, {"sql": "update `stadiums` set `is_published` = 1, `stadiums`.`updated_at` = '2025-07-01 10:05:10' where `id` = 5", "type": "query", "params": [], "bindings": [1, "2025-07-01 10:05:10", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 45}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 151}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.1734369, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "EditStadium.php:45", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FStadiumResource%2FPages%2FEditStadium.php&line=45", "ajax": false, "filename": "EditStadium.php", "line": "45"}, "connection": "ticketgol", "explain": null, "start_percent": 23.293, "width_percent": 1.348}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `batch_uuid`, `event`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('Resource', '{\\\"is_published\\\":true,\\\"updated_at\\\":\\\"2025-07-01 10:05:10\\\"}', 1, 'App\\\\Models\\\\User', null, 'Updated', 5, 'App\\\\Models\\\\Stadium', 'Stadium Updated by Super Admin', '2025-07-01 10:05:10', '2025-07-01 10:05:10')", "type": "query", "params": [], "bindings": ["Resource", "{\"is_published\":true,\"updated_at\":\"2025-07-01 10:05:10\"}", 1, "App\\Models\\User", null, "Updated", 5, "App\\Models\\Stadium", "Stadium Updated by Super Admin", "2025-07-01 10:05:10", "2025-07-01 10:05:10"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/z3d0x/filament-logger/src/Loggers/AbstractModelLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\z3d0x\\filament-logger\\src\\Loggers\\AbstractModelLogger.php", "line": 91}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 45}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 151}], "start": **********.179698, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ActivityLogger.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/ActivityLogger.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FActivityLogger.php&line=174", "ajax": false, "filename": "ActivityLogger.php", "line": "174"}, "connection": "ticketgol", "explain": null, "start_percent": 24.642, "width_percent": 1.448}, {"sql": "select * from `slugs` where (`sluggable_type` = 'App\\\\Models\\\\Stadium' and `sluggable_id` = 5 and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium", 5, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasSlugs.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\HasSlugs.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 151}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.183127, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasSlugs.php:30", "source": {"index": 21, "namespace": null, "name": "app/Traits/HasSlugs.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\HasSlugs.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FHasSlugs.php&line=30", "ajax": false, "filename": "HasSlugs.php", "line": "30"}, "connection": "ticketgol", "explain": null, "start_percent": 26.089, "width_percent": 1.292}, {"sql": "select * from `slugs` where (`sluggable_type` = 'App\\\\Models\\\\Stadium' and `sluggable_id` = 5 and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium", 5, "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasSlugs.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\HasSlugs.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 151}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.186726, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasSlugs.php:30", "source": {"index": 21, "namespace": null, "name": "app/Traits/HasSlugs.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\HasSlugs.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FHasSlugs.php&line=30", "ajax": false, "filename": "HasSlugs.php", "line": "30"}, "connection": "ticketgol", "explain": null, "start_percent": 27.381, "width_percent": 1.164}, {"sql": "select * from `slugs` where (`sluggable_type` = 'App\\\\Models\\\\Stadium' and `sluggable_id` = 5 and `locale` = 'es') limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Stadium", 5, "es"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasSlugs.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\HasSlugs.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 151}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.189082, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasSlugs.php:30", "source": {"index": 21, "namespace": null, "name": "app/Traits/HasSlugs.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\HasSlugs.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FHasSlugs.php&line=30", "ajax": false, "filename": "HasSlugs.php", "line": "30"}, "connection": "ticketgol", "explain": null, "start_percent": 28.545, "width_percent": 1.036}, {"sql": "insert into `stadium_translations` (`created_at`, `description`, `locale`, `meta_description`, `meta_keywords`, `meta_title`, `name`, `stadium_id`, `updated_at`) values ('2025-07-01 10:05:10', 'Rerum est voluptatem commodi quod ipsa. Illum est est natus excepturi laborum sed occaecati. Excepturi laudantium occaecati laudantium molestiae. Facilis quisquam veritatis quibusdam totam hic aut cumque.', 'en', 'Minus voluptas corrupti maxime tenetur eius dolore.', 'Sed dignissimos veritatis nobis.', 'Voluptatibus fugiat inventore quo similique earum error.', '<PERSON><PERSON>', 5, '2025-07-01 10:05:10'), ('2025-07-01 10:05:10', 'Excepturi quod saepe placeat quod eius nulla et. Velit corrupti in voluptate voluptatum. Numquam ut quae nostrum non rerum consequatur. Pariatur nemo voluptatem et reiciendis animi consequatur.', 'es', 'Accusamus voluptatem omnis quasi dicta rerum omnis voluptatem.', 'Quae quisquam reiciendis iusto.', 'Culpa perferendis repellendus vitae non provident.', 'Fatima Dietrich I', 5, '2025-07-01 10:05:10'), ('2025-07-01 10:05:10', 'Aliquid maiores voluptatem saepe debitis aut. Dolorum a temporibus quo quos asperiores quis. Eligendi repellat ab consequuntur aut voluptatum possimus aperiam.', 'it', 'Quia similique qui deserunt.', 'Sunt odit tempora dolores laborum impedit.', 'Ad quis ex qui et dolor qui nemo.', 'Angelina Prosacco', 5, '2025-07-01 10:05:10') on duplicate key update `name` = values(`name`), `description` = values(`description`), `meta_title` = values(`meta_title`), `meta_keywords` = values(`meta_keywords`), `meta_description` = values(`meta_description`), `updated_at` = values(`updated_at`)", "type": "query", "params": [], "bindings": ["2025-07-01 10:05:10", "Rerum est voluptatem commodi quod ipsa. Illum est est natus excepturi laborum sed occaecati. Excepturi laudantium occaecati laudantium molestiae. Facilis quisquam veritatis quibusdam totam hic aut cumque.", "en", "Minus voluptas corrupti maxime tenetur eius dolore.", "Sed dignissimos veritatis nobis.", "Voluptatibus fugiat inventore quo similique earum error.", "<PERSON><PERSON>", 5, "2025-07-01 10:05:10", "2025-07-01 10:05:10", "Excepturi quod saepe placeat quod eius nulla et. Velit corrupti in voluptate voluptatum. Numquam ut quae nostrum non rerum consequatur. Pariatur nemo voluptatem et reiciendis animi consequatur.", "es", "Accusamus voluptatem omnis quasi dicta rerum omnis voluptatem.", "Quae quisquam reiciendis iusto.", "Culpa perferendis repellendus vitae non provident.", "<PERSON><PERSON>", 5, "2025-07-01 10:05:10", "2025-07-01 10:05:10", "Aliquid maiores voluptatem saepe debitis aut. Dolorum a temporibus quo quos asperiores quis. Eligendi repellat ab consequuntur aut voluptatum possimus aperiam.", "it", "Quia similique qui deserunt.", "Sunt odit tempora dolores laborum impedit.", "Ad quis ex qui et dolor qui nemo.", "<PERSON>", 5, "2025-07-01 10:05:10"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 151}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.191803, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "EditStadium.php:56", "source": {"index": 12, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FStadiumResource%2FPages%2FEditStadium.php&line=56", "ajax": false, "filename": "EditStadium.php", "line": "56"}, "connection": "ticketgol", "explain": null, "start_percent": 29.581, "width_percent": 3.392}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 33}, {"index": 10, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 155}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.202962, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:33", "source": {"index": 9, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanUseDatabaseTransactions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanUseDatabaseTransactions.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FConcerns%2FCanUseDatabaseTransactions.php&line=33", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "33"}, "connection": "ticketgol", "explain": null, "start_percent": 32.974, "width_percent": 0}, {"sql": "select `country_translations`.`name`, `countries`.`id` from `countries` left join `country_translations` on `countries`.`id` = `country_translations`.`country_id` where `country_translations`.`locale` = 'en' and `countries`.`deleted_at` is null order by `country_translations`.`name` asc", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 804}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 652}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 168}], "start": **********.295266, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "Select.php:804", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 804}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=804", "ajax": false, "filename": "Select.php", "line": "804"}, "connection": "ticketgol", "explain": null, "start_percent": 32.974, "width_percent": 8.006}, {"sql": "select * from `stadium_translations` where `stadium_translations`.`stadium_id` = 5 and `stadium_translations`.`stadium_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": [5, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 15}, {"index": 22, "namespace": "view", "name": "filament-panels::components.page.index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\/../resources/views/components/page/index.blade.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3264751, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EditStadium.php:15", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/StadiumResource/Pages/EditStadium.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FStadiumResource%2FPages%2FEditStadium.php&line=15", "ajax": false, "filename": "EditStadium.php", "line": "15"}, "connection": "ticketgol", "explain": null, "start_percent": 40.979, "width_percent": 1.164}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiUzF3UEZiU2E2UDZ4MXh5NFE4SHFYSWFqMmxTRXdoblhESWt4UGY1ViI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQzOiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3Qvc3RhZGl1bXMvNS9lZGl0Ijt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjtzOjg6ImZpbGFtZW50IjthOjE6e3M6MTM6Im5vdGlmaWNhdGlvbnMiO2E6MTp7aTowO2E6MTE6e3M6MjoiaWQiO3M6MzY6IjlmNDkyMGMyLTE5YjAtNDcwYS1iMzEwLWVhNjJiMzZiZmYwMyI7czo3OiJhY3Rpb25zIjthOjA6e31zOjQ6ImJvZHkiO047czo1OiJjb2xvciI7TjtzOjg6ImR1cmF0aW9uIjtpOjYwMDA7czo0OiJpY29uIjtzOjIzOiJoZXJvaWNvbi1vLWNoZWNrLWNpcmNsZSI7czo5OiJpY29uQ29sb3IiO3M6Nzoic3VjY2VzcyI7czo2OiJzdGF0dXMiO3M6Nzoic3VjY2VzcyI7czo1OiJ0aXRsZSI7czo1OiJTYXZlZCI7czo0OiJ2aWV3IjtzOjM2OiJmaWxhbWVudC1ub3RpZmljYXRpb25zOjpub3RpZmljYXRpb24iO3M6ODoidmlld0RhdGEiO2E6MDp7fX19fX0=', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'VtjjOpilFuaemOVbFsrdjHugYxu9NJviG6B3aeou'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiUzF3UEZiU2E2UDZ4MXh5NFE4SHFYSWFqMmxTRXdoblhESWt4UGY1ViI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQzOiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3Qvc3RhZGl1bXMvNS9lZGl0Ijt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjtzOjg6ImZpbGFtZW50IjthOjE6e3M6MTM6Im5vdGlmaWNhdGlvbnMiO2E6MTp7aTowO2E6MTE6e3M6MjoiaWQiO3M6MzY6IjlmNDkyMGMyLTE5YjAtNDcwYS1iMzEwLWVhNjJiMzZiZmYwMyI7czo3OiJhY3Rpb25zIjthOjA6e31zOjQ6ImJvZHkiO047czo1OiJjb2xvciI7TjtzOjg6ImR1cmF0aW9uIjtpOjYwMDA7czo0OiJpY29uIjtzOjIzOiJoZXJvaWNvbi1vLWNoZWNrLWNpcmNsZSI7czo5OiJpY29uQ29sb3IiO3M6Nzoic3VjY2VzcyI7czo2OiJzdGF0dXMiO3M6Nzoic3VjY2VzcyI7czo1OiJ0aXRsZSI7czo1OiJTYXZlZCI7czo0OiJ2aWV3IjtzOjM2OiJmaWxhbWVudC1ub3RpZmljYXRpb25zOjpub3RpZmljYXRpb24iO3M6ODoidmlld0RhdGEiO2E6MDp7fX19fX0=", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "VtjjOpilFuaemOVbFsrdjHugYxu9NJviG6B3aeou"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.347434, "duration": 0.04076, "duration_str": "40.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 42.143, "width_percent": 57.857}]}, "models": {"data": {"App\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Slug": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": {"app.filament.resources.stadium-resource.pages.edit-stadium #QTH9dkrCn37U4CFTbuXI": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:15 [\n      \"id\" => 5\n      \"address_line_1\" => \"\"\"\n        336 Aubrey Lane\\n\n        West Elena, CA 03986\n        \"\"\"\n      \"address_line_2\" => \"\"\"\n        901 Jaskolski Run Apt. 892\\n\n        <PERSON><PERSON><PERSON>berg, AK 78099-0697\n        \"\"\"\n      \"postcode\" => \"37607-8413\"\n      \"country_id\" => 211\n      \"is_published\" => true\n      \"created_at\" => \"2025-06-16T14:45:01.000000Z\"\n      \"updated_at\" => \"2025-06-16T14:45:01.000000Z\"\n      \"deleted_at\" => null\n      \"events_count\" => 2\n      \"clubs_count\" => 1\n      \"translations\" => array:3 [\n        0 => array:10 [\n          \"id\" => 13\n          \"stadium_id\" => 5\n          \"locale\" => \"en\"\n          \"name\" => \"Lenore <PERSON>\"\n          \"description\" => \"Rerum est voluptatem commodi quod ipsa. Illum est est natus excepturi laborum sed occaecati. Excepturi laudantium occaecati laudantium molestiae. Facilis quisquam veritatis quibusdam totam hic aut cumque.\"\n          \"meta_title\" => \"Voluptatibus fugiat inventore quo similique earum error.\"\n          \"meta_description\" => \"Minus voluptas corrupti maxime tenetur eius dolore.\"\n          \"meta_keywords\" => \"Sed dignissimos veritatis nobis.\"\n          \"created_at\" => \"2025-06-16T14:45:02.000000Z\"\n          \"updated_at\" => \"2025-06-16T14:45:02.000000Z\"\n        ]\n        1 => array:10 [\n          \"id\" => 15\n          \"stadium_id\" => 5\n          \"locale\" => \"es\"\n          \"name\" => \"Fatima Dietrich I\"\n          \"description\" => \"Excepturi quod saepe placeat quod eius nulla et. Velit corrupti in voluptate voluptatum. Numquam ut quae nostrum non rerum consequatur. Pariatur nemo voluptatem et reiciendis animi consequatur.\"\n          \"meta_title\" => \"Culpa perferendis repellendus vitae non provident.\"\n          \"meta_description\" => \"Accusamus voluptatem omnis quasi dicta rerum omnis voluptatem.\"\n          \"meta_keywords\" => \"Quae quisquam reiciendis iusto.\"\n          \"created_at\" => \"2025-06-16T14:45:02.000000Z\"\n          \"updated_at\" => \"2025-06-16T14:45:02.000000Z\"\n        ]\n        2 => array:10 [\n          \"id\" => 14\n          \"stadium_id\" => 5\n          \"locale\" => \"it\"\n          \"name\" => \"Angelina Prosacco\"\n          \"description\" => \"Aliquid maiores voluptatem saepe debitis aut. Dolorum a temporibus quo quos asperiores quis. Eligendi repellat ab consequuntur aut voluptatum possimus aperiam.\"\n          \"meta_title\" => \"Ad quis ex qui et dolor qui nemo.\"\n          \"meta_description\" => \"Quia similique qui deserunt.\"\n          \"meta_keywords\" => \"Sunt odit tempora dolores laborum impedit.\"\n          \"created_at\" => \"2025-06-16T14:45:02.000000Z\"\n          \"updated_at\" => \"2025-06-16T14:45:02.000000Z\"\n        ]\n      ]\n      \"slugs\" => array:3 [\n        \"en\" => \"stadium-lenore-waelchi-5\"\n        \"it\" => \"stadium-angelina-prosacco-5\"\n        \"es\" => \"stadium-fatima-dietrich-i-5\"\n      ]\n      \"image_alt\" => \"stadium image\"\n      \"image\" => array:1 [\n        \"4022ea87-a198-447b-8df2-7a289b050666\" => \"4022ea87-a198-447b-8df2-7a289b050666\"\n      ]\n    ]\n    \"previousUrl\" => \"http://admin.ticketgol.test/stadiums?tableSearch=Lenore+Waelchi\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => \"0\"\n    \"record\" => App\\Models\\Stadium {#2874\n      #connection: \"mysql\"\n      #table: \"stadiums\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 5\n        \"address_line_1\" => \"\"\"\n          336 Aubrey Lane\\n\n          West Elena, CA 03986\n          \"\"\"\n        \"address_line_2\" => \"\"\"\n          901 Jaskolski Run Apt. 892\\n\n          Leannonberg, AK 78099-0697\n          \"\"\"\n        \"postcode\" => \"37607-8413\"\n        \"country_id\" => 211\n        \"is_published\" => true\n        \"created_at\" => \"2025-06-16 14:45:01\"\n        \"updated_at\" => \"2025-07-01 10:05:10\"\n        \"deleted_at\" => null\n      ]\n      #original: array:9 [\n        \"id\" => 5\n        \"address_line_1\" => \"\"\"\n          336 Aubrey Lane\\n\n          West Elena, CA 03986\n          \"\"\"\n        \"address_line_2\" => \"\"\"\n          901 Jaskolski Run Apt. 892\\n\n          Leannonberg, AK 78099-0697\n          \"\"\"\n        \"postcode\" => \"37607-8413\"\n        \"country_id\" => 211\n        \"is_published\" => true\n        \"created_at\" => \"2025-06-16 14:45:01\"\n        \"updated_at\" => \"2025-07-01 10:05:10\"\n        \"deleted_at\" => null\n      ]\n      #changes: array:2 [\n        \"is_published\" => true\n        \"updated_at\" => \"2025-07-01 10:05:10\"\n      ]\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#3925\n          #items: array:1 [\n            0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#3910\n              #connection: \"mysql\"\n              #table: \"media\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:18 [\n                \"id\" => 3\n                \"model_type\" => \"App\\Models\\Stadium\"\n                \"model_id\" => 5\n                \"uuid\" => \"4022ea87-a198-447b-8df2-7a289b050666\"\n                \"collection_name\" => \"default\"\n                \"name\" => \"localhost_5174_ (2)\"\n                \"file_name\" => \"01JZ2Q5537MJ1PCM0QRV4HRA9A.png\"\n                \"mime_type\" => \"image/png\"\n                \"disk\" => \"admin\"\n                \"conversions_disk\" => \"admin\"\n                \"size\" => 337645\n                \"manipulations\" => \"[]\"\n                \"custom_properties\" => \"{\"alt\":\"stadium image\"}\"\n                \"generated_conversions\" => \"[]\"\n                \"responsive_images\" => \"[]\"\n                \"order_column\" => 1\n                \"created_at\" => \"2025-07-01 10:05:10\"\n                \"updated_at\" => \"2025-07-01 10:05:10\"\n              ]\n              #original: array:18 [\n                \"id\" => 3\n                \"model_type\" => \"App\\Models\\Stadium\"\n                \"model_id\" => 5\n                \"uuid\" => \"4022ea87-a198-447b-8df2-7a289b050666\"\n                \"collection_name\" => \"default\"\n                \"name\" => \"localhost_5174_ (2)\"\n                \"file_name\" => \"01JZ2Q5537MJ1PCM0QRV4HRA9A.png\"\n                \"mime_type\" => \"image/png\"\n                \"disk\" => \"admin\"\n                \"conversions_disk\" => \"admin\"\n                \"size\" => 337645\n                \"manipulations\" => \"[]\"\n                \"custom_properties\" => \"{\"alt\":\"stadium image\"}\"\n                \"generated_conversions\" => \"[]\"\n                \"responsive_images\" => \"[]\"\n                \"order_column\" => 1\n                \"created_at\" => \"2025-07-01 10:05:10\"\n                \"updated_at\" => \"2025-07-01 10:05:10\"\n              ]\n              #changes: []\n              #casts: array:4 [\n                \"manipulations\" => \"array\"\n                \"custom_properties\" => \"array\"\n                \"generated_conversions\" => \"array\"\n                \"responsive_images\" => \"array\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: array:2 [\n                0 => \"original_url\"\n                1 => \"preview_url\"\n              ]\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: []\n              #streamChunkSize: 1048576\n            }\n          ]\n          #escapeWhenCastingToString: false\n          +collectionName: null\n          +formFieldName: null\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #oldAttributes: []\n      #activitylogOptions: Spatie\\Activitylog\\LogOptions {#3924\n        +logName: null\n        +submitEmptyLogs: false\n        +logFillable: true\n        +logOnlyDirty: true\n        +logUnguarded: false\n        +logAttributes: []\n        +logExceptAttributes: []\n        +dontLogIfAttributesChangedOnly: []\n        +attributeRawValues: []\n        +descriptionForEvent: Closure(string $eventName) {#3923\n          class: \"App\\Models\\Stadium\"\n          this: App\\Models\\Stadium {#2874}\n          file: \"E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\CustomActivityLog.php\"\n          line: \"16 to 16\"\n        }\n      }\n      +enableLoggingModelsEvents: true\n      +mediaConversions: []\n      +mediaCollections: []\n      #deletePreservingMedia: false\n      #unAttachedMediaLibraryItems: []\n      #forceDeleting: false\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.stadium-resource.pages.edit-stadium\"\n  \"component\" => \"App\\Filament\\Resources\\StadiumResource\\Pages\\EditStadium\"\n  \"id\" => \"QTH9dkrCn37U4CFTbuXI\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => update_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1386209966 data-indent-pad=\"  \"><span class=sf-dump-note>update_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386209966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.063666, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Stadium(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Stadium)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-390745157 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Stadium(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Stadium(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Stadium)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390745157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.064382, "xdebug_link": null}]}, "session": {"_token": "S1wPFbSa6P6x1xy4Q8HqXIaj2lSEwhnXDIkxPf5V", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://admin.ticketgol.test/stadiums/5/edit\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2", "filament": "array:1 [\n  \"notifications\" => array:1 [\n    0 => array:11 [\n      \"id\" => \"9f4920c2-19b0-470a-b310-ea62b36bff03\"\n      \"actions\" => []\n      \"body\" => null\n      \"color\" => null\n      \"duration\" => 6000\n      \"icon\" => \"heroicon-o-check-circle\"\n      \"iconColor\" => \"success\"\n      \"status\" => \"success\"\n      \"title\" => \"Saved\"\n      \"view\" => \"filament-notifications::notification\"\n      \"viewData\" => []\n    ]\n  ]\n]"}, "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f4920c2-6607-4ffd-889d-d4813f4f647b\" target=\"_blank\">View in Telescope</a>", "path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1663629514 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1663629514\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-781735087 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781735087\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-981614041 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S1wPFbSa6P6x1xy4Q8HqXIaj2lSEwhnXDIkxPf5V</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"3558 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;id&quot;:5,&quot;address_line_1&quot;:&quot;336 Aubrey Lane\\nWest Elena, CA 03986&quot;,&quot;address_line_2&quot;:&quot;901 Jaskolski Run Apt. 892\\nLeannonberg, AK 78099-0697&quot;,&quot;postcode&quot;:&quot;37607-8413&quot;,&quot;country_id&quot;:211,&quot;is_published&quot;:true,&quot;created_at&quot;:&quot;2025-06-16T14:45:01.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-16T14:45:01.000000Z&quot;,&quot;deleted_at&quot;:null,&quot;events_count&quot;:2,&quot;clubs_count&quot;:1,&quot;translations&quot;:[[[{&quot;id&quot;:13,&quot;stadium_id&quot;:5,&quot;locale&quot;:&quot;en&quot;,&quot;name&quot;:&quot;Lenore Waelchi&quot;,&quot;description&quot;:&quot;Rerum est voluptatem commodi quod ipsa. Illum est est natus excepturi laborum sed occaecati. Excepturi laudantium occaecati laudantium molestiae. Facilis quisquam veritatis quibusdam totam hic aut cumque.&quot;,&quot;meta_title&quot;:&quot;Voluptatibus fugiat inventore quo similique earum error.&quot;,&quot;meta_description&quot;:&quot;Minus voluptas corrupti maxime tenetur eius dolore.&quot;,&quot;meta_keywords&quot;:&quot;Sed dignissimos veritatis nobis.&quot;,&quot;created_at&quot;:&quot;2025-06-16T14:45:02.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-16T14:45:02.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:15,&quot;stadium_id&quot;:5,&quot;locale&quot;:&quot;es&quot;,&quot;name&quot;:&quot;Fatima Dietrich I&quot;,&quot;description&quot;:&quot;Excepturi quod saepe placeat quod eius nulla et. Velit corrupti in voluptate voluptatum. Numquam ut quae nostrum non rerum consequatur. Pariatur nemo voluptatem et reiciendis animi consequatur.&quot;,&quot;meta_title&quot;:&quot;Culpa perferendis repellendus vitae non provident.&quot;,&quot;meta_description&quot;:&quot;Accusamus voluptatem omnis quasi dicta rerum omnis voluptatem.&quot;,&quot;meta_keywords&quot;:&quot;Quae quisquam reiciendis iusto.&quot;,&quot;created_at&quot;:&quot;2025-06-16T14:45:02.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-16T14:45:02.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:14,&quot;stadium_id&quot;:5,&quot;locale&quot;:&quot;it&quot;,&quot;name&quot;:&quot;Angelina Prosacco&quot;,&quot;description&quot;:&quot;Aliquid maiores voluptatem saepe debitis aut. Dolorum a temporibus quo quos asperiores quis. Eligendi repellat ab consequuntur aut voluptatum possimus aperiam.&quot;,&quot;meta_title&quot;:&quot;Ad quis ex qui et dolor qui nemo.&quot;,&quot;meta_description&quot;:&quot;Quia similique qui deserunt.&quot;,&quot;meta_keywords&quot;:&quot;Sunt odit tempora dolores laborum impedit.&quot;,&quot;created_at&quot;:&quot;2025-06-16T14:45:02.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-16T14:45:02.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;slugs&quot;:[{&quot;en&quot;:&quot;stadium-lenore-waelchi-5&quot;,&quot;it&quot;:&quot;stadium-angelina-prosacco-5&quot;,&quot;es&quot;:&quot;stadium-fatima-dietrich-i-5&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_alt&quot;:null,&quot;image&quot;:[{&quot;48e67f7b-2cf8-4744-99c4-f38a0b4f73c8&quot;:[&quot;livewire-file:ffwbwksUSExPBayXnN6U1vdU9wqFbW-metabG9jYWxob3N0XzUxNzRfICgyKS5wbmc=-.png&quot;,{&quot;s&quot;:&quot;fil&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/admin.ticketgol.test\\/stadiums?tableSearch=Lenore+Waelchi&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeRelationManager&quot;:&quot;0&quot;,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Stadium&quot;,&quot;key&quot;:5,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;QTH9dkrCn37U4CFTbuXI&quot;,&quot;name&quot;:&quot;app.filament.resources.stadium-resource.pages.edit-stadium&quot;,&quot;path&quot;:&quot;stadiums\\/5\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;App\\\\Filament\\\\Resources\\\\StadiumResource\\\\RelationManagers\\\\SectorsRelationManager&quot;:[&quot;div&quot;,&quot;lYN4d2fXAfOmOhJwuaVF&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;37578677c9e7cdb59f5571e476ca361d1ec19d836e4f23e7822ea46e77e73217&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.image_alt</span>\" => \"<span class=sf-dump-str title=\"13 characters\">stadium image</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981614041\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-264708648 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1147 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6IkFDREpnRVN6eG1ia3ZnMG9OUXBBRVE9PSIsInZhbHVlIjoiOWR3MXFtdDdOYUdJYXM5OURmZkt4aVJ0a25DajdUZ2tEVHJIK2lIL0lEcVBQaEJHS0dzZk1NdDlycUExNnpoaU5XUUUzcHJYcWkrWFo5azljd1Z5YVRPb2JiS0txL2dBWERjV0JsQmNJaThFNndIMjRNdjExSm15N1F0UTYxYXAiLCJtYWMiOiI5ZDJkNjFkNjlkMDM0NTZiODQ1YmM4Zjc3OWZiZjYwNzUxYmQwYmM1OTFhZjYxNDJjMTc2NzBiZDAzMjk4NTA0IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkxxaWVYSDdYUnlKdk5rUVJHUTRLcUE9PSIsInZhbHVlIjoiTFpGOUtqZ1k5aFI3OVlhcXZlTHNCNzE4UTRrNGIxSWNxWmN6WVRtYXJENFllc2JPNUVEUGZNWGp0Znd1dTFRbkJFM0pHTCt5ME1lc0F1NVZ6M2VtamhrTDUwTHVHRm1adFhQMFVuWktQYW94WlI3SUJnVjZla3JVTko2bk9sMm4iLCJtYWMiOiIxOWJjMTc0M2U5NWI0OTc2Y2E5MzNlZGMwY2I3NWY3YTYyZGM5ZTNmZTlkMzIwYzhhMWE1ODRhNTNiYmM4NDEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://admin.ticketgol.test/stadiums/5/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://admin.ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4088</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264708648\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-831025894 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S1wPFbSa6P6x1xy4Q8HqXIaj2lSEwhnXDIkxPf5V</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtjjOpilFuaemOVbFsrdjHugYxu9NJviG6B3aeou</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831025894\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-80725164 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 10:05:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>vary</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">X-Inertia</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"97 characters\">selected_locale=en; expires=Wed, 01 Jul 2026 10:05:10 GMT; Max-Age=31536000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9YYmpjc09TTUV6R3J1ODdhWEF3Rmc9PSIsInZhbHVlIjoiSDd1OW84bGNxV1oraGJVMm5TK2JLZ3ZLU0w5dWtWVkgyclZYRHpCWmVlcVEyUzIyckpnM2VjQzdGVXM3YkplREFpdWhUbzE1cHRqcjZiblZ6VDF4S3NhUTdnSHhUNy84RDIxSG1jd1k4c0JDS1R1TDFzcHpKbHIyRVBGL2p6YlUiLCJtYWMiOiI5OTAwMDQyNjgwYzExNWQ0YmY1MzM1NGM5ODMzZTg2ZjU3ZTAyMDg3N2NmNmIwMDcyODg5YjEwMGU0YTM2M2ZhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 12:05:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IloxbDBmTTZ5L3o1VzMxcHhLSWVjTHc9PSIsInZhbHVlIjoidG55YURxc2RUT3hXR29mT21hK0ZnZk9JcW9Xa1hLWGtkZlhlWGljRWllbFdWL25vYmczdncwUHVFaTdqeWdKNHJ4MFE0MDhpeFFnNi9OcjUvMVltaW9LekhlQUdJSjF1UVVmN3V0c01YM3E5M1F4R2VnZkpPSUNiYTR2bnZIdisiLCJtYWMiOiJhNDI3MzYxZTRhMGU4YmIzZTQ1YzMyNzllM2UxYzA0MjljZTJhMTVkMzQzOWJiYWE0MDljNWU5MTFhNDEwN2UxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 12:05:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">selected_locale=en; expires=Wed, 01-Jul-2026 10:05:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9YYmpjc09TTUV6R3J1ODdhWEF3Rmc9PSIsInZhbHVlIjoiSDd1OW84bGNxV1oraGJVMm5TK2JLZ3ZLU0w5dWtWVkgyclZYRHpCWmVlcVEyUzIyckpnM2VjQzdGVXM3YkplREFpdWhUbzE1cHRqcjZiblZ6VDF4S3NhUTdnSHhUNy84RDIxSG1jd1k4c0JDS1R1TDFzcHpKbHIyRVBGL2p6YlUiLCJtYWMiOiI5OTAwMDQyNjgwYzExNWQ0YmY1MzM1NGM5ODMzZTg2ZjU3ZTAyMDg3N2NmNmIwMDcyODg5YjEwMGU0YTM2M2ZhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 12:05:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IloxbDBmTTZ5L3o1VzMxcHhLSWVjTHc9PSIsInZhbHVlIjoidG55YURxc2RUT3hXR29mT21hK0ZnZk9JcW9Xa1hLWGtkZlhlWGljRWllbFdWL25vYmczdncwUHVFaTdqeWdKNHJ4MFE0MDhpeFFnNi9OcjUvMVltaW9LekhlQUdJSjF1UVVmN3V0c01YM3E5M1F4R2VnZkpPSUNiYTR2bnZIdisiLCJtYWMiOiJhNDI3MzYxZTRhMGU4YmIzZTQ1YzMyNzllM2UxYzA0MjljZTJhMTVkMzQzOWJiYWE0MDljNWU5MTFhNDEwN2UxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 12:05:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80725164\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1545035533 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S1wPFbSa6P6x1xy4Q8HqXIaj2lSEwhnXDIkxPf5V</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://admin.ticketgol.test/stadiums/5/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f4920c2-19b0-470a-b310-ea62b36bff03</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Saved</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545035533\", {\"maxDepth\":0})</script>\n"}}