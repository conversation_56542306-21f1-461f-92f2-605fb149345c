{"__meta": {"id": "X7f108aa912b4bdf7ebbe208da615d22e", "datetime": "2025-07-01 09:16:01", "utime": **********.95644, "method": "GET", "uri": "/api/v1/reservation/detail/eyJpdiI6IjFnazhPdDY3S2p1ejNpK1prSUszZmc9PSIsInZhbHVlIjoiS2h0MUI0MnEwUFdLWVd6WjFKQVpIZz09IiwibWFjIjoiOGZkYmIxMmRlNDIxZTc2MzM0ZTRjMGNiMDFhNzQ5NjA0YmJiMmY4YjMzZTI1NmMxODQxMjQ3YTFjOGU3MWJkMyIsInRhZyI6IiJ9", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.739724, "end": **********.956454, "duration": 0.21673011779785156, "duration_str": "217ms", "measures": [{"label": "Booting", "start": **********.739724, "relative_start": 0, "end": **********.834677, "relative_end": **********.834677, "duration": 0.09495306015014648, "duration_str": "94.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.834693, "relative_start": 0.09496903419494629, "end": **********.956457, "relative_end": 2.86102294921875e-06, "duration": 0.12176394462585449, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 7227528, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/reservation/detail/{reservationId}", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\TicketReservationController@getPreliminaryReservation", "namespace": null, "prefix": "api/v1/reservation", "where": [], "as": "api.reservations.detail", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FTicketReservationController.php&line=44\" onclick=\"\">app/Http/Controllers/Api/V1/TicketReservationController.php:44-53</a>"}, "queries": {"nb_statements": 24, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.032409999999999994, "accumulated_duration_str": "32.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs' limit 1", "type": "query", "params": [], "bindings": ["huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.839932, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 2.777}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.844489, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 2.777, "width_percent": 2.561}, {"sql": "select `id`, `ticket_id`, `user_id`, `quantity`, `price`, `status`, `expires_at` from `ticket_reservations` where `ticket_reservations`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 20, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.848732, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 5.338, "width_percent": 2.962}, {"sql": "select `id`, `ticket_type`, `event_id`, `sector_id`, `currency_code`, `quantity` from `tickets` where `tickets`.`id` in (20) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 24, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 25, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.8524692, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 8.3, "width_percent": 7.868}, {"sql": "select `id`, `date`, `time`, `timezone`, `stadium_id` from `events` where `events`.`id` in (7) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 29, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.85711, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 16.168, "width_percent": 2.036}, {"sql": "select `event_id`, `name`, `locale` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.860305, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 18.204, "width_percent": 2.654}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (5) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.862887, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 20.858, "width_percent": 2.252}, {"sql": "select `stadium_id`, `name`, `locale` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.867099, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 23.11, "width_percent": 5.955}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.8709419, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 29.065, "width_percent": 2.438}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 43, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 44, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 45, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 46, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 47, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.8734488, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 43, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 31.503, "width_percent": 1.728}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.876038, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 33.23, "width_percent": 3.548}, {"sql": "select `restrictions`.`id`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (7) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 33, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 34, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 35, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.8795261, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 36.779, "width_percent": 5.893}, {"sql": "select `restriction_id`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (6, 7, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 37, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 38, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 39, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 40, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 41, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.884405, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 37, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 42.672, "width_percent": 2.191}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.886759, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 44.863, "width_percent": 1.728}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (45) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 29, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.889241, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 46.591, "width_percent": 2.16}, {"sql": "select `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (45)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.8919342, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 48.75, "width_percent": 1.944}, {"sql": "select `restrictions`.`id`, `ticket_restrictions`.`ticket_id` as `pivot_ticket_id`, `ticket_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `ticket_restrictions` on `restrictions`.`id` = `ticket_restrictions`.`restriction_id` where `ticket_restrictions`.`ticket_id` in (20) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 28, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 29, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.8950372, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 27, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 50.694, "width_percent": 4.165}, {"sql": "select `restriction_id`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (3, 4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 33, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 34, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 35, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.898103, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 32, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 54.86, "width_percent": 2.438}, {"sql": "select `id`, `name`, `email`, `user_type` from `users` where `users`.`id` in (7) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 24, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 25, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.900535, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 57.297, "width_percent": 1.975}, {"sql": "select `user_id`, `address`, `city`, `phone`, `zip`, `country_id` from `user_details` where `user_details`.`user_id` in (7) and `user_details`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 29, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.903558, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 28, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 59.272, "width_percent": 3.703}, {"sql": "select `id`, `shortcode` from `countries` where `countries`.`id` in (192) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 34, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 35, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 36, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.906837, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 33, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 62.974, "width_percent": 1.882}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (192)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 39, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 40, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 42, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.909451, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 38, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 64.857, "width_percent": 2.715}, {"sql": "select `id`, `ticket_reservation_id` from `orders` where `orders`.`ticket_reservation_id` in (28) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, {"index": 24, "namespace": null, "name": "app/Repositories/TicketReservationRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\TicketReservationRepository.php", "line": 108}, {"index": 25, "namespace": null, "name": "app/Services/TicketReservationService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\TicketReservationService.php", "line": 38}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V1/TicketReservationController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\TicketReservationController.php", "line": 47}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}], "start": **********.912326, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:77", "source": {"index": 23, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\BaseRepository.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FBaseRepository.php&line=77", "ajax": false, "filename": "BaseRepository.php", "line": "77"}, "connection": "ticketgol", "explain": null, "start_percent": 67.572, "width_percent": 2.746}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiY1M3VTZPRDRMazBKbnhQbjEyaUtkSGg2cXRKZWZXOVJIOFBqSTBaRiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MjoiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzLzI0Ijt9fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.945715, "duration": 0.00962, "duration_str": "9.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 70.318, "width_percent": 29.682}]}, "models": {"data": {"App\\Models\\Restriction": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Country": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\TicketReservation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicketReservation.php&line=1", "ajax": false, "filename": "TicketReservation.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\UserDetail": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUserDetail.php&line=1", "ajax": false, "filename": "UserDetail.php", "line": "?"}}}, "count": 27, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders/24\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f490f2f-6f55-4a7c-9784-1bd6fe72f79b\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/reservation/detail/eyJpdiI6IjFnazhPdDY3S2p1ejNpK1prSUszZmc9PSIsInZhbHVlIjoiS2h0MUI0MnEwUFdLWVd6WjFKQVpIZz09IiwibWFjIjoiOGZkYmIxMmRlNDIxZTc2MzM0ZTRjMGNiMDFhNzQ5NjA0YmJiMmY4YjMzZTI1NmMxODQxMjQ3YTFjOGU3MWJkMyIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1380262612 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"853 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6IjlrM3AreVpVaHl5MmpyRkZITXFET3c9PSIsInZhbHVlIjoiR3Y3dWdiZEV5cVNieFd5TE9IeGtTVmhCSE9rV3VtcWhUYXBBdDd2YTFNWXJIOHZmcUNPOVlYNm9EMGF1OWJQazZoRTdER1BtejdpWVlYdVllUmhiSDcvUUVYa1ZnOTJsRjliek0rT1UxaDRmZTREYzVwK0I0RHZmWkdzalJ5MHMiLCJtYWMiOiJkMTM1NjQ1MTgyZDAyODk2N2ExZDdiNzg0OGYzNzhmYzY3M2Y2MmNhN2M4MTM1ZWJhNjg1NWQ0NDljOTQzYmFiIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6ImVmOVEydERkaDlSOHdNendDT2JpMlE9PSIsInZhbHVlIjoiTGxia0hVRDRHY1YyTG1IaHNVSmFhSjZuMk4rbXdaQmxxM1oyQ1psRXFieWRBSm1Mbzh6ZWhoNzRrbk8vamxxenQ0SEhyeUNBSkNnbEJZUUc2cUNIaXc1bmErT0JKdmYwdEh3ZnRJM0JSMHk4Yk41VDBzUjE3bXhBamZaWVdQZ0kiLCJtYWMiOiJjZWMyNDQ4MjBmMGY0NjI2NmVhN2U4MjFhNDQ1YjZkNzEyMDgyYTY4MzA5OGRlYmFmYzAyYmVkNWZmZjMwYTFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"238 characters\">http://ticketgol.test/ticket/checkout/eyJpdiI6IjFnazhPdDY3S2p1ejNpK1prSUszZmc9PSIsInZhbHVlIjoiS2h0MUI0MnEwUFdLWVd6WjFKQVpIZz09IiwibWFjIjoiOGZkYmIxMmRlNDIxZTc2MzM0ZTRjMGNiMDFhNzQ5NjA0YmJiMmY4YjMzZTI1NmMxODQxMjQ3YTFjOGU3MWJkMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjlrM3AreVpVaHl5MmpyRkZITXFET3c9PSIsInZhbHVlIjoiR3Y3dWdiZEV5cVNieFd5TE9IeGtTVmhCSE9rV3VtcWhUYXBBdDd2YTFNWXJIOHZmcUNPOVlYNm9EMGF1OWJQazZoRTdER1BtejdpWVlYdVllUmhiSDcvUUVYa1ZnOTJsRjliek0rT1UxaDRmZTREYzVwK0I0RHZmWkdzalJ5MHMiLCJtYWMiOiJkMTM1NjQ1MTgyZDAyODk2N2ExZDdiNzg0OGYzNzhmYzY3M2Y2MmNhN2M4MTM1ZWJhNjg1NWQ0NDljOTQzYmFiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380262612\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-632345804 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">huo08DJbq6VOlKRcDAk3pSojRxjPQB7domg8SuYs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632345804\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1949414537 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:16:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjRGaWRxUkZHcTFoMHFYT0lQdmo5a3c9PSIsInZhbHVlIjoiQjFWSm9BTTNVbFJacEtFSzJOZUhNUWVrNDV5L3F6RXZnWDRmVjg3cHFMUXVDSUw3NjlFSGF5TzRraENLM2FUSVM2TTB2NEV3OGQzWWZHeFRaNVRMblRyY3QrbURxV05HQng2Y0ZMYUxFLzh4S0FrcENPYmxzMFM4bFRhbEpleXQiLCJtYWMiOiI1OGIwZTY2ZGE5YTc4ZDE1OGZhMzhkODEzNTdkYzgzZjk0YTQwOGRlYTFiNDliMDk4OTQyOWNiMmMxZWY1MjVmIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:16:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6InNvZjFnTnd0dldkQm0zWTkxbnZoM1E9PSIsInZhbHVlIjoiYWF6OUYrRCtmVlFHRTNnQ1pRNXVOZmpOL0J1cE5HMHFGWWg0QW5mR05Da0tXU1I5TXZYeENGb2Q0K1B3NHUrQXVnUVEwWVg5bnp3cndrb2hnamNNWk9nL3pUbW5pTnNRT1VJVCtsRVl2QkdFTUV0M1orSHFKd2pMNGREMG03b0MiLCJtYWMiOiIxMjYwNjg4NTFjNTU0NmNhMDllNmJkZmExMjJiZDMwYmZjZThiN2Q1NjgzOWNlNWJjYmNkYzVjZTg0ZjQ5MTRkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:16:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjRGaWRxUkZHcTFoMHFYT0lQdmo5a3c9PSIsInZhbHVlIjoiQjFWSm9BTTNVbFJacEtFSzJOZUhNUWVrNDV5L3F6RXZnWDRmVjg3cHFMUXVDSUw3NjlFSGF5TzRraENLM2FUSVM2TTB2NEV3OGQzWWZHeFRaNVRMblRyY3QrbURxV05HQng2Y0ZMYUxFLzh4S0FrcENPYmxzMFM4bFRhbEpleXQiLCJtYWMiOiI1OGIwZTY2ZGE5YTc4ZDE1OGZhMzhkODEzNTdkYzgzZjk0YTQwOGRlYTFiNDliMDk4OTQyOWNiMmMxZWY1MjVmIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:16:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6InNvZjFnTnd0dldkQm0zWTkxbnZoM1E9PSIsInZhbHVlIjoiYWF6OUYrRCtmVlFHRTNnQ1pRNXVOZmpOL0J1cE5HMHFGWWg0QW5mR05Da0tXU1I5TXZYeENGb2Q0K1B3NHUrQXVnUVEwWVg5bnp3cndrb2hnamNNWk9nL3pUbW5pTnNRT1VJVCtsRVl2QkdFTUV0M1orSHFKd2pMNGREMG03b0MiLCJtYWMiOiIxMjYwNjg4NTFjNTU0NmNhMDllNmJkZmExMjJiZDMwYmZjZThiN2Q1NjgzOWNlNWJjYmNkYzVjZTg0ZjQ5MTRkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:16:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949414537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cS7U6OD4Lk0JnxPn12iKdHh6qtJefW9RH8PjI0ZF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/24</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}