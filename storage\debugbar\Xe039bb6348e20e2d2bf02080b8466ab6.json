{"__meta": {"id": "Xe039bb6348e20e2d2bf02080b8466ab6", "datetime": "2025-07-01 07:03:41", "utime": 1751353421.788253, "method": "GET", "uri": "/events", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.781187, "end": 1751353421.788269, "duration": 1.0070819854736328, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": **********.781187, "relative_start": 0, "end": **********.842266, "relative_end": **********.842266, "duration": 0.06107902526855469, "duration_str": "61.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.842277, "relative_start": 0.06108999252319336, "end": 1751353421.788271, "relative_end": 1.9073486328125e-06, "duration": 0.9459939002990723, "duration_str": "946ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 17454400, "peak_usage_str": "17MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 34, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.907874, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.913957, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.921596, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.93235, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.93798, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.9424, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.946466, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.022037, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.026166, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.072369, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.079773, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.127097, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.132651, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.183756, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.186403, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.237183, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.243201, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.289532, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.292062, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.336186, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.339207, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.389419, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.39691, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.438719, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.441804, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.494426, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751353421.501159, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": 1751353421.505386, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1751353421.520459, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1751353421.537212, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::f07f1a332c895be3dafc362336ba959c", "param_count": null, "params": [], "start": 1751353421.61785, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/f07f1a332c895be3dafc362336ba959c.blade.php__components::f07f1a332c895be3dafc362336ba959c", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Ff07f1a332c895be3dafc362336ba959c.blade.php&line=1", "ajax": false, "filename": "f07f1a332c895be3dafc362336ba959c.blade.php", "line": "?"}}, {"name": "filament-language-switch::language-switch", "param_count": null, "params": [], "start": 1751353421.618963, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/language-switch.blade.phpfilament-language-switch::language-switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Flanguage-switch.blade.php&line=1", "ajax": false, "filename": "language-switch.blade.php", "line": "?"}}, {"name": "filament-language-switch::switch", "param_count": null, "params": [], "start": 1751353421.61988, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/switch.blade.phpfilament-language-switch::switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": 1751353421.78345, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}]}, "route": {"uri": "GET events", "domain": "admin.ticketgol.test", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents@__invoke", "as": "filament.admin.resources.events.index", "namespace": null, "prefix": "/events", "where": [], "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 41, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.030109999999999998, "accumulated_duration_str": "30.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7' limit 1", "type": "query", "params": [], "bindings": ["zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.845387, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 2.325}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.847988, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 2.325, "width_percent": 2.591}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.85387, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "ticketgol", "explain": null, "start_percent": 4.915, "width_percent": 2.424}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.858249, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "ticketgol", "explain": null, "start_percent": 7.34, "width_percent": 2.624}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.86117, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "ticketgol", "explain": null, "start_percent": 9.963, "width_percent": 2.79}, {"sql": "select count(*) as aggregate from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": ["en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.885525, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "ticketgol", "explain": null, "start_percent": 12.753, "width_percent": 3.52}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.888149, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 16.274, "width_percent": 4.716}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.89156, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 20.99, "width_percent": 2.757}, {"sql": "select `events`.*, `et`.`name` as `event_name`, `lt`.`name` as `league_name`, `ct`.`name` as `country_name`, `st`.`name` as `stadium_name`, `hct`.`name` as `home_club_name`, `gct`.`name` as `guest_club_name`, (select count(*) from `tickets` where `events`.`id` = `tickets`.`event_id` and `is_active` = 1 and `tickets`.`deleted_at` is null) as `tickets_count` from `events` left join `event_translations` as `et` on `events`.`id` = `et`.`event_id` and `et`.`locale` = 'en' left join `league_translations` as `lt` on `events`.`league_id` = `lt`.`league_id` and `lt`.`locale` = 'en' left join `country_translations` as `ct` on `events`.`country_id` = `ct`.`country_id` and `ct`.`locale` = 'en' left join `stadium_translations` as `st` on `events`.`stadium_id` = `st`.`stadium_id` and `st`.`locale` = 'en' left join `club_translations` as `hct` on `events`.`home_club_id` = `hct`.`club_id` and `hct`.`locale` = 'en' left join `club_translations` as `gct` on `events`.`guest_club_id` = `gct`.`club_id` and `gct`.`locale` = 'en' where (`events`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, "en", "en", "en", "en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 17, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.897019, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 23.746, "width_percent": 4.949}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 181}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 68}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.90059, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:326", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=326", "ajax": false, "filename": "HasBulkActions.php", "line": "326"}, "connection": "ticketgol", "explain": null, "start_percent": 28.695, "width_percent": 3.022}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 07:03:41' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5432172, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 31.717, "width_percent": 2.723}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 07:03:41' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.549964, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 34.44, "width_percent": 3.055}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 07:03:41' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5554218, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 37.496, "width_percent": 2.524}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 07:03:41' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.56013, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 40.02, "width_percent": 2.923}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 07:03:41' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5653498, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 42.943, "width_percent": 3.122}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 07:03:41' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5708768, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 46.064, "width_percent": 1.86}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 07:03:41' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5752232, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 47.924, "width_percent": 2.69}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 07:03:41' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.57987, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 50.614, "width_percent": 1.959}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 07:03:41' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.583738, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 52.574, "width_percent": 1.661}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 07:03:41' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.587663, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 54.234, "width_percent": 1.627}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5927699, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 55.862, "width_percent": 1.926}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 07:03:41' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.5976138, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 57.788, "width_percent": 2.026}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 07:03:41' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.6016028, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 59.814, "width_percent": 1.428}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 07:03:41' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.605664, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 61.242, "width_percent": 1.993}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 07:03:41' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.6103559, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 63.235, "width_percent": 2.458}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 07:03:41' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.636415, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 65.692, "width_percent": 1.727}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-30 07:03:41' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.640141, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 67.419, "width_percent": 1.793}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-30 07:03:41' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.643988, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 69.213, "width_percent": 1.993}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-30 07:03:41' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.648346, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 71.206, "width_percent": 2.391}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-30 07:03:41' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.6525319, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 73.597, "width_percent": 1.86}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-30 07:03:41' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.659051, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 75.457, "width_percent": 1.727}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-30 07:03:41' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.6646, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 77.184, "width_percent": 1.959}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-30 07:03:41' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.6701791, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 79.143, "width_percent": 1.561}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-30 07:03:41' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.677122, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 80.704, "width_percent": 2.723}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-30 07:03:41' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.684625, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 83.427, "width_percent": 3.155}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.691331, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 86.583, "width_percent": 1.428}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-30 07:03:41' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.695718, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 88.011, "width_percent": 2.292}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-30 07:03:41' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.700914, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 90.302, "width_percent": 2.624}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-30 07:03:41' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.705676, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 92.926, "width_percent": 2.325}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-30 07:03:41' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-30 07:03:41"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751353421.711618, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 95.251, "width_percent": 2.458}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9', `last_activity` = 1751353421, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoidThGMjNoTExleHdxUVFuTWFjb2lvMWpwYVI1RWNpdDk2NGQ1bFQ4aiI7czozOiJ1cmwiO2E6MDp7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM0OiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvZXZlbnRzIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEwxRkEzRS9aaURzTlRlTlNQOGNtZS5LdkNyZmRpVVcudjVRcDljaGx5YU1kelY0RHRPZkIyIjt9", 1751353421, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": 1751353421.7865741, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 97.708, "width_percent": 2.292}]}, "models": {"data": {"App\\Models\\Event": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\Slug": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 42, "is_counter": true}, "livewire": {"data": {"app.filament.resources.event-resource.pages.manage-events #F3LNjnjbb3jRsjMWjiSJ": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:2 [\n      \"created_at\" => false\n      \"updated_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.event-resource.pages.manage-events\"\n  \"component\" => \"App\\Filament\\Resources\\EventResource\\Pages\\ManageEvents\"\n  \"id\" => \"F3LNjnjbb3jRsjMWjiSJ\"\n]", "filament-language-switch #CX8e0t41Eje33l7H9x3k": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"CX8e0t41Eje33l7H9x3k\"\n]", "filament.livewire.notifications #JxBFevpZpiqfM1ywF6t8": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#9156\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"JxBFevpZpiqfM1ywF6t8\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 172, "messages": [{"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2034980955 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034980955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.86349, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-818797391 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818797391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.863912, "xdebug_link": null}, {"message": "[\n  ability => create_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1657087535 data-indent-pad=\"  \"><span class=sf-dump-note>create_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">create_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657087535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865253, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-210942338 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210942338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865658, "xdebug_link": null}, {"message": "[\n  ability => reorder_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-499976669 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reorder_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499976669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867776, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1161917700 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161917700\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868181, "xdebug_link": null}, {"message": "[\n  ability => delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-409638112 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409638112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870969, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1982735063 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982735063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87139, "xdebug_link": null}, {"message": "[\n  ability => force_delete_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1284224103 data-indent-pad=\"  \"><span class=sf-dump-note>force_delete_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">force_delete_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284224103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872516, "xdebug_link": null}, {"message": "[\n  ability => forceDeleteAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1238505219 data-indent-pad=\"  \"><span class=sf-dump-note>forceDeleteAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">forceDeleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238505219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872861, "xdebug_link": null}, {"message": "[\n  ability => restore_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-995073603 data-indent-pad=\"  \"><span class=sf-dump-note>restore_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">restore_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995073603\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873895, "xdebug_link": null}, {"message": "[\n  ability => restoreAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1049305650 data-indent-pad=\"  \"><span class=sf-dump-note>restoreAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restoreAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049305650\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.874205, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1833129450 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833129450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.970852, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1067845083 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067845083\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971497, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1595325012 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595325012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.973643, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-151279232 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151279232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.974036, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1061386655 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061386655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.012683, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1189931819 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189931819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.013285, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1709732256 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709732256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.019409, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-170554461 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170554461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.020146, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-572437999 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572437999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.031867, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-160710145 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-160710145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.032255, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1940087953 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940087953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.034609, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1178566949 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178566949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.035091, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1245275004 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245275004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.058903, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-237907855 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237907855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.06013, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2069369745 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069369745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.063984, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-661376140 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-661376140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.064865, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-431216215 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431216215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.070737, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1209633814 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209633814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.071374, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-283935750 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283935750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.078249, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2144899455 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144899455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.078841, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1701472613 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701472613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.087247, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-750726415 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750726415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.087852, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-102939945 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102939945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.090131, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-808523144 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808523144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.090701, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1721110950 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721110950\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.120103, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1149671259 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149671259\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.120635, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-328962386 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328962386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.125346, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-942805918 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942805918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.125939, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1660012954 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660012954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.137403, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-937816730 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937816730\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.137895, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1617274238 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617274238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.140216, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1183373528 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183373528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.140809, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-121627958 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121627958\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.17532, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1282772923 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282772923\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.176176, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2046703355 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046703355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.182365, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1459622528 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459622528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.182923, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-676960331 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676960331\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.191457, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1971695959 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971695959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.191985, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1180175531 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180175531\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.194892, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-79152074 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79152074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.195502, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1992324983 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992324983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.225406, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1019869362 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019869362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.225882, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-686183863 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686183863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.228948, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-116085178 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116085178\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.229612, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-664956854 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664956854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.235495, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-758099540 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-758099540\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.236245, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1164667623 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164667623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.241607, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1017606881 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017606881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.242209, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-690862887 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690862887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.252402, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-529337984 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529337984\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.253045, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-545091090 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545091090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.256633, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-246843844 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246843844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.25712, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-8172449 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8172449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.284564, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1146199050 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146199050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.284971, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1679009958 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679009958\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.288513, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-410452177 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410452177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.288854, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1442613774 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442613774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.298464, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-123658170 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123658170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.298957, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648544770 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648544770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.301681, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1425509712 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425509712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.302113, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-595671090 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595671090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.330648, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-273020012 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273020012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.331179, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1276597000 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276597000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.334942, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1600660851 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600660851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.335381, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-233033123 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233033123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.345598, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-711517596 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711517596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.346638, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1819258859 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819258859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.350103, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1810786678 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810786678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.350873, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1947040855 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947040855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.378981, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1854120107 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854120107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.37971, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-492294404 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492294404\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.38277, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1294640870 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294640870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.383446, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-151430266 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151430266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.387941, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-448951279 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448951279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.388472, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1510889411 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510889411\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.395528, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1553972293 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553972293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.396086, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-806976506 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806976506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.401471, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2135763498 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135763498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.401916, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-395812907 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395812907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.403669, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1191744090 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191744090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.404037, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1234016639 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234016639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.432901, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1117141548 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117141548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.433431, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1352566410 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352566410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.437092, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-367874172 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Event(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367874172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.437716, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-429904960 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429904960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.448191, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-913445424 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913445424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.448797, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-647143831 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647143831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.451567, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-468646894 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468646894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.452354, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1165772562 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165772562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.485793, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1862674235 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862674235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.48628, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-343782552 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343782552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.488548, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-530688315 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530688315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.489031, "xdebug_link": null}, {"message": "[\n  ability => update_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-636118428 data-indent-pad=\"  \"><span class=sf-dump-note>update_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">update_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636118428\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.492523, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-874468637 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874468637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.49302, "xdebug_link": null}, {"message": "[\n  ability => delete_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-347071799 data-indent-pad=\"  \"><span class=sf-dump-note>delete_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347071799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.499451, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Event(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Event)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1027737636 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Event(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Event(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; Object(App\\Models\\Event)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027737636\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.50019, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-703900977 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703900977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.539552, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1138693396 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138693396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.541553, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1342245267 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342245267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.547082, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1979035299 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979035299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.547951, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-490422317 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490422317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.553153, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-422553140 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422553140\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.553804, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1814151894 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814151894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.557941, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-186606120 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186606120\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.558519, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1745608288 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745608288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.562884, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1730529517 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730529517\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.563569, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1023405077 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023405077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.568615, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-416572430 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416572430\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.569231, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1860450675 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860450675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.573242, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-290650907 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290650907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.573745, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1216424978 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216424978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.578354, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-549676621 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549676621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.578803, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1946488689 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946488689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.582193, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1695693626 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695693626\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.582584, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1146956409 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146956409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.586089, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-689045860 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689045860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.586523, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1109254714 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109254714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.590958, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-210622002 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210622002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.591581, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-989751169 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989751169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.595672, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-892000455 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892000455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.596261, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1758851183 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758851183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.600194, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1931284056 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931284056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.600666, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-870539262 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870539262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.603717, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-759366316 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759366316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.604134, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1271066005 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271066005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.60829, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-140312977 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140312977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.608758, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-481173499 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481173499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.613005, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1688365337 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688365337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.613485, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-709440535 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709440535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.633926, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-135903026 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135903026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.634971, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1296244932 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296244932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.638588, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1662338287 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662338287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.639052, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-734373747 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734373747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.642376, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1901256199 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901256199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.642812, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-751104267 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751104267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.646562, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1854377709 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854377709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.647074, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-557585911 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557585911\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.650703, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2125986771 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125986771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.651193, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-312664946 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312664946\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.656426, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-6809188 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6809188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.657539, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-555209570 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555209570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.662395, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1289639267 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289639267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.663154, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-683977679 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683977679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.667912, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1879080398 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879080398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.668702, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-517116655 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517116655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.67347, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-523474543 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523474543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.674224, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1224684782 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224684782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.681254, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2024557618 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024557618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.681884, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1483478695 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483478695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.689188, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1332763447 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332763447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.69005, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1436387774 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436387774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.693719, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-180694687 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180694687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.694378, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-612605016 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612605016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.698706, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1722731487 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722731487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.6994, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1360819245 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360819245\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.704138, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-391034361 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391034361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.704638, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852584307 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852584307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.708946, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1551440529 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551440529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.709702, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1978131162 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978131162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.714776, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1932201115 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932201115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751353421.715452, "xdebug_link": null}]}, "session": {"_token": "u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j", "url": "[]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://admin.ticketgol.test/events\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2"}, "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f48dfdb-b3c2-46c1-a38e-e079f8f2edab\" target=\"_blank\">View in Telescope</a>", "path_info": "/events", "status_code": "<pre class=sf-dump id=sf-dump-721563693 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-721563693\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-711191013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-711191013\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1417613935 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1417613935\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-232166782 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1147 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; __stripe_mid=fd60eaa0-e90c-4914-bb27-ce58ed87fc6066be49; XSRF-TOKEN=eyJpdiI6IjBjZ1RKaDROZXFUbkphN2lJRXFNcGc9PSIsInZhbHVlIjoiVmx4cW9yQ0M4S3gyMGYrRVlqL3NBNDczS0NIcjc5dTBpNUl0Q0tOT0NyUmptN1ZSd3I1ZjVnTEQ2Yk5zRGZGUy9FRnJycHZWUWMvTHpBaXArS2VhNFJiN3F4TXBSUG8ydXFYc3pWdXJPbUllTS9mYWVVYWlXdVR1SDY0dGVjeGYiLCJtYWMiOiJlZjY2N2ZkNzg3NmRiZWEwNDczOWEwYzZlYmJkODMwYzViNzI4OGU4NDQyMmEzZjk1YzUwZjM4YjZhNWFmNjcwIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InI2a2FqcGQ5RmtiUXo5Ykt4ampvVHc9PSIsInZhbHVlIjoieHVQYnE1bVIwSHhlVDArcFNDd3JTR1NTd2pGaHE4L0hHY0JPeHc2OXl0aEVoT29Qb1MvVlFqc0JWQVZUdU1LSnVQYU5XUGZmbEc1bFFsQWJqNis3T1Y0YlZVWjIySnlsRm9SZTJla01nQTJhR2pEVUwzYmpmYmhUWmVxZHo3Q3YiLCJtYWMiOiJhY2Y1YjdlZTkzMjVkYWM2ZTljMzM4NGJlOWI4OTNjZTNmNTkyMGI0ZGZmMTlmMDc1YzZjYmE3OTMwZjMyOTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232166782\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1325389912 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zBFoVNd9nZvXQTjxvJWmdAmllOfb3JbvDHN2bnh7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325389912\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1811268994 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 07:03:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlF2YTlIRVlVN08zSmNsTzFkNStWaWc9PSIsInZhbHVlIjoiU1NrK3ZwZFhuL0tKb09ndnphaTNIVmxzTXREQWpGZG5XdmxMREdCRkNJajB5T1JvY25JQXRMY2tURXhXSVpCOVVKSHNORDdzb29kaFF0MDh2VDc5dk13b1doMnhLVXEvRmk5cy9VSVVPMmtzdStNNGZTZ1dyWkJ5Ujc5TmdQNTYiLCJtYWMiOiJlMTRiN2IyN2M3NDg2NzgxMjkzMzY1YWIwYjQ2NzEyYWVmZmQzZWZlZDdmMjE5ZjFkZDI5ZWI3ZTUwMDg4YmVlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:03:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Ik8vdG54TVNVYzVkeHQ1Z1YzR3B0Ync9PSIsInZhbHVlIjoiYjFWSmE1YjVQckdxMDA2eEFCTHZnelA0NTdOdHN0RDk5aDM3UG5mTlNiMldPd2YyTlhEdExNclNMQWNZNVhQa1lZeE9naDV5T1ZsUDEzSmNMWHlRMlowSU4xUTYwVlNSSnZWS3JBcW5yTk5zcDk1TGNzRitPdDl1MWxJY3poNGoiLCJtYWMiOiI0ZTQ1MmE0ZTdjYWJjZjUxZmVjODQzNzIyYmJlZWE0YTc2MGJiZjkwOGUzYmUxODRlOWNlZmYxZTkzOTg4YTNjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:03:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlF2YTlIRVlVN08zSmNsTzFkNStWaWc9PSIsInZhbHVlIjoiU1NrK3ZwZFhuL0tKb09ndnphaTNIVmxzTXREQWpGZG5XdmxMREdCRkNJajB5T1JvY25JQXRMY2tURXhXSVpCOVVKSHNORDdzb29kaFF0MDh2VDc5dk13b1doMnhLVXEvRmk5cy9VSVVPMmtzdStNNGZTZ1dyWkJ5Ujc5TmdQNTYiLCJtYWMiOiJlMTRiN2IyN2M3NDg2NzgxMjkzMzY1YWIwYjQ2NzEyYWVmZmQzZWZlZDdmMjE5ZjFkZDI5ZWI3ZTUwMDg4YmVlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:03:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Ik8vdG54TVNVYzVkeHQ1Z1YzR3B0Ync9PSIsInZhbHVlIjoiYjFWSmE1YjVQckdxMDA2eEFCTHZnelA0NTdOdHN0RDk5aDM3UG5mTlNiMldPd2YyTlhEdExNclNMQWNZNVhQa1lZeE9naDV5T1ZsUDEzSmNMWHlRMlowSU4xUTYwVlNSSnZWS3JBcW5yTk5zcDk1TGNzRitPdDl1MWxJY3poNGoiLCJtYWMiOiI0ZTQ1MmE0ZTdjYWJjZjUxZmVjODQzNzIyYmJlZWE0YTc2MGJiZjkwOGUzYmUxODRlOWNlZmYxZTkzOTg4YTNjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:03:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811268994\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1884709412 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u8F23hLLexwqQQnMacoio1jpaR5Ecit964d5lT8j</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://admin.ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$L1FA3E/ZiDsNTeNSP8cme.KvCrfdiUW.v5Qp9chlyaMdzV4DtOfB2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884709412\", {\"maxDepth\":0})</script>\n"}}